part of xr_helper;

class Validations {
  //! Password Validation
  static String? password(
    String? value, {
    String? emptyPasswordMessage = "Password can't be empty",
    String? passwordLengthMessage =
        "Password must be at least 8 characters long",
  }) {
    if (value == null || value.isEmpty) {
      return emptyPasswordMessage;
    } else if (value.length < 8) {
      return passwordLengthMessage;
    }
    return null;
  }

  //! Phone Number Validation
  static String? phoneNumber(value,
      {String? emptyPhoneMessage = "Phone number can't be empty",
      String? phoneLengthMessage = "Invalid phone number"}) {
    String pattern = r'(^(?:[0]9)?[0-9]{1,12}$)';
    RegExp regExp = RegExp(pattern);
    if (value == null || value.isEmpty) {
      return emptyPhoneMessage;
    } else if (!regExp.hasMatch(value)) {
      return phoneLengthMessage;
    }
    return null;
  }

  //! Numbers Only Validation
  static String? numbersOnly(value,
      {String? emptyMessage = "Field can't be empty",
      String? invalidMessage = "Invalid number"}) {
    String pattern = r'(^[0-9]*$)';
    RegExp regExp = RegExp(pattern);
    if (value == null || value.isEmpty) {
      return emptyMessage;
    } else if (!regExp.hasMatch(value)) {
      return invalidMessage;
    }
    return null;
  }

  //! Email Validation
  static String? email(
    String? value, {
    String? emptyEmailMessage = "Email can't be empty",
    String? invalidEmailMessage = "Invalid email",
  }) {
    final RegExp urlExp = RegExp(
        r"^[a-zA-Z0-9.a-zA-Z0-9!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+");
    if (value == null || value.isEmpty) {
      return emptyEmailMessage;
    } else if (!urlExp.hasMatch(value)) {
      return invalidEmailMessage;
    }
    return null;
  }

  //! Must Be Not Empty Validation
  static String? mustBeNotEmpty(String? value, {String? emptyMessage}) {
    if (value == null || value.isEmpty) {
      return emptyMessage ?? "Field can't be empty";
    }
  }

  //! Palestinian ID Validation
  static String? palestinianId(
    String? value, {
    String? emptyMessage = "ID number can't be empty",
    String? invalidMessage = "Invalid ID number",
  }) {
    if (value == null || value.isEmpty) {
      return emptyMessage;
    }

    if (value.length != 9 || !RegExp(r'^[0-9]+$').hasMatch(value)) {
      return invalidMessage;
    }

    int sum = 0;
    for (int i = 0; i < value.length; i++) {
      int digit = int.parse(value[i]);
      int incNum = digit * ((i % 2) + 1);
      sum += (incNum > 9) ? incNum - 9 : incNum;
    }

    if (sum % 10 != 0) {
      return invalidMessage;
    }

    return null;
  }

  //! Palestinian Phone Number Validation
  static String? palestinianPhoneNumber(
    String? value, {
    String? emptyMessage = "Phone number can't be empty",
    String? invalidMessage = "Invalid phone number",
  }) {
    if (value == null || value.isEmpty) {
      return emptyMessage;
    }

    // Palestinian phone number patterns
    List<String> patterns = [
      r'^(00972|0|\+972)[5][0-9]{8}$', // Palestinian mobile with 972 code
      r'^(00970|0|\+970)[5][0-9]{8}$', // Palestinian mobile with 970 code
      r'^(05[0-9]|0[12346789])([0-9]{7})$', // Local format
      r'^(00972|0|\+972|0|)[2][0-9]{7}$', // Landline
    ];

    bool isValid = false;
    for (String pattern in patterns) {
      if (RegExp(pattern).hasMatch(value)) {
        isValid = true;
        break;
      }
    }

    if (!isValid) {
      return invalidMessage;
    }

    return null;
  }

  //! Confirm Password Validation
  static String? confirmPassword(
    String? value,
    String? originalPassword, {
    String? emptyMessage = "Confirm password can't be empty",
    String? mismatchMessage = "Passwords do not match",
  }) {
    if (value == null || value.isEmpty) {
      return emptyMessage;
    }

    if (value != originalPassword) {
      return mismatchMessage;
    }

    return null;
  }
}
