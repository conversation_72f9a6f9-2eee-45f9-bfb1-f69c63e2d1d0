{"snapshots": {"/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/products/controllers/store.controller.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/products/controllers/store.controller.dart", "baseContent": "import 'package:dropx/src/screens/stores/models/store.model.dart';\nimport 'package:dropx/src/screens/stores/repositories/store.repository.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass StoresController extends BaseVM {\n  final StoresRepository storesRepo;\n\n  StoresController({\n    required this.storesRepo,\n  });\n\n  // * Get Stores\n  Future<List<StoreModel>> getStores() async {\n    return await baseFunction(\n      () async {\n        return await storesRepo.getStores();\n      },\n    );\n  }\n\n  // * Add Stores\n  Future<void> addStores({\n    required Map<String, dynamic> data,\n  }) async {\n    return await baseFunction(\n      () async {\n        return await storesRepo.addStores(data: data);\n      },\n    );\n  }\n}\n", "baseTimestamp": 1756912559704}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/settings.gradle": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/settings.gradle", "baseContent": "pluginManagement {\n    def flutterSdkPath = {\n        def properties = new Properties()\n        file(\"local.properties\").withInputStream { properties.load(it) }\n        def flutterSdkPath = properties.getProperty(\"flutter.sdk\")\n        assert flutterSdkPath != null, \"flutter.sdk not set in local.properties\"\n        return flutterSdkPath\n    }()\n\n    includeBuild(\"$flutterSdkPath/packages/flutter_tools/gradle\")\n\n    repositories {\n        google()\n        mavenCentral()\n        gradlePluginPortal()\n    }\n}\n\nplugins {\n    id \"dev.flutter.flutter-plugin-loader\" version \"1.0.0\"\n    id \"com.android.application\" version \"8.6.0\" apply false\n    id \"org.jetbrains.kotlin.android\" version \"2.1.0\" apply false\n}\n\ninclude \":app\"\n", "baseTimestamp": 1756912592007}, "/terminal_output": {"filePath": "/terminal_output", "baseContent": "xrgouda@Amrs-MacBook-Air dropx % cd packages\nxrgouda@Amrs-MacBook-Air packages % cd x\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "baseTimestamp": 1756912714521, "deltas": [{"timestamp": 1756912723747, "changes": [{"type": "DELETE", "lineNumber": 1, "oldContent": "xrgouda@Amrs-MacBook-Air packages % cd x"}, {"type": "DELETE", "lineNumber": 2, "oldContent": ""}, {"type": "DELETE", "lineNumber": 3, "oldContent": ""}, {"type": "DELETE", "lineNumber": 4, "oldContent": ""}, {"type": "DELETE", "lineNumber": 5, "oldContent": ""}, {"type": "DELETE", "lineNumber": 6, "oldContent": ""}, {"type": "DELETE", "lineNumber": 7, "oldContent": ""}, {"type": "DELETE", "lineNumber": 8, "oldContent": ""}, {"type": "DELETE", "lineNumber": 9, "oldContent": ""}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}, {"type": "DELETE", "lineNumber": 11, "oldContent": ""}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "DELETE", "lineNumber": 13, "oldContent": ""}, {"type": "DELETE", "lineNumber": 14, "oldContent": ""}, {"type": "DELETE", "lineNumber": 15, "oldContent": ""}, {"type": "INSERT", "lineNumber": 1, "content": "xrgouda@Amrs-MacBook-Air packages % cd xr_helper"}, {"type": "INSERT", "lineNumber": 2, "content": "xrgouda@Amrs-MacBook-Air xr_helper % flutter pub upgrade --major-versions"}, {"type": "INSERT", "lineNumber": 3, "content": "Resolving dependencies... (1.2s)"}, {"type": "INSERT", "lineNumber": 4, "content": "Changed 6 constraints in pubspec.yaml:"}, {"type": "INSERT", "lineNumber": 5, "content": "  intl: ^0.19.0 -> ^0.20.2"}, {"type": "INSERT", "lineNumber": 6, "content": "  firebase_core: ^3.6.0 -> ^4.1.0"}, {"type": "INSERT", "lineNumber": 7, "content": "  firebase_messaging: ^15.1.3 -> ^16.0.1"}, {"type": "INSERT", "lineNumber": 8, "content": "  flutter_hooks: ^0.20.3 -> ^0.21.3+1"}, {"type": "INSERT", "lineNumber": 9, "content": "  flutter_form_builder: ^9.3.0 -> ^10.2.0"}, {"type": "INSERT", "lineNumber": 10, "content": "  flutter_lints: ^3.0.1 -> ^6.0.0"}, {"type": "INSERT", "lineNumber": 11, "content": "Resolving dependencies... "}, {"type": "INSERT", "lineNumber": 12, "content": "Downloading packages... (1.1s)"}, {"type": "INSERT", "lineNumber": 13, "content": "> _flutterfire_internals 1.3.61 (was 1.3.59)"}, {"type": "INSERT", "lineNumber": 14, "content": "  characters 1.4.0 (1.4.1 available)"}, {"type": "INSERT", "lineNumber": 15, "content": "> firebase_core 4.1.0 (was 3.15.2)"}, {"type": "INSERT", "lineNumber": 16, "content": "> firebase_core_web 3.1.0 (was 2.24.1)"}, {"type": "INSERT", "lineNumber": 17, "content": "> firebase_messaging 16.0.1 (was 15.2.10)"}, {"type": "INSERT", "lineNumber": 18, "content": "> firebase_messaging_platform_interface 4.7.1 (was 4.6.10)"}, {"type": "INSERT", "lineNumber": 19, "content": "> firebase_messaging_web 4.0.1 (was 3.10.10)"}, {"type": "INSERT", "lineNumber": 20, "content": "> flutter_form_builder 10.2.0 (was 9.7.0)"}, {"type": "INSERT", "lineNumber": 21, "content": "> flutter_hooks 0.21.3+1 (was 0.20.5)"}, {"type": "INSERT", "lineNumber": 22, "content": "> flutter_lints 6.0.0 (was 3.0.2)"}, {"type": "INSERT", "lineNumber": 23, "content": "> intl 0.20.2 (was 0.19.0)"}, {"type": "INSERT", "lineNumber": 24, "content": "> lints 6.0.0 (was 3.0.0)"}, {"type": "INSERT", "lineNumber": 25, "content": "  material_color_utilities 0.11.1 (0.13.0 available)"}, {"type": "INSERT", "lineNumber": 26, "content": "  meta 1.16.0 (1.17.0 available)"}, {"type": "INSERT", "lineNumber": 27, "content": "  test_api 0.7.6 (0.7.7 available)"}, {"type": "INSERT", "lineNumber": 28, "content": "Changed 11 dependencies!"}, {"type": "INSERT", "lineNumber": 29, "content": "4 packages have newer versions incompatible with dependency constraints."}, {"type": "INSERT", "lineNumber": 30, "content": "Try `flutter pub outdated` for more information."}, {"type": "INSERT", "lineNumber": 31, "content": "xrgouda@Amrs-MacBook-Air xr_helper % "}]}, {"timestamp": 1756912736827, "changes": [{"type": "DELETE", "lineNumber": 17, "oldContent": "xrgouda@Amrs-MacBook-Air xr_helper % "}, {"type": "DELETE", "lineNumber": 19, "oldContent": "Try `flutter pub outdated` for more information."}, {"type": "DELETE", "lineNumber": 21, "oldContent": "4 packages have newer versions incompatible with dependency constraints."}, {"type": "DELETE", "lineNumber": 23, "oldContent": "Changed 11 dependencies!"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "  test_api 0.7.6 (0.7.7 available)"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  meta 1.16.0 (1.17.0 available)"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "  material_color_utilities 0.11.1 (0.13.0 available)"}, {"type": "INSERT", "lineNumber": 25, "content": "  material_color_utilities 0.11.1 (0.13.0 available)"}, {"type": "INSERT", "lineNumber": 26, "content": "  meta 1.16.0 (1.17.0 available)"}, {"type": "INSERT", "lineNumber": 27, "content": "  test_api 0.7.6 (0.7.7 available)"}, {"type": "INSERT", "lineNumber": 28, "content": "Changed 11 dependencies!"}, {"type": "INSERT", "lineNumber": 29, "content": "4 packages have newer versions incompatible with dependency constraints."}, {"type": "INSERT", "lineNumber": 30, "content": "Try `flutter pub outdated` for more information."}, {"type": "INSERT", "lineNumber": 31, "content": "xrgouda@Amrs-MacBook-Air xr_helper % cd xr_helper                        "}]}, {"timestamp": 1756912742841, "changes": [{"type": "INSERT", "lineNumber": 21, "content": "> flutter_hooks 0.21.3+1 (was 0.20.5)"}, {"type": "INSERT", "lineNumber": 22, "content": "> flutter_lints 6.0.0 (was 3.0.2)"}, {"type": "INSERT", "lineNumber": 23, "content": "> intl 0.20.2 (was 0.19.0)"}, {"type": "INSERT", "lineNumber": 24, "content": "> lints 6.0.0 (was 3.0.0)"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "> flutter_hooks 0.21.3+1 (was 0.20.5)"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "> flutter_lints 6.0.0 (was 3.0.2)"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "> intl 0.20.2 (was 0.19.0)"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "xrgouda@Amrs-MacBook-Air xr_helper % cd xr_helper                        "}, {"type": "MODIFY", "lineNumber": 31, "content": "xrgouda@Amrs-MacBook-Air xr_helper % cd ..                               ", "oldContent": "> lints 6.0.0 (was 3.0.0)"}, {"type": "INSERT", "lineNumber": 32, "content": "xrgouda@Amrs-MacBook-Air packages % flutter pub upgrade --major-versions"}]}, {"timestamp": 1756912796516, "changes": [{"type": "DELETE", "lineNumber": 22, "oldContent": "  material_color_utilities 0.11.1 (0.13.0 available)"}, {"type": "MODIFY", "lineNumber": 25, "content": "  material_color_utilities 0.11.1 (0.13.0 available)", "oldContent": "  meta 1.16.0 (1.17.0 available)"}, {"type": "INSERT", "lineNumber": 26, "content": "  meta 1.16.0 (1.17.0 available)"}, {"type": "INSERT", "lineNumber": 33, "content": "Resolving dependencies... (2.6s)"}, {"type": "INSERT", "lineNumber": 34, "content": "Changed 7 constraints in pubspec.yaml:"}, {"type": "INSERT", "lineNumber": 35, "content": "  flutter_hooks: ^0.20.4 -> ^0.21.3+1"}, {"type": "INSERT", "lineNumber": 36, "content": "  flutter_form_builder: ^9.4.1 -> ^10.2.0"}, {"type": "INSERT", "lineNumber": 37, "content": "  permission_handler: ^11.3.1 -> ^12.0.1"}, {"type": "INSERT", "lineNumber": 38, "content": "  geolocator: ^13.0.1 -> ^14.0.2"}, {"type": "INSERT", "lineNumber": 39, "content": "  firebase_core: ^3.6.0 -> ^4.1.0"}, {"type": "INSERT", "lineNumber": 40, "content": "  flutter_lints: ^4.0.0 -> ^6.0.0"}, {"type": "INSERT", "lineNumber": 41, "content": "  flutter_launcher_icons: ^0.13.1 -> ^0.14.4"}, {"type": "INSERT", "lineNumber": 42, "content": "Resolving dependencies in `/Users/<USER>/Flutter-Projects/Ajory/dropx`... "}, {"type": "INSERT", "lineNumber": 43, "content": "Downloading packages... (31.4s)"}, {"type": "INSERT", "lineNumber": 44, "content": "> _fe_analyzer_shared 88.0.0 (was 72.0.0)"}, {"type": "INSERT", "lineNumber": 45, "content": "> _flutterfire_internals 1.3.61 (was 1.3.44)"}, {"type": "INSERT", "lineNumber": 46, "content": "> analyzer 8.1.1 (was 6.7.0)"}, {"type": "INSERT", "lineNumber": 47, "content": "> another_flushbar 1.12.31 (was 1.12.30)"}, {"type": "INSERT", "lineNumber": 48, "content": "> any_link_preview 3.0.3 (was 3.0.2)"}, {"type": "INSERT", "lineNumber": 49, "content": "! archive 3.6.1 (overridden) (4.0.7 available)"}, {"type": "INSERT", "lineNumber": 50, "content": "> args 2.7.0 (was 2.6.0)"}, {"type": "INSERT", "lineNumber": 51, "content": "> async 2.13.0 (was 2.11.0)"}, {"type": "INSERT", "lineNumber": 52, "content": "> audio_waveforms 1.3.0 (was 1.2.0)"}, {"type": "INSERT", "lineNumber": 53, "content": "> boolean_selector 2.1.2 (was 2.1.1)"}, {"type": "INSERT", "lineNumber": 54, "content": "> build 3.1.0 (was 2.4.1) (4.0.0 available)"}, {"type": "INSERT", "lineNumber": 55, "content": "> build_config 1.2.0 (was 1.1.1)"}, {"type": "INSERT", "lineNumber": 56, "content": "> build_daemon 4.0.4 (was 4.0.2)"}, {"type": "INSERT", "lineNumber": 57, "content": "> build_resolvers 3.0.3 (was 2.4.2) (3.0.4 available)"}, {"type": "INSERT", "lineNumber": 58, "content": "> build_runner 2.7.1 (was 2.4.13) (2.7.2 available)"}, {"type": "INSERT", "lineNumber": 59, "content": "> build_runner_core 9.3.1 (was 7.3.2) (9.3.2 available)"}, {"type": "INSERT", "lineNumber": 60, "content": "> built_value 8.11.1 (was 8.9.2)"}, {"type": "INSERT", "lineNumber": 61, "content": "> carousel_slider 5.1.1 (was 5.0.0)"}, {"type": "INSERT", "lineNumber": 62, "content": "> characters 1.4.0 (was 1.3.0) (1.4.1 available)"}, {"type": "INSERT", "lineNumber": 63, "content": "> chatview 2.5.0 (was 2.3.0)"}, {"type": "INSERT", "lineNumber": 64, "content": "+ chatview_utils 0.0.1"}, {"type": "INSERT", "lineNumber": 65, "content": "> checked_yaml 2.0.4 (was 2.0.3)"}, {"type": "INSERT", "lineNumber": 66, "content": "> cli_util 0.4.2 (was 0.4.1)"}, {"type": "INSERT", "lineNumber": 67, "content": "> clock 1.1.2 (was 1.1.1)"}, {"type": "INSERT", "lineNumber": 68, "content": "> code_builder 4.10.1 (was 4.10.0)"}, {"type": "INSERT", "lineNumber": 69, "content": "> collection 1.19.1 (was 1.18.0)"}, {"type": "INSERT", "lineNumber": 70, "content": "> convert 3.1.2 (was 3.1.1)"}, {"type": "INSERT", "lineNumber": 71, "content": "> crypto 3.0.6 (was 3.0.5)"}, {"type": "INSERT", "lineNumber": 72, "content": "> csslib 1.0.2 (was 1.0.0)"}, {"type": "INSERT", "lineNumber": 73, "content": "> dart_style 3.1.2 (was 2.3.7)"}, {"type": "INSERT", "lineNumber": 74, "content": "+ dbus 0.7.11"}, {"type": "INSERT", "lineNumber": 75, "content": "> emoji_picker_flutter 4.3.0 (was 3.1.0)"}, {"type": "INSERT", "lineNumber": 76, "content": "> equatable 2.0.7 (was 2.0.5)"}, {"type": "INSERT", "lineNumber": 77, "content": "> fake_async 1.3.3 (was 1.3.1)"}, {"type": "INSERT", "lineNumber": 78, "content": "> ffi 2.1.4 (was 2.1.3)"}, {"type": "INSERT", "lineNumber": 79, "content": "> file_selector_linux 0.9.3+2 (was 0.9.3)"}, {"type": "INSERT", "lineNumber": 80, "content": "> file_selector_macos 0.9.4+4 (was 0.9.4+2)"}, {"type": "INSERT", "lineNumber": 81, "content": "> file_selector_windows 0.9.3+4 (was 0.9.3+3)"}, {"type": "INSERT", "lineNumber": 82, "content": "> firebase_core 4.1.0 (was 3.6.0)"}, {"type": "INSERT", "lineNumber": 83, "content": "> firebase_core_platform_interface 6.0.0 (was 5.3.0)"}, {"type": "INSERT", "lineNumber": 84, "content": "> firebase_core_web 3.1.0 (was 2.18.1)"}, {"type": "INSERT", "lineNumber": 85, "content": "> firebase_messaging 16.0.1 (was 15.1.3)"}, {"type": "INSERT", "lineNumber": 86, "content": "> firebase_messaging_platform_interface 4.7.1 (was 4.5.46)"}, {"type": "INSERT", "lineNumber": 87, "content": "> firebase_messaging_web 4.0.1 (was 3.9.2)"}, {"type": "INSERT", "lineNumber": 88, "content": "> fixnum 1.1.1 (was 1.1.0)"}, {"type": "INSERT", "lineNumber": 89, "content": "> flutter_form_builder 10.2.0 (was 9.5.0)"}, {"type": "INSERT", "lineNumber": 90, "content": "> flutter_gen_core 5.11.0 (was 5.8.0)"}, {"type": "INSERT", "lineNumber": 91, "content": "> flutter_gen_runner 5.11.0 (was 5.8.0)"}, {"type": "INSERT", "lineNumber": 92, "content": "> flutter_hooks 0.21.3+1 (was 0.20.5)"}, {"type": "INSERT", "lineNumber": 93, "content": "> flutter_launcher_icons 0.14.4 (was 0.13.1)"}, {"type": "INSERT", "lineNumber": 94, "content": "> flutter_lints 6.0.0 (was 4.0.0)"}, {"type": "INSERT", "lineNumber": 95, "content": "> flutter_native_splash 2.4.6 (was 2.4.1)"}, {"type": "INSERT", "lineNumber": 96, "content": "> flutter_plugin_android_lifecycle 2.0.30 (was 2.0.23)"}, {"type": "INSERT", "lineNumber": 97, "content": "> flutter_riverpod 2.6.1 (was 2.5.3)"}, {"type": "INSERT", "lineNumber": 98, "content": "> flutter_svg 2.2.0 (was 2.0.10+1)"}, {"type": "INSERT", "lineNumber": 99, "content": "> fluttertoast 8.2.12 (was 8.2.8)"}, {"type": "INSERT", "lineNumber": 100, "content": "> font_awesome_flutter 10.10.0 (was 10.7.0)"}, {"type": "INSERT", "lineNumber": 101, "content": "> form_builder_image_picker 4.3.1 (was 4.1.0)"}, {"type": "INSERT", "lineNumber": 102, "content": "> form_builder_validators 11.2.0 (was 11.0.0)"}, {"type": "INSERT", "lineNumber": 103, "content": "+ geoclue 0.1.1"}, {"type": "INSERT", "lineNumber": 104, "content": "> geolocator 14.0.2 (was 13.0.1)"}, {"type": "INSERT", "lineNumber": 105, "content": "> geolocator_android 5.0.2 (was 4.6.1)"}, {"type": "INSERT", "lineNumber": 106, "content": "> geolocator_apple 2.3.13 (was 2.3.7)"}, {"type": "INSERT", "lineNumber": 107, "content": "+ geolocator_linux 0.2.3"}, {"type": "INSERT", "lineNumber": 108, "content": "> geolocator_platform_interface 4.2.6 (was 4.2.4)"}, {"type": "INSERT", "lineNumber": 109, "content": "> geolocator_web 4.1.3 (was 4.1.1)"}, {"type": "INSERT", "lineNumber": 110, "content": "> geolocator_windows 0.2.5 (was 0.2.3)"}, {"type": "INSERT", "lineNumber": 111, "content": "> get 4.7.2 (was 4.6.6)"}, {"type": "INSERT", "lineNumber": 112, "content": "> glob 2.1.3 (was 2.1.2)"}, {"type": "INSERT", "lineNumber": 113, "content": "> google_fonts 6.3.1 (was 6.2.1)"}, {"type": "INSERT", "lineNumber": 114, "content": "> google_maps_flutter 2.13.1 (was 2.9.0)"}, {"type": "INSERT", "lineNumber": 115, "content": "> google_maps_flutter_android 2.18.2 (was 2.14.10)"}, {"type": "INSERT", "lineNumber": 116, "content": "> google_maps_flutter_ios 2.15.5 (was 2.13.1)"}, {"type": "INSERT", "lineNumber": 117, "content": "> google_maps_flutter_platform_interface 2.14.0 (was 2.9.5)"}, {"type": "INSERT", "lineNumber": 118, "content": "> google_maps_flutter_web 0.5.14 (was 0.5.10)"}, {"type": "INSERT", "lineNumber": 119, "content": "+ gsettings 0.2.8"}, {"type": "INSERT", "lineNumber": 120, "content": "> hooks_riverpod 2.6.1 (was 2.5.4)"}, {"type": "INSERT", "lineNumber": 121, "content": "> html 0.15.6 (was 0.15.4)"}, {"type": "INSERT", "lineNumber": 122, "content": "> http 1.5.0 (was 1.2.2)"}, {"type": "INSERT", "lineNumber": 123, "content": "> http_multi_server 3.2.2 (was 3.2.1)"}, {"type": "INSERT", "lineNumber": 124, "content": "> http_parser 4.1.2 (was 4.0.2)"}, {"type": "INSERT", "lineNumber": 125, "content": "> image 4.5.4 (was 4.2.0)"}, {"type": "INSERT", "lineNumber": 126, "content": "> image_picker 1.2.0 (was 1.1.2)"}, {"type": "INSERT", "lineNumber": 127, "content": "> image_picker_android 0.8.13+1 (was 0.8.12+17)"}, {"type": "INSERT", "lineNumber": 128, "content": "> image_picker_for_web 3.1.0 (was 3.0.6)"}, {"type": "INSERT", "lineNumber": 129, "content": "> image_picker_ios 0.8.13 (was 0.8.12+1)"}, {"type": "INSERT", "lineNumber": 130, "content": "> image_picker_linux 0.2.2 (was 0.2.1+1)"}, {"type": "INSERT", "lineNumber": 131, "content": "> image_picker_macos 0.2.2 (was 0.2.1+1)"}, {"type": "INSERT", "lineNumber": 132, "content": "> image_picker_platform_interface 2.11.0 (was 2.10.0)"}, {"type": "INSERT", "lineNumber": 133, "content": "> image_picker_windows 0.2.2 (was 0.2.1+1)"}, {"type": "INSERT", "lineNumber": 134, "content": "> image_size_getter 2.4.1 (was 2.2.0)"}, {"type": "INSERT", "lineNumber": 135, "content": "> intl 0.20.2 (was 0.19.0)"}, {"type": "INSERT", "lineNumber": 136, "content": "> io 1.0.5 (was 1.0.4)"}, {"type": "INSERT", "lineNumber": 137, "content": "> leak_tracker 11.0.1 (was 10.0.5)"}, {"type": "INSERT", "lineNumber": 138, "content": "> leak_tracker_flutter_testing 3.0.10 (was 3.0.5)"}, {"type": "INSERT", "lineNumber": 139, "content": "> leak_tracker_testing 3.0.2 (was 3.0.1)"}, {"type": "INSERT", "lineNumber": 140, "content": "> lints 6.0.0 (was 4.0.0)"}, {"type": "INSERT", "lineNumber": 141, "content": "> logger 2.6.1 (was 2.4.0)"}, {"type": "INSERT", "lineNumber": 142, "content": "> logging 1.3.0 (was 1.2.0)"}, {"type": "INSERT", "lineNumber": 143, "content": "> lottie 3.3.1 (was 3.1.3)"}, {"type": "INSERT", "lineNumber": 144, "content": "> matcher 0.12.17 (was 0.12.16+1)"}, {"type": "INSERT", "lineNumber": 145, "content": "  material_color_utilities 0.11.1 (0.13.0 available)"}, {"type": "INSERT", "lineNumber": 146, "content": "> meta 1.16.0 (was 1.15.0) (1.17.0 available)"}, {"type": "INSERT", "lineNumber": 147, "content": "> package_config 2.2.0 (was 2.1.0)"}, {"type": "INSERT", "lineNumber": 148, "content": "+ package_info_plus 8.3.1"}, {"type": "INSERT", "lineNumber": 149, "content": "+ package_info_plus_platform_interface 3.2.1"}, {"type": "INSERT", "lineNumber": 150, "content": "> path 1.9.1 (was 1.9.0)"}, {"type": "INSERT", "lineNumber": 151, "content": "> path_parsing 1.1.0 (was 1.0.1)"}, {"type": "INSERT", "lineNumber": 152, "content": "> path_provider 2.1.5 (was 2.1.4)"}, {"type": "INSERT", "lineNumber": 153, "content": "> path_provider_android 2.2.18 (was 2.2.12)"}, {"type": "INSERT", "lineNumber": 154, "content": "> path_provider_foundation 2.4.2 (was 2.4.0)"}, {"type": "INSERT", "lineNumber": 155, "content": "> permission_handler 12.0.1 (was 11.3.1)"}, {"type": "INSERT", "lineNumber": 156, "content": "> permission_handler_android 13.0.1 (was 12.0.13)"}, {"type": "INSERT", "lineNumber": 157, "content": "> permission_handler_apple 9.4.7 (was 9.4.5)"}, {"type": "INSERT", "lineNumber": 158, "content": "> permission_handler_html 0.1.3+5 (was 0.1.3+2)"}, {"type": "INSERT", "lineNumber": 159, "content": "> permission_handler_platform_interface 4.3.0 (was 4.2.3)"}, {"type": "INSERT", "lineNumber": 160, "content": "> petitparser 7.0.1 (was 6.0.2)"}, {"type": "INSERT", "lineNumber": 161, "content": "> platform 3.1.6 (was 3.1.5)"}, {"type": "INSERT", "lineNumber": 162, "content": "> provider 6.1.5+1 (was 6.1.2)"}, {"type": "INSERT", "lineNumber": 163, "content": "> pub_semver 2.2.0 (was 2.1.4)"}, {"type": "INSERT", "lineNumber": 164, "content": "> pubspec_parse 1.5.0 (was 1.3.0)"}, {"type": "INSERT", "lineNumber": 165, "content": "> riverpod 2.6.1 (was 2.5.3)"}, {"type": "INSERT", "lineNumber": 166, "content": "> shared_preferences 2.5.3 (was 2.3.3)"}, {"type": "INSERT", "lineNumber": 167, "content": "> shared_preferences_android 2.4.12 (was 2.3.4)"}, {"type": "INSERT", "lineNumber": 168, "content": "> shared_preferences_foundation 2.5.4 (was 2.5.3)"}, {"type": "INSERT", "lineNumber": 169, "content": "> shared_preferences_web 2.4.3 (was 2.4.2)"}, {"type": "INSERT", "lineNumber": 170, "content": "> shelf 1.4.2 (was 1.4.1)"}, {"type": "INSERT", "lineNumber": 171, "content": "> shelf_web_socket 3.0.0 (was 2.0.0)"}, {"type": "INSERT", "lineNumber": 172, "content": "< sky_engine 0.0.0 from sdk flutter (was 0.0.99 from sdk flutter)"}, {"type": "INSERT", "lineNumber": 173, "content": "> smooth_page_indicator 1.2.1 (was 1.2.0+3)"}, {"type": "INSERT", "lineNumber": 174, "content": "> source_span 1.10.1 (was 1.10.0)"}, {"type": "INSERT", "lineNumber": 175, "content": "> sqflite 2.4.2 (was 2.4.0)"}, {"type": "INSERT", "lineNumber": 176, "content": "> sqflite_android 2.4.2+2 (was 2.4.0)"}, {"type": "INSERT", "lineNumber": 177, "content": "> sqflite_common 2.5.6 (was 2.5.4+5)"}, {"type": "INSERT", "lineNumber": 178, "content": "> sqflite_darwin 2.4.2 (was 2.4.1-1)"}, {"type": "INSERT", "lineNumber": 179, "content": "> stack_trace 1.12.1 (was 1.11.1)"}, {"type": "INSERT", "lineNumber": 180, "content": "> stream_channel 2.1.4 (was 2.1.2)"}, {"type": "INSERT", "lineNumber": 181, "content": "> stream_transform 2.1.1 (was 2.1.0)"}, {"type": "INSERT", "lineNumber": 182, "content": "> string_scanner 1.4.1 (was 1.2.0)"}, {"type": "INSERT", "lineNumber": 183, "content": "> string_validator 1.2.0 (was 1.1.0)"}, {"type": "INSERT", "lineNumber": 184, "content": "> synchronized 3.4.0 (was 3.3.0+3)"}, {"type": "INSERT", "lineNumber": 185, "content": "> term_glyph 1.2.2 (was 1.2.1)"}, {"type": "INSERT", "lineNumber": 186, "content": "> test_api 0.7.6 (was 0.7.2) (0.7.7 available)"}, {"type": "INSERT", "lineNumber": 187, "content": "> time 2.1.5 (was 2.1.4)"}, {"type": "INSERT", "lineNumber": 188, "content": "> timing 1.0.2 (was 1.0.1)"}, {"type": "INSERT", "lineNumber": 189, "content": "> typed_data 1.4.0 (was 1.3.2)"}, {"type": "INSERT", "lineNumber": 190, "content": "> url_launcher 6.3.2 (was 6.3.1)"}, {"type": "INSERT", "lineNumber": 191, "content": "> url_launcher_android 6.3.18 (was 6.3.12)"}, {"type": "INSERT", "lineNumber": 192, "content": "> url_launcher_ios 6.3.4 (was 6.3.1)"}, {"type": "INSERT", "lineNumber": 193, "content": "> url_launcher_linux 3.2.1 (was 3.2.0)"}, {"type": "INSERT", "lineNumber": 194, "content": "> url_launcher_macos 3.2.3 (was 3.2.1)"}, {"type": "INSERT", "lineNumber": 195, "content": "> url_launcher_web 2.4.1 (was 2.3.3)"}, {"type": "INSERT", "lineNumber": 196, "content": "> url_launcher_windows 3.1.4 (was 3.1.2)"}, {"type": "INSERT", "lineNumber": 197, "content": "> vector_graphics 1.1.19 (was 1.1.11+1)"}, {"type": "INSERT", "lineNumber": 198, "content": "> vector_graphics_codec 1.1.13 (was 1.1.11+1)"}, {"type": "INSERT", "lineNumber": 199, "content": "> vector_graphics_compiler 1.1.19 (was 1.1.11+1)"}, {"type": "INSERT", "lineNumber": 200, "content": "> vector_math 2.2.0 (was 2.1.4)"}, {"type": "INSERT", "lineNumber": 201, "content": "> video_player 2.10.0 (was 2.9.2)"}, {"type": "INSERT", "lineNumber": 202, "content": "> video_player_android 2.8.13 (was 2.7.13)"}, {"type": "INSERT", "lineNumber": 203, "content": "> video_player_avfoundation 2.8.4 (was 2.6.2)"}, {"type": "INSERT", "lineNumber": 204, "content": "> video_player_platform_interface 6.4.0 (was 6.2.3)"}, {"type": "INSERT", "lineNumber": 205, "content": "> video_player_web 2.4.0 (was 2.3.2)"}, {"type": "INSERT", "lineNumber": 206, "content": "> vm_service 15.0.2 (was 14.2.5)"}, {"type": "INSERT", "lineNumber": 207, "content": "> watcher 1.1.3 (was 1.1.0)"}, {"type": "INSERT", "lineNumber": 208, "content": "> web 1.1.1 (was 1.1.0)"}, {"type": "INSERT", "lineNumber": 209, "content": "> web_socket 1.0.1 (was 0.1.6)"}, {"type": "INSERT", "lineNumber": 210, "content": "> web_socket_channel 3.0.3 (was 3.0.1)"}, {"type": "INSERT", "lineNumber": 211, "content": "! win32 5.14.0 (overridden)"}, {"type": "INSERT", "lineNumber": 212, "content": "> xml 6.6.1 (was 6.5.0)"}, {"type": "INSERT", "lineNumber": 213, "content": "> yaml 3.1.3 (was 3.1.2)"}, {"type": "INSERT", "lineNumber": 214, "content": "These packages are no longer being depended on:"}, {"type": "INSERT", "lineNumber": 215, "content": "- _macros 0.3.2 from sdk dart"}, {"type": "INSERT", "lineNumber": 216, "content": "- js 0.7.1"}, {"type": "INSERT", "lineNumber": 217, "content": "- macros 0.1.2-main.4"}, {"type": "INSERT", "lineNumber": 218, "content": "- timeago 3.7.0"}, {"type": "INSERT", "lineNumber": 219, "content": "Changed 171 dependencies in `/Users/<USER>/Flutter-Projects/Ajory/dropx`!"}, {"type": "INSERT", "lineNumber": 220, "content": "9 packages have newer versions incompatible with dependency constraints."}, {"type": "INSERT", "lineNumber": 221, "content": "Try `flutter pub outdated` for more information."}, {"type": "INSERT", "lineNumber": 222, "content": "xrgouda@Amrs-MacBook-Air packages % "}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/packages/xr_helper/pubspec.yaml": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/packages/xr_helper/pubspec.yaml", "baseContent": "name: xr_helper\ndescription: \"A new Flutter package project.\"\nversion: 0.0.1\nhomepage:\n\nenvironment:\n  sdk: '>=3.2.0 <4.0.0'\n  flutter: \">=1.17.0\"\n\ndependencies:\n  flutter:\n    sdk: flutter\n\n  cupertino_icons: ^1.0.6\n\n  #? URL Launcher (Make calls, send SMS, open URLs)\n  url_launcher: ^6.2.1\n\n  #? Local Storage\n  get_storage: ^2.1.1\n\n  #? Remote (HTTP Requests)\n  http: ^1.1.2\n\n  #? Sized Box (Height & Width)\n  gap: ^3.0.1\n\n  #? Intl (Date Formatting)\n  intl: ^0.20.2\n\n  #? Theme (Fonts)\n  google_fonts: ^6.1.0\n\n  #? Alerts\n  another_flushbar: ^1.12.30\n  fluttertoast: ^8.2.8\n\n  #? Firebase\n  firebase_core: ^4.1.0\n  firebase_messaging: ^16.0.1\n\n  #? Hooks\n  flutter_hooks: ^0.21.3+1\n\n  #? UI\n  cached_network_image:\n  shimmer: ^3.0.0\n  flutter_form_builder: ^10.2.0\n\n  #? Logger (Log messages with colors)\n  logger: ^2.0.2+1\n\n  #? State Management\n  provider: ^6.1.1\n\n\ndev_dependencies:\n  flutter_test:\n    sdk: flutter\n  flutter_lints: ^6.0.0\n\nflutter:\n", "baseTimestamp": 1756912724040}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/pubspec.yaml": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/pubspec.yaml", "baseContent": "name: dropx\ndescription: \"Kingdom Pets Application.\"\n\npublish_to: 'none'\n\nscripts:\n  build_runner: dart run build_runner build --delete-conflicting-outputs\n  launch_icons: flutter pub run flutter_launcher_icons:main\n  launch_splash: flutter pub run flutter_native_splash:create\n\nversion: 1.0.0+0\n\nenvironment:\n  sdk: '>=3.0.6 <4.0.0'\n\ndependencies:\n  flutter:\n    sdk: flutter\n\n  #? Localization\n  flutter_localizations:\n    sdk: flutter\n\n  # * Helper Package *\n  xr_helper:\n    path: packages/xr_helper\n\n  cupertino_icons: ^1.0.6\n\n  #? State Management\n  flutter_riverpod: ^2.4.9\n  riverpod: ^2.4.9\n  hooks_riverpod: ^2.4.9\n  flutter_hooks: ^0.21.3+1\n  equatable:\n\n  #? responsive\n  flutter_screenutil: ^5.9.3\n\n\n  #? Google Fonts\n  google_fonts:\n\n  #? Assets\n  lottie: ^3.0.0\n  flutter_svg:\n  smooth_page_indicator: ^1.2.0+3\n\n  #? form Builder\n  flutter_form_builder: ^10.2.0\n  form_builder_image_picker: ^4.1.0\n  form_builder_validators: ^11.0.0\n\n\n  #? Utils\n  no_context_navigation: ^3.0.0\n  fluttertoast: ^8.2.8\n  permission_handler: ^12.0.1\n\n  #? Location\n  google_maps_flutter: ^2.9.0\n  geolocator: ^14.0.2\n\n  #? Firebase\n  firebase_core: ^4.1.0\n\n  #? UI\n  loading_animation_widget: ^1.3.0\n  carousel_slider: ^5.0.0\n  multi_video_player: ^0.0.5\n  font_awesome_flutter: ^10.7.0\n  flutter_staggered_grid_view: ^0.7.0\n  chatview: ^2.3.0\n\ndependency_overrides:\n  archive: ^3.6.1\n  win32: ^5.5.4\n\n\ndev_dependencies:\n  flutter_test:\n    sdk: flutter\n\n  flutter_lints: ^6.0.0\n  flutter_launcher_icons: ^0.14.4\n  flutter_native_splash: ^2.3.9\n  build_runner:\n  flutter_gen_runner:\n\n#? dart run flutter_launcher_icons:main\nflutter_launcher_icons:\n  android: true\n  ios: true\n  remove_alpha_ios: true\n  image_path: \"assets/images/app_icon.png\"\n  adaptive_icon_background: \"assets/images/app_icon.png\"\n  adaptive_icon_foreground: \"assets/images/app_icon.png\"\n  adaptive_icon_foreground_inset: 16\n\n# ? dart run flutter_native_splash:create\nflutter_native_splash:\n  android: true\n  ios: true\n  web: false\n  fullscreen: false\n  color: '#ffffff'\n  image: 'assets/images/app_icon.png'\n  android_12:\n    color: '#ffffff'\n    image: 'assets/images/app_icon.png'\n\nflutter:\n  uses-material-design: true\n  generate: true\n\n  assets:\n    - assets/images/\n#    - assets/animated/\n#    - assets/icons/\n\nflutter_intl:\n  enabled: true\n\n\nflutter_gen:\n  output: lib/generated", "baseTimestamp": 1756912796821}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/app/src/main/AndroidManifest.xml": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/app/src/main/AndroidManifest.xml", "baseContent": "<manifest xmlns:android=\"http://schemas.android.com/apk/res/android\">\n\n    <uses-permission android:name=\"android.permission.INTERNET\"/>\n    <uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>\n    <uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>\n\n    <application\n            android:label=\"DropX\"\n            android:name=\"${applicationName}\"\n            android:icon=\"@mipmap/ic_launcher\">\n\n\n        <meta-data\n                android:name=\"com.google.android.geo.API_KEY\"\n                android:value=\"AIzaSyCTUQH9tBTBxjAJSpDmDEVllVhWqmR0nR8\"/>\n\n        <activity\n                android:name=\".MainActivity\"\n                android:exported=\"true\"\n                android:launchMode=\"singleTop\"\n                android:taskAffinity=\"\"\n                android:theme=\"@style/LaunchTheme\"\n                android:configChanges=\"orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode\"\n                android:hardwareAccelerated=\"true\"\n                android:windowSoftInputMode=\"adjustResize\">\n\n\n            <!-- Specifies an Android theme to apply to this Activity as soon as\n                 the Android process has started. This theme is visible to the user\n                 while the Flutter UI initializes. After that, this theme continues\n                 to determine the Window background behind the Flutter UI. -->\n            <meta-data\n                    android:name=\"io.flutter.embedding.android.NormalTheme\"\n                    android:resource=\"@style/NormalTheme\"\n            />\n            <intent-filter>\n                <action android:name=\"android.intent.action.MAIN\"/>\n                <category android:name=\"android.intent.category.LAUNCHER\"/>\n            </intent-filter>\n        </activity>\n        <!-- Don't delete the meta-data below.\n             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->\n        <meta-data\n                android:name=\"flutterEmbedding\"\n                android:value=\"2\"/>\n    </application>\n    <!-- Required to query activities that can process text, see:\n         https://developer.android.com/training/package-visibility and\n         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.\n\n         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->\n    <queries>\n        <intent>\n            <action android:name=\"android.intent.action.PROCESS_TEXT\"/>\n            <data android:mimeType=\"text/plain\"/>\n        </intent>\n    </queries>\n</manifest>\n", "baseTimestamp": 1756913020326}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/consts/app_constants.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/consts/app_constants.dart", "baseContent": "import 'package:flutter/material.dart' show Locale, LocalizationsDelegate;\nimport 'package:flutter_localizations/flutter_localizations.dart';\nimport 'package:dropx/generated/l10n.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass AppConsts {\n  static const String appName = 'DropX';\n  static const Locale locale = Locale('en');\n\n  static const List<Locale> supportedLocales = [\n    locale,\n    Locale('ar'),\n  ];\n\n  static bool get isEnglish =>\n      GetStorageService.getData(key: LocalKeys.language) == 'en';\n\n  static const List<LocalizationsDelegate> localizationsDelegates = [\n    S.delegate,\n    GlobalMaterialLocalizations.delegate,\n    GlobalCupertinoLocalizations.delegate,\n    GlobalWidgetsLocalizations.delegate,\n  ];\n\n  //? Test Login\n  static const String testEmail = 'admin';\n  static const String testPass = 'test@123';\n}\n", "baseTimestamp": 1756913096385}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/widgets/doctors_list.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/widgets/doctors_list.widget.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport 'see_all_button.widget.dart';\n\nclass DoctorsListWidget extends StatelessWidget {\n  const DoctorsListWidget({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    return Column(\n      children: [\n        Row(\n          children: [\n            Text(context.tr.doctors, style: AppTextStyles.title),\n            const Spacer(),\n            SeeAllButtonWidget(\n              onPressed: () {\n                const DoctorsScreen().navigate;\n              },\n            )\n          ],\n        ),\n        AppGaps.gap8,\n        SizedBox(\n          height: 110.h,\n          child: ListView.separated(\n              scrollDirection: Axis.horizontal,\n              itemBuilder: (context, index) => Column(\n                    children: [\n                      CircleAvatar(\n                          radius: 40.r,\n                          backgroundColor: ColorManager.lightGrey,\n                          child: const Padding(\n                              padding: EdgeInsets.all(AppSpaces.padding4),\n                              child: BaseCachedImage(\n                                'https://t4.ftcdn.net/jpg/02/60/04/09/360_F_260040900_oO6YW1sHTnKxby4GcjCvtypUCWjnQRg5.jpg',\n                                radius: AppRadius.radius100,\n                                height: 100,\n                                fit: BoxFit.cover,\n                              ))),\n                      AppGaps.gap4,\n                      Text(\n                        'Amr Gouda',\n                        style: AppTextStyles.labelLarge,\n                      ),\n                    ],\n                  ),\n              separatorBuilder: (context, index) => AppGaps.gap16,\n              itemCount: 6),\n        )\n      ],\n    );\n  }\n}\n", "baseTimestamp": 1756930315842, "deltas": [{"timestamp": 1756930319151, "changes": [{"type": "DELETE", "lineNumber": 15, "oldContent": "        Row("}, {"type": "DELETE", "lineNumber": 16, "oldContent": "          children: ["}, {"type": "DELETE", "lineNumber": 17, "oldContent": "            Text(context.tr.doctors, style: AppTextStyles.title),"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "            const Spacer(),"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "            SeeAllButtonWidget("}, {"type": "DELETE", "lineNumber": 20, "oldContent": "              onPressed: () {"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "                const Doctors<PERSON><PERSON>en().navigate;"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "              },"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "            )"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "          ],"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "        ),"}, {"type": "INSERT", "lineNumber": 15, "content": "     "}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/home.screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/home.screen.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:dropx/src/screens/home/<USER>/widgets/home_slider.widget.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport 'widgets/animals_list.widget.dart';\nimport 'widgets/doctors_list.widget.dart';\nimport 'widgets/products_list.widget.dart';\n\nclass HomeScreen extends StatelessWidget {\n  const HomeScreen({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(\n        backgroundColor: ColorManager.white,\n        surfaceTintColor: ColorManager.white,\n        centerTitle: false,\n        title: Text(\n          context.tr.welcomeWithName('Amr'),\n          style: AppTextStyles.title,\n        ),\n        leading: Padding(\n          padding: const EdgeInsets.all(AppSpaces.padding8),\n          child: CircleAvatar(\n            backgroundColor: ColorManager.lightPrimaryColor,\n            radius: 40.r,\n            child: ClipOval(\n              child: BaseCachedImage(\n                height: 80.h,\n                width: 80.w,\n                'https://cdn4.iconfinder.com/data/icons/gamer-player-3d-avatars-3d-illustration-pack/512/19_Man_T-Shirt_Suit.png',\n                fit: BoxFit.cover, // Ensures the image covers the circular area\n              ),\n            ),\n          ),\n        ),\n        actions: [\n          IconButton(\n              onPressed: () {},\n              icon: const CircleAvatar(\n                  backgroundColor: ColorManager.primaryColor,\n                  child: Icon(Icons.notifications))),\n        ],\n      ),\n      body: ListView(\n        padding:\n            const EdgeInsets.symmetric(horizontal: AppSpaces.screenPadding),\n        children: const [\n          AppGaps.gap16,\n\n          // * Home Slider\n          HomeSliderWidget(),\n\n          AppGaps.gap16,\n\n          // * Animals\n          AnimalsListWidget(),\n\n          AppGaps.gap24,\n\n          // * Products\n          ProductsListWidget(),\n\n          AppGaps.gap24,\n\n          // * Stores\n          HomeStoresListWidget(),\n\n          AppGaps.gap24,\n\n          // * Doctors\n          DoctorsListWidget(),\n\n          AppGaps.gap24,\n        ],\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1756930331759, "deltas": [{"timestamp": 1756930334911, "changes": [{"type": "DELETE", "lineNumber": 69, "oldContent": "          // * Stores"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "          HomeStoresListWidget(),"}]}, {"timestamp": 1756930336991, "changes": [{"type": "DELETE", "lineNumber": 67, "oldContent": "          AppGaps.gap24,"}]}, {"timestamp": 1756931515216, "changes": [{"type": "DELETE", "lineNumber": 67, "oldContent": ""}, {"type": "DELETE", "lineNumber": 68, "oldContent": ""}]}, {"timestamp": 1756931533740, "changes": [{"type": "INSERT", "lineNumber": 67, "content": ""}, {"type": "INSERT", "lineNumber": 68, "content": ""}]}, {"timestamp": 1756931537596, "changes": [{"type": "DELETE", "lineNumber": 67, "oldContent": ""}, {"type": "DELETE", "lineNumber": 70, "oldContent": ""}]}, {"timestamp": 1756931593376, "changes": [{"type": "DELETE", "lineNumber": 8, "oldContent": "import 'widgets/doctors_list.widget.dart';"}]}, {"timestamp": 1756931596885, "changes": [{"type": "DELETE", "lineNumber": 66, "oldContent": "          AppGaps.gap24,"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "          // * Doctors"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "          DoctorsListWidget(),"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/main_screen/view/main.screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/main_screen/view/main.screen.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart';\nimport 'package:dropx/src/screens/home/<USER>/home.screen.dart';\n\nimport '../../../core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';\n\nclass MainScreen extends ConsumerWidget {\n  const MainScreen({super.key});\n\n  @override\n  Widget build(BuildContext context, WidgetRef ref) {\n    final currentIndex = ref.watch(bottomNavigationControllerProvider);\n    return Scaffold(\n      body: _SelectedScreen(currentIndex: currentIndex),\n      bottomNavigationBar: const BottomNavBarWidget(),\n    );\n  }\n}\n\nString selectedTitle(int currentIndex, BuildContext context) {\n  switch (currentIndex) {\n    case 0:\n      return context.tr.home;\n    case 1:\n      return context.tr.reels;\n    case 2:\n      return context.tr.doctors;\n    case 3:\n      return context.tr.shops;\n  }\n\n  return context.tr.home;\n}\n\nclass _SelectedScreen extends StatelessWidget {\n  final int currentIndex;\n\n  const _SelectedScreen({required this.currentIndex});\n\n  @override\n  Widget build(BuildContext context) {\n    switch (currentIndex) {\n      case 0:\n        return const HomeScreen();\n      case 1:\n        return const ReelsScreen();\n    }\n    return const SizedBox.shrink();\n  }\n}\n", "baseTimestamp": 1756930341284, "deltas": [{"timestamp": 1756930344323, "changes": [{"type": "DELETE", "lineNumber": 46, "oldContent": "      case 1:"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "        return const <PERSON><PERSON>S<PERSON><PERSON>();"}, {"type": "INSERT", "lineNumber": 46, "content": ""}]}, {"timestamp": 1756930639830, "changes": [{"type": "MODIFY", "lineNumber": 39, "content": "  const _SelectedScreen({", "oldContent": "  const _SelectedScreen({required this.currentIndex});"}, {"type": "INSERT", "lineNumber": 40, "content": "    required this.currentIndex,"}, {"type": "INSERT", "lineNumber": 41, "content": "  });"}, {"type": "DELETE", "lineNumber": 46, "oldContent": ""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/widgets/products_list.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/widgets/products_list.widget.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport 'see_all_button.widget.dart';\n\nclass ProductsListWidget extends StatelessWidget {\n  const ProductsListWidget({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    final width = 140.w;\n\n    return Column(\n      children: [\n        Row(\n          children: [\n            Text(context.tr.products, style: AppTextStyles.title),\n            const Spacer(),\n            SeeAllButtonWidget(\n              onPressed: () {\n                const ProductsScreen().navigate;\n              },\n            )\n          ],\n        ),\n        AppGaps.gap8,\n        SizedBox(\n          height: 110.h,\n          child: ListView.separated(\n              scrollDirection: Axis.horizontal,\n              itemBuilder: (context, index) => Stack(\n                    children: [\n                      BaseCachedImage(\n                        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTwyXeKDN29AmZgZPLS7n0Bepe8QmVappBwZCeA3XWEbWNdiDFB',\n                        width: width,\n                        height: 110.h,\n                        radius: AppRadius.radius20,\n                      ),\n                      Container(\n                        width: width,\n                        padding: const EdgeInsets.symmetric(\n                          horizontal: AppSpaces.padding12,\n                          vertical: AppSpaces.padding8,\n                        ),\n                        alignment: Alignment.bottomLeft,\n                        decoration: BoxDecoration(\n                          color: Colors.black.withOpacity(0.2),\n                          borderRadius:\n                              BorderRadius.circular(AppSpaces.padding20),\n                        ),\n                        child: Row(\n                          mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                          crossAxisAlignment: CrossAxisAlignment.end,\n                          children: [\n                            Expanded(\n                              child: Column(\n                                crossAxisAlignment: CrossAxisAlignment.start,\n                                mainAxisSize: MainAxisSize.min,\n                                children: [\n                                  Expanded(\n                                    child: Align(\n                                      alignment: Alignment.bottomLeft,\n                                      child: Text(\n                                        'Dogs Food',\n                                        style: AppTextStyles.title\n                                            .copyWith(color: Colors.white),\n                                      ),\n                                    ),\n                                  ),\n                                  Text(\n                                    '\\$20',\n                                    style: AppTextStyles.title.copyWith(\n                                        color: ColorManager.primaryColor),\n                                  ),\n                                ],\n                              ),\n                            ),\n                            CircleAvatar(\n                              backgroundColor: ColorManager.primaryColor,\n                              radius: 14.r,\n                              child: const Icon(\n                                Icons.add_shopping_cart_outlined,\n                                color: Colors.white,\n                                size: 18,\n                              ),\n                            ),\n                          ],\n                        ),\n                      ),\n                    ],\n                  ),\n              separatorBuilder: (context, index) => AppGaps.gap16,\n              itemCount: 6),\n        )\n      ],\n    );\n  }\n}\n", "baseTimestamp": 1756930371310, "deltas": [{"timestamp": 1756930376171, "changes": [{"type": "INSERT", "lineNumber": 6, "content": "import '../../../stores/view/products_screen/products.screen.dart';"}]}, {"timestamp": 1756930389063, "changes": [{"type": "MODIFY", "lineNumber": 6, "content": "import '../../../stores/view/products_screen/stores.screen.dart';", "oldContent": "import '../../../stores/view/products_screen/products.screen.dart';"}]}]}, "/a.dummy": {"filePath": "/a.dummy", "baseContent": "stores.screen.dart", "baseTimestamp": 1756930385283}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/products_screen/products.screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/products_screen/products.screen.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/shared/widgets/app_bar/base_appbar.dart';\nimport 'package:dropx/src/core/shared/widgets/search_bar_widget/search_bar.widget.dart';\nimport 'package:dropx/src/screens/products/view/products_screen/widgets/products_grid.widget.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass ProductsScreen extends StatelessWidget {\n  const ProductsScreen({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: BaseAppBar(\n        title: context.tr.stores,\n      ),\n      body: Padding(\n        padding:\n            const EdgeInsets.symmetric(horizontal: AppSpaces.screenPadding),\n        child: Column(\n          children: [\n            AppGaps.gap12,\n\n            // * Search Bar\n            SearchBarWidget(\n              label: context.tr.searchForProducts,\n            ),\n\n            AppGaps.gap12,\n\n            // * Products Grid\n            const Expanded(child: ProductsGridWidget()),\n          ],\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1756930392570, "deltas": [{"timestamp": 1756930397625, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "import 'package:dropx/src/screens/stores/view/products_screen/widgets/products_grid.widget.dart';"}]}, {"timestamp": 1756930435762, "changes": [{"type": "DELETE", "lineNumber": 5, "oldContent": "import 'package:dropx/src/screens/products/view/products_screen/widgets/products_grid.widget.dart';"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/product_details_screen/store_details.screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/product_details_screen/store_details.screen.dart", "baseContent": "import 'package:flutter/cupertino.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass StoreDetailsScreen extends StatelessWidget {\n  const StoreDetailsScreen({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      body: Column(\n        children: [\n          // * Store Top Section (Image - Fav - Back Arrow)\n          const StoreTopSectionDetailsWidget(),\n\n          AppGaps.gap24,\n\n          // * Store Tabs\n          Expanded(\n            child: ListView(\n              padding:\n                  const EdgeInsets.symmetric(horizontal: AppSpaces.padding12),\n              children: [\n                // about the store\n                Row(\n                  mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                  children: [\n                    // * About\n                    Column(\n                      children: [\n                        Container(\n                          width: 80,\n                          height: 60.h,\n                          decoration: BoxDecoration(\n                            color: ColorManager.primaryColor.withOpacity(.8),\n                            borderRadius:\n                                BorderRadius.circular(AppRadius.radius20),\n                          ),\n                          child: const Icon(\n                            CupertinoIcons.info,\n                            color: ColorManager.white,\n                            size: 40,\n                          ),\n                        ),\n                        AppGaps.gap8,\n                        Text(\n                          context.tr.about,\n                          style: AppTextStyles.title.copyWith(\n                            fontSize: 16,\n                            color: ColorManager.primaryColor,\n                          ),\n                        ),\n                      ],\n                    ),\n\n                    // * Products\n                    Column(\n                      children: [\n                        Container(\n                          width: 80,\n                          height: 60.h,\n                          decoration: BoxDecoration(\n                            border: Border.all(\n                              color: ColorManager.primaryColor.withOpacity(.8),\n                              width: 2,\n                            ),\n                            borderRadius:\n                                BorderRadius.circular(AppRadius.radius20),\n                          ),\n                          child: const Icon(\n                            Icons.shopping_bag_outlined,\n                            color: ColorManager.primaryColor,\n                            size: 40,\n                          ),\n                        ),\n                        AppGaps.gap8,\n                        Text(\n                          context.tr.products,\n                          style: AppTextStyles.title.copyWith(\n                            fontSize: 16,\n                            fontWeight: FontWeight.normal,\n                            color: ColorManager.primaryColor,\n                          ),\n                        ),\n                      ],\n                    ),\n\n                    // * Reels\n                    Column(\n                      children: [\n                        Container(\n                          width: 80,\n                          height: 60.h,\n                          decoration: BoxDecoration(\n                            border: Border.all(\n                              color: ColorManager.primaryColor.withOpacity(.8),\n                              width: 2,\n                            ),\n                            borderRadius:\n                                BorderRadius.circular(AppRadius.radius20),\n                          ),\n                          child: const Icon(\n                            Icons.video_collection_outlined,\n                            color: ColorManager.primaryColor,\n                            size: 40,\n                          ),\n                        ),\n                        AppGaps.gap8,\n                        Text(\n                          'Reels',\n                          style: AppTextStyles.title.copyWith(\n                            fontSize: 16,\n                            fontWeight: FontWeight.normal,\n                            color: ColorManager.primaryColor,\n                          ),\n                        ),\n                      ],\n                    ),\n\n                    // * Animals\n                    Column(\n                      children: [\n                        Container(\n                          width: 80,\n                          height: 60.h,\n                          decoration: BoxDecoration(\n                            border: Border.all(\n                              color: ColorManager.primaryColor.withOpacity(.8),\n                              width: 2,\n                            ),\n                            borderRadius:\n                                BorderRadius.circular(AppRadius.radius20),\n                          ),\n                          child: const Icon(\n                            Icons.pets_outlined,\n                            color: ColorManager.primaryColor,\n                            size: 40,\n                          ),\n                        ),\n                        AppGaps.gap8,\n                        Text(\n                          context.tr.animals,\n                          style: AppTextStyles.title.copyWith(\n                            fontSize: 16,\n                            fontWeight: FontWeight.normal,\n                            color: ColorManager.primaryColor,\n                          ),\n                        ),\n                      ],\n                    ),\n                  ],\n                ),\n              ],\n            ),\n          ),\n        ],\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1756930405920, "deltas": [{"timestamp": 1756930408090, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "import 'package:dropx/src/screens/stores/view/product_details_screen/widgets/store_top_section_details.widget.dart';"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/products_screen/widgets/products_grid.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/products_screen/widgets/products_grid.widget.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:dropx/src/core/shared/extensions/riverpod_extensions.dart';\nimport 'package:dropx/src/core/shared/widgets/lists/base_list.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:dropx/src/screens/stores/providers/store.providers.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass ProductsGridWidget extends ConsumerWidget {\n  const ProductsGridWidget({super.key});\n\n  @override\n  Widget build(BuildContext context, WidgetRef ref) {\n    final storesFuture = ref.watch(getStoresFutureProvider);\n\n    return BaseList.grid(\n      data: const [0, 1, 2, 3, 4],\n      crossAxisCount: 2,\n      mainAxisSpacing: AppSpaces.padding16,\n      itemBuilder: (context, index) => const ProductCard(),\n      separatorGap: AppGaps.gap16,\n    );\n\n    return storesFuture.get(\n      data: (stores) {\n        return BaseList(\n          data: stores,\n          isGrid: true,\n          itemBuilder: (context, index) => GestureDetector(\n            onTap: () {\n              const StoreDetailsScreen().navigate;\n            },\n            child: Stack(\n              children: [\n                BaseCachedImage(\n                  'https://static.wixstatic.com/media/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png/v1/fill/w_614,h_614,al_c,lg_1,q_90/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png',\n                  height: 120.h,\n                  width: 220.w,\n                  radius: AppRadius.radius20,\n                ),\n                Container(\n                  width: 220.w,\n                  padding: const EdgeInsets.symmetric(\n                      horizontal: AppSpaces.padding12,\n                      vertical: AppSpaces.padding8),\n                  alignment: Alignment.bottomCenter,\n                  decoration: BoxDecoration(\n                      color: Colors.black.withOpacity(0.2),\n                      borderRadius: BorderRadius.circular(AppRadius.radius20)),\n                  child: Row(\n                    mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                    children: [\n                      Text(\n                        'Pet Store',\n                        style: AppTextStyles.whiteTitle.copyWith(\n                          fontSize: 20,\n                        ),\n                      ),\n                      const CircleAvatar(\n                        radius: 16,\n                        backgroundColor: ColorManager.primaryColor,\n                        child: Icon(\n                          Icons.arrow_forward_ios,\n                          color: Colors.white,\n                          size: 18,\n                        ),\n                      )\n                    ],\n                  ),\n                )\n              ],\n            ),\n          ),\n          separatorGap: AppGaps.gap16,\n        );\n      },\n    );\n  }\n}\n", "baseTimestamp": 1756930414434, "deltas": [{"timestamp": 1756930418181, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "import 'package:dropx/src/screens/stores/view/products_screen/widgets/product_card.dart';"}]}, {"timestamp": 1756930420500, "changes": [{"type": "DELETE", "lineNumber": 17, "oldContent": "    return BaseList.grid("}, {"type": "DELETE", "lineNumber": 18, "oldContent": "      data: const [0, 1, 2, 3, 4],"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "      crossAxisCount: 2,"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "      mainAxisSpacing: AppSpaces.padding16,"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "      itemBuilder: (context, index) => const ProductCard(),"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "      separatorGap: AppGaps.gap16,"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 24, "oldContent": ""}]}, {"timestamp": *************, "changes": [{"type": "INSERT", "lineNumber": 10, "content": "import '../../product_details_screen/store_details.screen.dart';"}, {"type": "INSERT", "lineNumber": 11, "content": ""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/l10n/intl_en.arb": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/l10n/intl_en.arb", "baseContent": "{\n  \"login\": \"تسجيل الدخول\",\n  \"register\": \"تسجيل\",\n  \"email\": \"البريد الإلكتروني\",\n  \"dontHaveAnAccount\": \"ليس لديك حساب؟\",\n  \"haveAnAccount\": \"لديك حساب؟\",\n  \"password\": \"كلمة المرور\",\n  \"confirmPassword\": \"تأكيد كلمة المرور\",\n  \"forgotPassword\": \"هل نسيت كلمة المرور؟\",\n  \"resetPassword\": \"إعادة تعيين كلمة المرور\",\n  \"mobileNumber\": \"رقم الجوال\",\n  \"loginWithYourAccountNow\": \"سجل الدخول بحسابك الآن!\",\n  \"registerWithYourAccountNow\": \"سجل بحسابك الآن!\",\n  \"skip\": \"تخطي\",\n  \"stores\": \"المتاجر\",\n  \"fullName\": \"الاسم الكامل\",\n  \"registerAsStore\": \"التسجيل كمتجر؟\",\n  \"registerAsDoctor\": \"التسجيل كطبيب؟\",\n  \"home\": \"الرئيسية\",\n  \"reels\": \"ريلز\",\n  \"shops\": \"المحلات\",\n  \"doctors\": \"الأطباء\",\n  \"seeAll\": \"عرض الكل\",\n  \"welcomeWithName\": \"مرحبًا، {name}\",\n  \"animals\": \"الحيوانات\",\n  \"products\": \"المنتجات\",\n  \"about\": \"حول\",\n  \"onBoardingTitle1\": \"اعثر على حيوانك المثالي\",\n  \"onBoardingTitle2\": \"استكشف أفضل متاجر الحيوانات\",\n  \"onBoardingTitle3\": \"اعتنِ بصحة حيوانك الأليف\",\n", "baseTimestamp": *************, "deltas": [{"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 29, "oldContent": "  \"onBoardingTitle3\": \"اعتنِ بصحة حيوانك الأليف\","}, {"type": "DELETE", "lineNumber": 30, "oldContent": ""}, {"type": "INSERT", "lineNumber": 29, "content": "  \"onBoardingTitle3\": \"اعتنِ بصحة حيوانك الأليف\""}, {"type": "INSERT", "lineNumber": 30, "content": "}"}]}, {"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 1, "oldContent": "  \"login\": \"تسجيل الدخول\","}, {"type": "DELETE", "lineNumber": 2, "oldContent": "  \"register\": \"تسجيل\","}, {"type": "DELETE", "lineNumber": 3, "oldContent": "  \"email\": \"ال<PERSON><PERSON><PERSON><PERSON> الإلكتروني\","}, {"type": "DELETE", "lineNumber": 4, "oldContent": "  \"dontHaveAnAccount\": \"ليس لديك حساب؟\","}, {"type": "DELETE", "lineNumber": 5, "oldContent": "  \"haveAnAccount\": \"لديك حساب؟\","}, {"type": "DELETE", "lineNumber": 6, "oldContent": "  \"password\": \"كلمة المرور\","}, {"type": "DELETE", "lineNumber": 7, "oldContent": "  \"confirmPassword\": \"تأكيد كلمة المرور\","}, {"type": "DELETE", "lineNumber": 8, "oldContent": "  \"forgotPassword\": \"هل نسيت كلمة المرور؟\","}, {"type": "DELETE", "lineNumber": 9, "oldContent": "  \"resetPassword\": \"إعادة تعيين كلمة المرور\","}, {"type": "DELETE", "lineNumber": 10, "oldContent": "  \"mobileNumber\": \"رقم الجوال\","}, {"type": "DELETE", "lineNumber": 11, "oldContent": "  \"loginWithYourAccountNow\": \"سجل الدخول بحسابك الآن!\","}, {"type": "DELETE", "lineNumber": 12, "oldContent": "  \"registerWithYourAccountNow\": \"سجل بحسابك الآن!\","}, {"type": "DELETE", "lineNumber": 13, "oldContent": "  \"skip\": \"تخطي\","}, {"type": "DELETE", "lineNumber": 14, "oldContent": "  \"stores\": \"المتاجر\","}, {"type": "DELETE", "lineNumber": 15, "oldContent": "  \"fullName\": \"الاسم الكامل\","}, {"type": "DELETE", "lineNumber": 16, "oldContent": "  \"registerAsStore\": \"التسجيل كمتجر؟\","}, {"type": "DELETE", "lineNumber": 17, "oldContent": "  \"registerAsDoctor\": \"التسجيل كطبيب؟\","}, {"type": "DELETE", "lineNumber": 18, "oldContent": "  \"home\": \"الرئيسية\","}, {"type": "DELETE", "lineNumber": 19, "oldContent": "  \"reels\": \"ريلز\","}, {"type": "DELETE", "lineNumber": 20, "oldContent": "  \"shops\": \"المحلات\","}, {"type": "DELETE", "lineNumber": 21, "oldContent": "  \"doctors\": \"الأطباء\","}, {"type": "DELETE", "lineNumber": 22, "oldContent": "  \"seeAll\": \"عرض الكل\","}, {"type": "DELETE", "lineNumber": 23, "oldContent": "  \"welcomeWithName\": \"مرحبًا، {name}\","}, {"type": "DELETE", "lineNumber": 24, "oldContent": "  \"animals\": \"الحيوانات\","}, {"type": "DELETE", "lineNumber": 25, "oldContent": "  \"products\": \"المنتجات\","}, {"type": "DELETE", "lineNumber": 26, "oldContent": "  \"about\": \"حول\","}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  \"onBoardingTitle1\": \"اعثر على حيوانك المثالي\","}, {"type": "DELETE", "lineNumber": 28, "oldContent": "  \"onBoardingTitle2\": \"استكشف أفضل متاجر الحيوانات\","}, {"type": "DELETE", "lineNumber": 29, "oldContent": "  \"onBoardingTitle3\": \"اعتنِ بصحة حيوانك الأليف\""}, {"type": "INSERT", "lineNumber": 1, "content": "  \"login\": \"Login\","}, {"type": "INSERT", "lineNumber": 2, "content": "  \"register\": \"Register\","}, {"type": "INSERT", "lineNumber": 3, "content": "  \"email\": \"Email\","}, {"type": "INSERT", "lineNumber": 4, "content": "  \"dontHaveAnAccount\": \"Don't have an account?\","}, {"type": "INSERT", "lineNumber": 5, "content": "  \"haveAnAccount\": \"Have an account?\","}, {"type": "INSERT", "lineNumber": 6, "content": "  \"password\": \"Password\","}, {"type": "INSERT", "lineNumber": 7, "content": "  \"confirmPassword\": \"Confirm Password\","}, {"type": "INSERT", "lineNumber": 8, "content": "  \"forgotPassword\": \"Forgot Password?\","}, {"type": "INSERT", "lineNumber": 9, "content": "  \"resetPassword\": \"Reset Password\","}, {"type": "INSERT", "lineNumber": 10, "content": "  \"mobileNumber\": \"Mobile Number\","}, {"type": "INSERT", "lineNumber": 11, "content": "  \"loginWithYourAccountNow\": \"Login with your account now !\","}, {"type": "INSERT", "lineNumber": 12, "content": "  \"registerWithYourAccountNow\": \"Register with your account now !\","}, {"type": "INSERT", "lineNumber": 13, "content": "  \"skip\": \"Ski<PERSON>\","}, {"type": "INSERT", "lineNumber": 14, "content": "  \"stores\": \"Stores\","}, {"type": "INSERT", "lineNumber": 15, "content": "  \"fullName\": \"Full Name\","}, {"type": "INSERT", "lineNumber": 16, "content": "  \"registerAsStore\": \"Register as Store?\","}, {"type": "INSERT", "lineNumber": 17, "content": "  \"registerAsDoctor\": \"Register as Doctor?\","}, {"type": "INSERT", "lineNumber": 18, "content": "  \"home\": \"Home\","}, {"type": "INSERT", "lineNumber": 19, "content": "  \"reels\": \"Reels\","}, {"type": "INSERT", "lineNumber": 20, "content": "  \"shops\": \"Shops\","}, {"type": "INSERT", "lineNumber": 21, "content": "  \"doctors\": \"Doctors\","}, {"type": "INSERT", "lineNumber": 22, "content": "  \"seeAll\": \"See All\","}, {"type": "INSERT", "lineNumber": 23, "content": "  \"welcomeWithName\": \"Welcome, {name}\","}, {"type": "INSERT", "lineNumber": 24, "content": "  \"animals\": \"Animals\","}, {"type": "INSERT", "lineNumber": 25, "content": "  \"products\": \"Products\","}, {"type": "INSERT", "lineNumber": 26, "content": "  \"about\": \"About\","}, {"type": "INSERT", "lineNumber": 27, "content": "  \"onBoardingTitle1\": \"Find Your Perfect Pet\","}, {"type": "INSERT", "lineNumber": 28, "content": "  \"onBoardingTitle2\": \"Explore Top Pet Stores\","}, {"type": "INSERT", "lineNumber": 29, "content": "  \"onBoardingTitle3\": \"Care for Your Pet's Health\","}, {"type": "INSERT", "lineNumber": 30, "content": "  \"onBoardingDescription1\": \"Discover a wide variety of pets to adopt and bring home the perfect companion.\","}, {"type": "INSERT", "lineNumber": 31, "content": "  \"onBoardingDescription2\": \"Browse through trusted pet stores and find the best products for your furry friends.\","}, {"type": "INSERT", "lineNumber": 32, "content": "  \"onBoardingDescription3\": \"Connect with professional veterinarians and ensure your pets receive the best care.\","}, {"type": "INSERT", "lineNumber": 33, "content": "  \"next\": \"Next\","}, {"type": "INSERT", "lineNumber": 34, "content": "  \"startNow\": \"Start Now\","}, {"type": "INSERT", "lineNumber": 35, "content": "  \"description\": \"Description\","}, {"type": "INSERT", "lineNumber": 36, "content": "  \"address\": \"Address\","}, {"type": "INSERT", "lineNumber": 37, "content": "  \"enter\": \"Enter\","}, {"type": "INSERT", "lineNumber": 38, "content": "  \"doctor<PERSON>ame\": \"Doctor Name\","}, {"type": "INSERT", "lineNumber": 39, "content": "  \"emailOptional\": \"Email (Optional)\","}, {"type": "INSERT", "lineNumber": 40, "content": "  \"doctor<PERSON><PERSON>\": \"Doctor <PERSON><PERSON>\","}, {"type": "INSERT", "lineNumber": 41, "content": "  \"save\": \"Save\","}, {"type": "INSERT", "lineNumber": 42, "content": "  \"submit\": \"Submit\","}, {"type": "INSERT", "lineNumber": 43, "content": "  \"pickImage\": \"Pick Image\","}, {"type": "INSERT", "lineNumber": 44, "content": "  \"locationPickedSuccessfully\": \"Location picked successfully\","}, {"type": "INSERT", "lineNumber": 45, "content": "  \"pickLocation\": \"Pick Location\","}, {"type": "INSERT", "lineNumber": 46, "content": "  \"tapToSelectLocation\": \"Tap to select location\","}, {"type": "INSERT", "lineNumber": 47, "content": "  \"changeLocation\": \"Change Location\","}, {"type": "INSERT", "lineNumber": 48, "content": "  \"noDataFound\": \"No data found\","}, {"type": "INSERT", "lineNumber": 49, "content": "  \"changeSocial\": \"Change Social\","}, {"type": "INSERT", "lineNumber": 50, "content": "  \"pleaseAddValidLink\": \"Please add valid link\","}, {"type": "INSERT", "lineNumber": 51, "content": "  \"socialMedia\": \"Social Media\","}, {"type": "INSERT", "lineNumber": 52, "content": "  \"doctorBackground\": \"Doctor Background\","}, {"type": "INSERT", "lineNumber": 53, "content": "  \"pleaseAddYourSocialMedia\": \"Please add your social media\","}, {"type": "INSERT", "lineNumber": 54, "content": "  \"pleaseAddYourLocation\": \"Please add your location\","}, {"type": "INSERT", "lineNumber": 55, "content": "  \"youCanAlsoRegisterAsDoctor\": \"You can also register as a doctor from your profile.\","}, {"type": "INSERT", "lineNumber": 56, "content": "  \"youCanAlsoRegisterAsStore\": \"You can also register as a doctor from your profile.\","}, {"type": "INSERT", "lineNumber": 57, "content": "  \"storeLogo\": \"Store Logo\","}, {"type": "INSERT", "lineNumber": 58, "content": "  \"storeBackground\": \"Store Background\","}, {"type": "INSERT", "lineNumber": 59, "content": "  \"storeName\": \"Store Name\","}, {"type": "INSERT", "lineNumber": 60, "content": "  \"search\": \"Search\","}, {"type": "INSERT", "lineNumber": 61, "content": "  \"searchForProducts\": \"Search for products\","}, {"type": "INSERT", "lineNumber": 62, "content": "  \"searchForStores\": \"Search for stores\","}, {"type": "INSERT", "lineNumber": 63, "content": "  \"searchForDoctors\": \"Search for doctors\""}]}, {"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 1, "oldContent": "  \"login\": \"Login\","}, {"type": "DELETE", "lineNumber": 2, "oldContent": "  \"register\": \"Register\","}, {"type": "DELETE", "lineNumber": 3, "oldContent": "  \"email\": \"Email\","}, {"type": "DELETE", "lineNumber": 4, "oldContent": "  \"dontHaveAnAccount\": \"Don't have an account?\","}, {"type": "DELETE", "lineNumber": 5, "oldContent": "  \"haveAnAccount\": \"Have an account?\","}, {"type": "DELETE", "lineNumber": 6, "oldContent": "  \"password\": \"Password\","}, {"type": "DELETE", "lineNumber": 7, "oldContent": "  \"confirmPassword\": \"Confirm Password\","}, {"type": "DELETE", "lineNumber": 8, "oldContent": "  \"forgotPassword\": \"Forgot Password?\","}, {"type": "DELETE", "lineNumber": 9, "oldContent": "  \"resetPassword\": \"Reset Password\","}, {"type": "DELETE", "lineNumber": 10, "oldContent": "  \"mobileNumber\": \"Mobile Number\","}, {"type": "DELETE", "lineNumber": 11, "oldContent": "  \"loginWithYourAccountNow\": \"Login with your account now !\","}, {"type": "DELETE", "lineNumber": 12, "oldContent": "  \"registerWithYourAccountNow\": \"Register with your account now !\","}, {"type": "DELETE", "lineNumber": 13, "oldContent": "  \"skip\": \"Ski<PERSON>\","}, {"type": "DELETE", "lineNumber": 14, "oldContent": "  \"stores\": \"Stores\","}, {"type": "DELETE", "lineNumber": 15, "oldContent": "  \"fullName\": \"Full Name\","}, {"type": "DELETE", "lineNumber": 16, "oldContent": "  \"registerAsStore\": \"Register as Store?\","}, {"type": "DELETE", "lineNumber": 17, "oldContent": "  \"registerAsDoctor\": \"Register as Doctor?\","}, {"type": "DELETE", "lineNumber": 18, "oldContent": "  \"home\": \"Home\","}, {"type": "DELETE", "lineNumber": 19, "oldContent": "  \"reels\": \"Reels\","}, {"type": "DELETE", "lineNumber": 20, "oldContent": "  \"shops\": \"Shops\","}, {"type": "DELETE", "lineNumber": 21, "oldContent": "  \"doctors\": \"Doctors\","}, {"type": "DELETE", "lineNumber": 22, "oldContent": "  \"seeAll\": \"See All\","}, {"type": "DELETE", "lineNumber": 23, "oldContent": "  \"welcomeWithName\": \"Welcome, {name}\","}, {"type": "DELETE", "lineNumber": 24, "oldContent": "  \"animals\": \"Animals\","}, {"type": "DELETE", "lineNumber": 25, "oldContent": "  \"products\": \"Products\","}, {"type": "DELETE", "lineNumber": 26, "oldContent": "  \"about\": \"About\","}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  \"onBoardingTitle1\": \"Find Your Perfect Pet\","}, {"type": "DELETE", "lineNumber": 28, "oldContent": "  \"onBoardingTitle2\": \"Explore Top Pet Stores\","}, {"type": "DELETE", "lineNumber": 29, "oldContent": "  \"onBoardingTitle3\": \"Care for Your Pet's Health\","}, {"type": "DELETE", "lineNumber": 30, "oldContent": "  \"onBoardingDescription1\": \"Discover a wide variety of pets to adopt and bring home the perfect companion.\","}, {"type": "INSERT", "lineNumber": 1, "content": "  \"login\": \"تسجيل الدخول\","}, {"type": "INSERT", "lineNumber": 2, "content": "  \"register\": \"التسجيل\","}, {"type": "INSERT", "lineNumber": 3, "content": "  \"email\": \"ال<PERSON><PERSON><PERSON><PERSON> الإلكتروني\","}, {"type": "INSERT", "lineNumber": 4, "content": "  \"dontHaveAnAccount\": \"ليس لديك حساب؟\","}, {"type": "INSERT", "lineNumber": 5, "content": "  \"haveAnAccount\": \"لديك حساب؟\","}, {"type": "INSERT", "lineNumber": 6, "content": "  \"password\": \"كلمة المرور\","}, {"type": "INSERT", "lineNumber": 7, "content": "  \"confirmPassword\": \"تأكيد كلمة المرور\","}, {"type": "INSERT", "lineNumber": 8, "content": "  \"forgotPassword\": \"نسيت كلمة المرور؟\","}, {"type": "INSERT", "lineNumber": 9, "content": "  \"resetPassword\": \"إعادة تعيين كلمة المرور\","}, {"type": "INSERT", "lineNumber": 10, "content": "  \"mobileNumber\": \"رقم الهاتف المحمول\","}, {"type": "INSERT", "lineNumber": 11, "content": "  \"loginWithYourAccountNow\": \"سجل دخولك بحسابك الآن!\","}, {"type": "INSERT", "lineNumber": 12, "content": "  \"registerWithYourAccountNow\": \"سجل حسابك الآن!\","}, {"type": "INSERT", "lineNumber": 13, "content": "  \"skip\": \"تخطي\","}, {"type": "INSERT", "lineNumber": 14, "content": "  \"stores\": \"المتاجر\","}, {"type": "INSERT", "lineNumber": 15, "content": "  \"fullName\": \"الاسم الكامل\","}, {"type": "INSERT", "lineNumber": 16, "content": "  \"registerAsStore\": \"التسجيل كمتجر؟\","}, {"type": "INSERT", "lineNumber": 17, "content": "  \"registerAsDoctor\": \"التسجيل كطبيب؟\","}, {"type": "INSERT", "lineNumber": 18, "content": "  \"home\": \"الرئيسية\","}, {"type": "INSERT", "lineNumber": 19, "content": "  \"reels\": \"الريلز\","}, {"type": "INSERT", "lineNumber": 20, "content": "  \"shops\": \"المتاجر\","}, {"type": "INSERT", "lineNumber": 21, "content": "  \"doctors\": \"الأطباء\","}, {"type": "INSERT", "lineNumber": 22, "content": "  \"seeAll\": \"عرض الكل\","}, {"type": "INSERT", "lineNumber": 23, "content": "  \"welcomeWithName\": \"مرحباً، {name}\","}, {"type": "INSERT", "lineNumber": 24, "content": "  \"animals\": \"الحيوانات\","}, {"type": "INSERT", "lineNumber": 25, "content": "  \"products\": \"المنتجات\","}, {"type": "INSERT", "lineNumber": 26, "content": "  \"about\": \"حول\","}, {"type": "INSERT", "lineNumber": 27, "content": "  \"onBoardingTitle1\": \"اعثر على حيوانك الأليف المثالي\","}, {"type": "INSERT", "lineNumber": 28, "content": "  \"onBoardingTitle2\": \"استكشف أفضل متاجر الحيوانات الأليفة\","}, {"type": "INSERT", "lineNumber": 29, "content": "  \"onBoardingTitle3\": \"اعتن بصحة حيوانك الأليف\","}, {"type": "INSERT", "lineNumber": 30, "content": "  \"onBoardingDescription1\": \"اكتشف مجموعة واسعة من الحيوانات الأليفة للتبني واصطحب الرفيق المثالي إلى المنزل.\","}, {"type": "INSERT", "lineNumber": 31, "content": "  \"onBoardingDescription2\": \"تصفح متاجر الحيوانات الأليفة الموثوقة واعثر على أفضل المنتجات لأصدقائك ذوي الفراء.\","}, {"type": "INSERT", "lineNumber": 32, "content": "  \"onBoardingDescription3\": \"تواصل مع الأطباء البيطريين المحترفين وتأكد من حصول حيواناتك الأليفة على أفضل رعاية.\","}, {"type": "INSERT", "lineNumber": 33, "content": "  \"next\": \"التالي\","}, {"type": "INSERT", "lineNumber": 34, "content": "  \"startNow\": \"ابد<PERSON> الآن\","}, {"type": "INSERT", "lineNumber": 35, "content": "  \"description\": \"الوصف\","}, {"type": "INSERT", "lineNumber": 36, "content": "  \"address\": \"الع<PERSON><PERSON><PERSON>\","}, {"type": "INSERT", "lineNumber": 37, "content": "  \"enter\": \"أدخل\","}, {"type": "INSERT", "lineNumber": 38, "content": "  \"doctorName\": \"اسم الطبيب\","}, {"type": "INSERT", "lineNumber": 39, "content": "  \"emailOptional\": \"ال<PERSON><PERSON>ي<PERSON> الإلكتروني (اختياري)\","}, {"type": "INSERT", "lineNumber": 40, "content": "  \"doctorLogo\": \"شعار الطبيب\","}, {"type": "INSERT", "lineNumber": 41, "content": "  \"save\": \"حفظ\","}, {"type": "INSERT", "lineNumber": 42, "content": "  \"submit\": \"إرسال\","}, {"type": "INSERT", "lineNumber": 43, "content": "  \"pickImage\": \"اختيار صورة\","}, {"type": "INSERT", "lineNumber": 44, "content": "  \"locationPickedSuccessfully\": \"تم اختيار الموقع بنجاح\","}, {"type": "INSERT", "lineNumber": 45, "content": "  \"pickLocation\": \"اختيار الموقع\","}, {"type": "INSERT", "lineNumber": 46, "content": "  \"tapToSelectLocation\": \"اضغط لاختيار الموقع\","}, {"type": "INSERT", "lineNumber": 47, "content": "  \"changeLocation\": \"تغيير الموقع\","}, {"type": "INSERT", "lineNumber": 48, "content": "  \"noDataFound\": \"لم يتم العثور على بيانات\","}, {"type": "INSERT", "lineNumber": 49, "content": "  \"changeSocial\": \"تغيير وسائل التواصل\","}, {"type": "INSERT", "lineNumber": 50, "content": "  \"pleaseAddValidLink\": \"يرجى إضافة رابط صحيح\","}, {"type": "INSERT", "lineNumber": 51, "content": "  \"socialMedia\": \"وسائل التواصل الاجتماعي\","}, {"type": "INSERT", "lineNumber": 52, "content": "  \"doctorBackground\": \"خلفية الطبيب\","}, {"type": "INSERT", "lineNumber": 53, "content": "  \"pleaseAddYourSocialMedia\": \"يرجى إضافة وسائل التواصل الاجتماعي الخاصة بك\","}, {"type": "INSERT", "lineNumber": 54, "content": "  \"pleaseAddYourLocation\": \"يرجى إضافة موقعك\","}, {"type": "INSERT", "lineNumber": 55, "content": "  \"youCanAlsoRegisterAsDoctor\": \"يمكنك أيضاً التسجيل كطبيب من ملفك الشخصي.\","}, {"type": "INSERT", "lineNumber": 56, "content": "  \"youCanAlsoRegisterAsStore\": \"يمكنك أيضاً التسجيل كمتجر من ملفك الشخصي.\","}, {"type": "INSERT", "lineNumber": 57, "content": "  \"storeLogo\": \"شعار المتجر\","}, {"type": "INSERT", "lineNumber": 58, "content": "  \"storeBackground\": \"خلفية المتجر\","}, {"type": "INSERT", "lineNumber": 59, "content": "  \"storeName\": \"اسم المتجر\","}, {"type": "INSERT", "lineNumber": 60, "content": "  \"search\": \"البح<PERSON>\","}, {"type": "INSERT", "lineNumber": 61, "content": "  \"searchForProducts\": \"البحث عن المنتجات\","}, {"type": "INSERT", "lineNumber": 62, "content": "  \"searchForStores\": \"البحث عن المتاجر\","}, {"type": "INSERT", "lineNumber": 63, "content": "  \"searchForDoctors\": \"البحث عن الأطباء\""}, {"type": "DELETE", "lineNumber": 32, "oldContent": "  \"onBoardingDescription2\": \"Browse through trusted pet stores and find the best products for your furry friends.\","}, {"type": "DELETE", "lineNumber": 33, "oldContent": "  \"searchForDoctors\": \"Search for doctors\""}, {"type": "DELETE", "lineNumber": 34, "oldContent": "  \"onBoardingDescription3\": \"Connect with professional veterinarians and ensure your pets receive the best care.\","}, {"type": "DELETE", "lineNumber": 35, "oldContent": "  \"searchForStores\": \"Search for stores\","}, {"type": "DELETE", "lineNumber": 36, "oldContent": "  \"next\": \"Next\","}, {"type": "DELETE", "lineNumber": 37, "oldContent": "  \"searchForProducts\": \"Search for products\","}, {"type": "DELETE", "lineNumber": 38, "oldContent": "  \"startNow\": \"Start Now\","}, {"type": "DELETE", "lineNumber": 39, "oldContent": "  \"search\": \"Search\","}, {"type": "DELETE", "lineNumber": 40, "oldContent": "  \"description\": \"Description\","}, {"type": "DELETE", "lineNumber": 41, "oldContent": "  \"storeName\": \"Store Name\","}, {"type": "DELETE", "lineNumber": 42, "oldContent": "  \"address\": \"Address\","}, {"type": "DELETE", "lineNumber": 43, "oldContent": "  \"storeBackground\": \"Store Background\","}, {"type": "DELETE", "lineNumber": 44, "oldContent": "  \"enter\": \"Enter\","}, {"type": "DELETE", "lineNumber": 45, "oldContent": "  \"storeLogo\": \"Store Logo\","}, {"type": "DELETE", "lineNumber": 46, "oldContent": "  \"doctor<PERSON>ame\": \"Doctor Name\","}, {"type": "DELETE", "lineNumber": 47, "oldContent": "  \"youCanAlsoRegisterAsStore\": \"You can also register as a doctor from your profile.\","}, {"type": "DELETE", "lineNumber": 48, "oldContent": "  \"emailOptional\": \"Email (Optional)\","}, {"type": "DELETE", "lineNumber": 49, "oldContent": "  \"youCanAlsoRegisterAsDoctor\": \"You can also register as a doctor from your profile.\","}, {"type": "DELETE", "lineNumber": 50, "oldContent": "  \"doctor<PERSON><PERSON>\": \"Doctor <PERSON><PERSON>\","}, {"type": "DELETE", "lineNumber": 51, "oldContent": "  \"pleaseAddYourLocation\": \"Please add your location\","}, {"type": "DELETE", "lineNumber": 52, "oldContent": "  \"save\": \"Save\","}, {"type": "DELETE", "lineNumber": 53, "oldContent": "  \"pleaseAddYourSocialMedia\": \"Please add your social media\","}, {"type": "DELETE", "lineNumber": 54, "oldContent": "  \"submit\": \"Submit\","}, {"type": "DELETE", "lineNumber": 55, "oldContent": "  \"doctorBackground\": \"Doctor Background\","}, {"type": "DELETE", "lineNumber": 56, "oldContent": "  \"pickImage\": \"Pick Image\","}, {"type": "DELETE", "lineNumber": 57, "oldContent": "  \"socialMedia\": \"Social Media\","}, {"type": "DELETE", "lineNumber": 58, "oldContent": "  \"locationPickedSuccessfully\": \"Location picked successfully\","}, {"type": "DELETE", "lineNumber": 59, "oldContent": "  \"pleaseAddValidLink\": \"Please add valid link\","}, {"type": "DELETE", "lineNumber": 60, "oldContent": "  \"pickLocation\": \"Pick Location\","}, {"type": "DELETE", "lineNumber": 61, "oldContent": "  \"changeSocial\": \"Change Social\","}, {"type": "DELETE", "lineNumber": 62, "oldContent": "  \"tapToSelectLocation\": \"Tap to select location\","}, {"type": "DELETE", "lineNumber": 63, "oldContent": "  \"noDataFound\": \"No data found\","}, {"type": "DELETE", "lineNumber": 64, "oldContent": "  \"changeLocation\": \"Change Location\","}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/l10n/intl_ar.arb": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/l10n/intl_ar.arb", "baseContent": "{\n  \"login\": \"تسجيل الدخول\",\n  \"register\": \"التسجيل\",\n  \"email\": \"البريد الإلكتروني\",\n  \"dontHaveAnAccount\": \"ليس لديك حساب؟\",\n  \"haveAnAccount\": \"لديك حساب؟\",\n  \"password\": \"كلمة المرور\",\n  \"confirmPassword\": \"تأكيد كلمة المرور\",\n  \"forgotPassword\": \"نسيت كلمة المرور؟\",\n  \"resetPassword\": \"إعادة تعيين كلمة المرور\",\n  \"mobileNumber\": \"رقم الهاتف المحمول\",\n  \"loginWithYourAccountNow\": \"سجل دخولك بحسابك الآن!\",\n  \"registerWithYourAccountNow\": \"سجل حسابك الآن!\",\n  \"skip\": \"تخطي\",\n  \"stores\": \"المتاجر\",\n  \"fullName\": \"الاسم الكامل\",\n  \"registerAsStore\": \"التسجيل كمتجر؟\",\n  \"registerAsDoctor\": \"التسجيل كطبيب؟\",\n  \"home\": \"الرئيسية\",\n  \"reels\": \"الريلز\",\n  \"shops\": \"المتاجر\",\n  \"doctors\": \"الأطباء\",\n  \"seeAll\": \"عرض الكل\",\n  \"welcomeWithName\": \"مرحباً، {name}\",\n  \"animals\": \"الحيوانات\",\n  \"products\": \"المنتجات\",\n  \"about\": \"حول\",\n  \"onBoardingTitle1\": \"اعثر على حيوانك الأليف المثالي\",\n  \"onBoardingTitle2\": \"استكشف أفضل متاجر الحيوانات الأليفة\",\n  \"onBoardingTitle3\": \"اعتن بصحة حيوانك الأليف\",\n  \"onBoardingDescription1\": \"اكتشف مجموعة واسعة من الحيوانات الأليفة للتبني واصطحب الرفيق المثالي إلى المنزل.\",\n  \"onBoardingDescription2\": \"تصفح متاجر الحيوانات الأليفة الموثوقة واعثر على أفضل المنتجات لأصدقائك ذوي الفراء.\",\n  \"onBoardingDescription3\": \"تواصل مع الأطباء البيطريين المحترفين وتأكد من حصول حيواناتك الأليفة على أفضل رعاية.\",\n  \"next\": \"التالي\",\n  \"startNow\": \"ابدأ الآن\",\n  \"description\": \"الوصف\",\n  \"address\": \"العنوان\",\n  \"enter\": \"أدخل\",\n  \"doctorName\": \"اسم الطبيب\",\n  \"emailOptional\": \"البريد الإلكتروني (اختياري)\",\n  \"doctorLogo\": \"شعار الطبيب\",\n  \"save\": \"حفظ\",\n  \"submit\": \"إرسال\",\n  \"pickImage\": \"اختيار صورة\",\n  \"locationPickedSuccessfully\": \"تم اختيار الموقع بنجاح\",\n  \"pickLocation\": \"اختيار الموقع\",\n  \"tapToSelectLocation\": \"اضغط لاختيار الموقع\",\n  \"changeLocation\": \"تغيير الموقع\",\n  \"noDataFound\": \"لم يتم العثور على بيانات\",\n  \"changeSocial\": \"تغيير وسائل التواصل\",\n  \"pleaseAddValidLink\": \"يرجى إضافة رابط صحيح\",\n  \"socialMedia\": \"وسائل التواصل الاجتماعي\",\n  \"doctorBackground\": \"خلفية الطبيب\",\n  \"pleaseAddYourSocialMedia\": \"يرجى إضافة وسائل التواصل الاجتماعي الخاصة بك\",\n  \"pleaseAddYourLocation\": \"يرجى إضافة موقعك\",\n  \"youCanAlsoRegisterAsDoctor\": \"يمكنك أيضاً التسجيل كطبيب من ملفك الشخصي.\",\n  \"youCanAlsoRegisterAsStore\": \"يمكنك أيضاً التسجيل كمتجر من ملفك الشخصي.\",\n  \"storeLogo\": \"شعار المتجر\",\n  \"storeBackground\": \"خلفية المتجر\",\n  \"storeName\": \"اسم المتجر\",\n  \"search\": \"البحث\",\n  \"searchForProducts\": \"البحث عن المنتجات\",\n  \"searchForStores\": \"البحث عن المتاجر\",\n  \"searchForDoctors\": \"البحث عن الأطباء\"\n}", "baseTimestamp": *************, "deltas": [{"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 1, "oldContent": "  \"login\": \"تسجيل الدخول\","}, {"type": "DELETE", "lineNumber": 2, "oldContent": "  \"register\": \"التسجيل\","}, {"type": "DELETE", "lineNumber": 3, "oldContent": "  \"email\": \"ال<PERSON><PERSON><PERSON><PERSON> الإلكتروني\","}, {"type": "DELETE", "lineNumber": 4, "oldContent": "  \"dontHaveAnAccount\": \"ليس لديك حساب؟\","}, {"type": "DELETE", "lineNumber": 5, "oldContent": "  \"haveAnAccount\": \"لديك حساب؟\","}, {"type": "DELETE", "lineNumber": 6, "oldContent": "  \"password\": \"كلمة المرور\","}, {"type": "DELETE", "lineNumber": 7, "oldContent": "  \"confirmPassword\": \"تأكيد كلمة المرور\","}, {"type": "DELETE", "lineNumber": 8, "oldContent": "  \"forgotPassword\": \"نسيت كلمة المرور؟\","}, {"type": "DELETE", "lineNumber": 9, "oldContent": "  \"resetPassword\": \"إعادة تعيين كلمة المرور\","}, {"type": "DELETE", "lineNumber": 10, "oldContent": "  \"mobileNumber\": \"رقم الهاتف المحمول\","}, {"type": "DELETE", "lineNumber": 11, "oldContent": "  \"loginWithYourAccountNow\": \"سجل دخولك بحسابك الآن!\","}, {"type": "DELETE", "lineNumber": 12, "oldContent": "  \"registerWithYourAccountNow\": \"سجل حسابك الآن!\","}, {"type": "DELETE", "lineNumber": 13, "oldContent": "  \"skip\": \"تخطي\","}, {"type": "DELETE", "lineNumber": 14, "oldContent": "  \"stores\": \"المتاجر\","}, {"type": "DELETE", "lineNumber": 15, "oldContent": "  \"fullName\": \"الاسم الكامل\","}, {"type": "DELETE", "lineNumber": 16, "oldContent": "  \"registerAsStore\": \"التسجيل كمتجر؟\","}, {"type": "DELETE", "lineNumber": 17, "oldContent": "  \"registerAsDoctor\": \"التسجيل كطبيب؟\","}, {"type": "DELETE", "lineNumber": 18, "oldContent": "  \"home\": \"الرئيسية\","}, {"type": "DELETE", "lineNumber": 19, "oldContent": "  \"reels\": \"الريلز\","}, {"type": "DELETE", "lineNumber": 20, "oldContent": "  \"shops\": \"المتاجر\","}, {"type": "DELETE", "lineNumber": 21, "oldContent": "  \"doctors\": \"الأطباء\","}, {"type": "DELETE", "lineNumber": 22, "oldContent": "  \"seeAll\": \"عرض الكل\","}, {"type": "DELETE", "lineNumber": 23, "oldContent": "  \"welcomeWithName\": \"مرحباً، {name}\","}, {"type": "DELETE", "lineNumber": 24, "oldContent": "  \"animals\": \"الحيوانات\","}, {"type": "DELETE", "lineNumber": 25, "oldContent": "  \"products\": \"المنتجات\","}, {"type": "DELETE", "lineNumber": 26, "oldContent": "  \"about\": \"حول\","}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  \"onBoardingTitle1\": \"اعثر على حيوانك الأليف المثالي\","}, {"type": "DELETE", "lineNumber": 28, "oldContent": "  \"onBoardingTitle2\": \"استكشف أفضل متاجر الحيوانات الأليفة\","}, {"type": "DELETE", "lineNumber": 29, "oldContent": "  \"onBoardingTitle3\": \"اعتن بصحة حيوانك الأليف\","}, {"type": "DELETE", "lineNumber": 30, "oldContent": "  \"onBoardingDescription1\": \"اكتشف مجموعة واسعة من الحيوانات الأليفة للتبني واصطحب الرفيق المثالي إلى المنزل.\","}, {"type": "DELETE", "lineNumber": 31, "oldContent": "  \"onBoardingDescription2\": \"تصفح متاجر الحيوانات الأليفة الموثوقة واعثر على أفضل المنتجات لأصدقائك ذوي الفراء.\","}, {"type": "DELETE", "lineNumber": 32, "oldContent": "  \"onBoardingDescription3\": \"تواصل مع الأطباء البيطريين المحترفين وتأكد من حصول حيواناتك الأليفة على أفضل رعاية.\","}, {"type": "DELETE", "lineNumber": 33, "oldContent": "  \"next\": \"التالي\","}, {"type": "DELETE", "lineNumber": 34, "oldContent": "  \"startNow\": \"ابد<PERSON> الآن\","}, {"type": "DELETE", "lineNumber": 35, "oldContent": "  \"description\": \"الوصف\","}, {"type": "DELETE", "lineNumber": 36, "oldContent": "  \"address\": \"الع<PERSON><PERSON><PERSON>\","}, {"type": "DELETE", "lineNumber": 37, "oldContent": "  \"enter\": \"أدخل\","}, {"type": "DELETE", "lineNumber": 38, "oldContent": "  \"doctorName\": \"اسم الطبيب\","}, {"type": "DELETE", "lineNumber": 39, "oldContent": "  \"emailOptional\": \"ال<PERSON><PERSON>ي<PERSON> الإلكتروني (اختياري)\","}, {"type": "DELETE", "lineNumber": 40, "oldContent": "  \"doctorLogo\": \"شعار الطبيب\","}, {"type": "DELETE", "lineNumber": 41, "oldContent": "  \"save\": \"حفظ\","}, {"type": "DELETE", "lineNumber": 42, "oldContent": "  \"submit\": \"إرسال\","}, {"type": "DELETE", "lineNumber": 43, "oldContent": "  \"pickImage\": \"اختيار صورة\","}, {"type": "DELETE", "lineNumber": 44, "oldContent": "  \"locationPickedSuccessfully\": \"تم اختيار الموقع بنجاح\","}, {"type": "DELETE", "lineNumber": 45, "oldContent": "  \"pickLocation\": \"اختيار الموقع\","}, {"type": "DELETE", "lineNumber": 46, "oldContent": "  \"tapToSelectLocation\": \"اضغط لاختيار الموقع\","}, {"type": "DELETE", "lineNumber": 47, "oldContent": "  \"changeLocation\": \"تغيير الموقع\","}, {"type": "DELETE", "lineNumber": 48, "oldContent": "  \"noDataFound\": \"لم يتم العثور على بيانات\","}, {"type": "DELETE", "lineNumber": 49, "oldContent": "  \"changeSocial\": \"تغيير وسائل التواصل\","}, {"type": "DELETE", "lineNumber": 50, "oldContent": "  \"pleaseAddValidLink\": \"يرجى إضافة رابط صحيح\","}, {"type": "DELETE", "lineNumber": 51, "oldContent": "  \"socialMedia\": \"وسائل التواصل الاجتماعي\","}, {"type": "DELETE", "lineNumber": 52, "oldContent": "  \"doctorBackground\": \"خلفية الطبيب\","}, {"type": "DELETE", "lineNumber": 53, "oldContent": "  \"pleaseAddYourSocialMedia\": \"يرجى إضافة وسائل التواصل الاجتماعي الخاصة بك\","}, {"type": "DELETE", "lineNumber": 54, "oldContent": "  \"pleaseAddYourLocation\": \"يرجى إضافة موقعك\","}, {"type": "DELETE", "lineNumber": 55, "oldContent": "  \"youCanAlsoRegisterAsDoctor\": \"يمكنك أيضاً التسجيل كطبيب من ملفك الشخصي.\","}, {"type": "DELETE", "lineNumber": 56, "oldContent": "  \"youCanAlsoRegisterAsStore\": \"يمكنك أيضاً التسجيل كمتجر من ملفك الشخصي.\","}, {"type": "DELETE", "lineNumber": 57, "oldContent": "  \"storeLogo\": \"شعار المتجر\","}, {"type": "DELETE", "lineNumber": 58, "oldContent": "  \"storeBackground\": \"خلفية المتجر\","}, {"type": "DELETE", "lineNumber": 59, "oldContent": "  \"storeName\": \"اسم المتجر\","}, {"type": "DELETE", "lineNumber": 60, "oldContent": "  \"search\": \"البح<PERSON>\","}, {"type": "DELETE", "lineNumber": 61, "oldContent": "  \"searchForProducts\": \"البحث عن المنتجات\","}, {"type": "DELETE", "lineNumber": 62, "oldContent": "  \"searchForStores\": \"البحث عن المتاجر\","}, {"type": "DELETE", "lineNumber": 63, "oldContent": "  \"searchForDoctors\": \"البحث عن الأطباء\""}, {"type": "INSERT", "lineNumber": 1, "content": ""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/main.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/main.dart", "baseContent": "import 'dart:io';\n\nimport 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:dropx/src/app.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nvoid main() async {\n  WidgetsFlutterBinding.ensureInitialized();\n\n  await GetStorageService.init();\n\n  HttpOverrides.global = MyHttpOverrides();\n\n  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);\n\n  runApp(const ProviderScope(child: BaseApp()));\n}\n", "baseTimestamp": 1756931861026, "deltas": [{"timestamp": 1756931866809, "changes": [{"type": "MODIFY", "lineNumber": 14, "content": "  // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);", "oldContent": "  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}]}, {"timestamp": 1756931931466, "changes": [{"type": "MODIFY", "lineNumber": 15, "content": "showToast(context.tr.)", "oldContent": ""}]}, {"timestamp": 1756931934281, "changes": [{"type": "MODIFY", "lineNumber": 15, "content": "showToast(context.tr.welcomeWithName);", "oldContent": "showToast(context.tr.)"}]}, {"timestamp": 1756931942833, "changes": [{"type": "MODIFY", "lineNumber": 15, "content": "showToast(context.tr.error, i);", "oldContent": "showToast(context.tr.welcomeWithName);"}]}, {"timestamp": 1756931946030, "changes": [{"type": "MODIFY", "lineNumber": 15, "content": "showToast(context.tr.error, isError: true);", "oldContent": "showToast(context.tr.error, i);"}]}, {"timestamp": 1756931958763, "changes": [{"type": "DELETE", "lineNumber": 15, "oldContent": "showToast(context.tr.error, isError: true);"}]}]}}}