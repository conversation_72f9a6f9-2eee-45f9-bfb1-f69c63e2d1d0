{"snapshots": {"/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/products/controllers/store.controller.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/products/controllers/store.controller.dart", "baseContent": "import 'package:dropx/src/screens/stores/models/store.model.dart';\nimport 'package:dropx/src/screens/stores/repositories/store.repository.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass StoresController extends BaseVM {\n  final StoresRepository storesRepo;\n\n  StoresController({\n    required this.storesRepo,\n  });\n\n  // * Get Stores\n  Future<List<StoreModel>> getStores() async {\n    return await baseFunction(\n      () async {\n        return await storesRepo.getStores();\n      },\n    );\n  }\n\n  // * Add Stores\n  Future<void> addStores({\n    required Map<String, dynamic> data,\n  }) async {\n    return await baseFunction(\n      () async {\n        return await storesRepo.addStores(data: data);\n      },\n    );\n  }\n}\n", "baseTimestamp": 1756912559704}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/settings.gradle": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/settings.gradle", "baseContent": "pluginManagement {\n    def flutterSdkPath = {\n        def properties = new Properties()\n        file(\"local.properties\").withInputStream { properties.load(it) }\n        def flutterSdkPath = properties.getProperty(\"flutter.sdk\")\n        assert flutterSdkPath != null, \"flutter.sdk not set in local.properties\"\n        return flutterSdkPath\n    }()\n\n    includeBuild(\"$flutterSdkPath/packages/flutter_tools/gradle\")\n\n    repositories {\n        google()\n        mavenCentral()\n        gradlePluginPortal()\n    }\n}\n\nplugins {\n    id \"dev.flutter.flutter-plugin-loader\" version \"1.0.0\"\n    id \"com.android.application\" version \"8.6.0\" apply false\n    id \"org.jetbrains.kotlin.android\" version \"2.1.0\" apply false\n}\n\ninclude \":app\"\n", "baseTimestamp": 1756912592007}, "/terminal_output": {"filePath": "/terminal_output", "baseContent": "xrgouda@Amrs-MacBook-Air dropx % cd packages\nxrgouda@Amrs-MacBook-Air packages % cd x\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "baseTimestamp": 1756912714521, "deltas": [{"timestamp": 1756912723747, "changes": [{"type": "DELETE", "lineNumber": 1, "oldContent": "xrgouda@Amrs-MacBook-Air packages % cd x"}, {"type": "DELETE", "lineNumber": 2, "oldContent": ""}, {"type": "DELETE", "lineNumber": 3, "oldContent": ""}, {"type": "DELETE", "lineNumber": 4, "oldContent": ""}, {"type": "DELETE", "lineNumber": 5, "oldContent": ""}, {"type": "DELETE", "lineNumber": 6, "oldContent": ""}, {"type": "DELETE", "lineNumber": 7, "oldContent": ""}, {"type": "DELETE", "lineNumber": 8, "oldContent": ""}, {"type": "DELETE", "lineNumber": 9, "oldContent": ""}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}, {"type": "DELETE", "lineNumber": 11, "oldContent": ""}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "DELETE", "lineNumber": 13, "oldContent": ""}, {"type": "DELETE", "lineNumber": 14, "oldContent": ""}, {"type": "DELETE", "lineNumber": 15, "oldContent": ""}, {"type": "INSERT", "lineNumber": 1, "content": "xrgouda@Amrs-MacBook-Air packages % cd xr_helper"}, {"type": "INSERT", "lineNumber": 2, "content": "xrgouda@Amrs-MacBook-Air xr_helper % flutter pub upgrade --major-versions"}, {"type": "INSERT", "lineNumber": 3, "content": "Resolving dependencies... (1.2s)"}, {"type": "INSERT", "lineNumber": 4, "content": "Changed 6 constraints in pubspec.yaml:"}, {"type": "INSERT", "lineNumber": 5, "content": "  intl: ^0.19.0 -> ^0.20.2"}, {"type": "INSERT", "lineNumber": 6, "content": "  firebase_core: ^3.6.0 -> ^4.1.0"}, {"type": "INSERT", "lineNumber": 7, "content": "  firebase_messaging: ^15.1.3 -> ^16.0.1"}, {"type": "INSERT", "lineNumber": 8, "content": "  flutter_hooks: ^0.20.3 -> ^0.21.3+1"}, {"type": "INSERT", "lineNumber": 9, "content": "  flutter_form_builder: ^9.3.0 -> ^10.2.0"}, {"type": "INSERT", "lineNumber": 10, "content": "  flutter_lints: ^3.0.1 -> ^6.0.0"}, {"type": "INSERT", "lineNumber": 11, "content": "Resolving dependencies... "}, {"type": "INSERT", "lineNumber": 12, "content": "Downloading packages... (1.1s)"}, {"type": "INSERT", "lineNumber": 13, "content": "> _flutterfire_internals 1.3.61 (was 1.3.59)"}, {"type": "INSERT", "lineNumber": 14, "content": "  characters 1.4.0 (1.4.1 available)"}, {"type": "INSERT", "lineNumber": 15, "content": "> firebase_core 4.1.0 (was 3.15.2)"}, {"type": "INSERT", "lineNumber": 16, "content": "> firebase_core_web 3.1.0 (was 2.24.1)"}, {"type": "INSERT", "lineNumber": 17, "content": "> firebase_messaging 16.0.1 (was 15.2.10)"}, {"type": "INSERT", "lineNumber": 18, "content": "> firebase_messaging_platform_interface 4.7.1 (was 4.6.10)"}, {"type": "INSERT", "lineNumber": 19, "content": "> firebase_messaging_web 4.0.1 (was 3.10.10)"}, {"type": "INSERT", "lineNumber": 20, "content": "> flutter_form_builder 10.2.0 (was 9.7.0)"}, {"type": "INSERT", "lineNumber": 21, "content": "> flutter_hooks 0.21.3+1 (was 0.20.5)"}, {"type": "INSERT", "lineNumber": 22, "content": "> flutter_lints 6.0.0 (was 3.0.2)"}, {"type": "INSERT", "lineNumber": 23, "content": "> intl 0.20.2 (was 0.19.0)"}, {"type": "INSERT", "lineNumber": 24, "content": "> lints 6.0.0 (was 3.0.0)"}, {"type": "INSERT", "lineNumber": 25, "content": "  material_color_utilities 0.11.1 (0.13.0 available)"}, {"type": "INSERT", "lineNumber": 26, "content": "  meta 1.16.0 (1.17.0 available)"}, {"type": "INSERT", "lineNumber": 27, "content": "  test_api 0.7.6 (0.7.7 available)"}, {"type": "INSERT", "lineNumber": 28, "content": "Changed 11 dependencies!"}, {"type": "INSERT", "lineNumber": 29, "content": "4 packages have newer versions incompatible with dependency constraints."}, {"type": "INSERT", "lineNumber": 30, "content": "Try `flutter pub outdated` for more information."}, {"type": "INSERT", "lineNumber": 31, "content": "xrgouda@Amrs-MacBook-Air xr_helper % "}]}, {"timestamp": 1756912736827, "changes": [{"type": "DELETE", "lineNumber": 17, "oldContent": "xrgouda@Amrs-MacBook-Air xr_helper % "}, {"type": "DELETE", "lineNumber": 19, "oldContent": "Try `flutter pub outdated` for more information."}, {"type": "DELETE", "lineNumber": 21, "oldContent": "4 packages have newer versions incompatible with dependency constraints."}, {"type": "DELETE", "lineNumber": 23, "oldContent": "Changed 11 dependencies!"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "  test_api 0.7.6 (0.7.7 available)"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  meta 1.16.0 (1.17.0 available)"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "  material_color_utilities 0.11.1 (0.13.0 available)"}, {"type": "INSERT", "lineNumber": 25, "content": "  material_color_utilities 0.11.1 (0.13.0 available)"}, {"type": "INSERT", "lineNumber": 26, "content": "  meta 1.16.0 (1.17.0 available)"}, {"type": "INSERT", "lineNumber": 27, "content": "  test_api 0.7.6 (0.7.7 available)"}, {"type": "INSERT", "lineNumber": 28, "content": "Changed 11 dependencies!"}, {"type": "INSERT", "lineNumber": 29, "content": "4 packages have newer versions incompatible with dependency constraints."}, {"type": "INSERT", "lineNumber": 30, "content": "Try `flutter pub outdated` for more information."}, {"type": "INSERT", "lineNumber": 31, "content": "xrgouda@Amrs-MacBook-Air xr_helper % cd xr_helper                        "}]}, {"timestamp": 1756912742841, "changes": [{"type": "INSERT", "lineNumber": 21, "content": "> flutter_hooks 0.21.3+1 (was 0.20.5)"}, {"type": "INSERT", "lineNumber": 22, "content": "> flutter_lints 6.0.0 (was 3.0.2)"}, {"type": "INSERT", "lineNumber": 23, "content": "> intl 0.20.2 (was 0.19.0)"}, {"type": "INSERT", "lineNumber": 24, "content": "> lints 6.0.0 (was 3.0.0)"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "> flutter_hooks 0.21.3+1 (was 0.20.5)"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "> flutter_lints 6.0.0 (was 3.0.2)"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "> intl 0.20.2 (was 0.19.0)"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "xrgouda@Amrs-MacBook-Air xr_helper % cd xr_helper                        "}, {"type": "MODIFY", "lineNumber": 31, "content": "xrgouda@Amrs-MacBook-Air xr_helper % cd ..                               ", "oldContent": "> lints 6.0.0 (was 3.0.0)"}, {"type": "INSERT", "lineNumber": 32, "content": "xrgouda@Amrs-MacBook-Air packages % flutter pub upgrade --major-versions"}]}, {"timestamp": 1756912796516, "changes": [{"type": "DELETE", "lineNumber": 22, "oldContent": "  material_color_utilities 0.11.1 (0.13.0 available)"}, {"type": "MODIFY", "lineNumber": 25, "content": "  material_color_utilities 0.11.1 (0.13.0 available)", "oldContent": "  meta 1.16.0 (1.17.0 available)"}, {"type": "INSERT", "lineNumber": 26, "content": "  meta 1.16.0 (1.17.0 available)"}, {"type": "INSERT", "lineNumber": 33, "content": "Resolving dependencies... (2.6s)"}, {"type": "INSERT", "lineNumber": 34, "content": "Changed 7 constraints in pubspec.yaml:"}, {"type": "INSERT", "lineNumber": 35, "content": "  flutter_hooks: ^0.20.4 -> ^0.21.3+1"}, {"type": "INSERT", "lineNumber": 36, "content": "  flutter_form_builder: ^9.4.1 -> ^10.2.0"}, {"type": "INSERT", "lineNumber": 37, "content": "  permission_handler: ^11.3.1 -> ^12.0.1"}, {"type": "INSERT", "lineNumber": 38, "content": "  geolocator: ^13.0.1 -> ^14.0.2"}, {"type": "INSERT", "lineNumber": 39, "content": "  firebase_core: ^3.6.0 -> ^4.1.0"}, {"type": "INSERT", "lineNumber": 40, "content": "  flutter_lints: ^4.0.0 -> ^6.0.0"}, {"type": "INSERT", "lineNumber": 41, "content": "  flutter_launcher_icons: ^0.13.1 -> ^0.14.4"}, {"type": "INSERT", "lineNumber": 42, "content": "Resolving dependencies in `/Users/<USER>/Flutter-Projects/Ajory/dropx`... "}, {"type": "INSERT", "lineNumber": 43, "content": "Downloading packages... (31.4s)"}, {"type": "INSERT", "lineNumber": 44, "content": "> _fe_analyzer_shared 88.0.0 (was 72.0.0)"}, {"type": "INSERT", "lineNumber": 45, "content": "> _flutterfire_internals 1.3.61 (was 1.3.44)"}, {"type": "INSERT", "lineNumber": 46, "content": "> analyzer 8.1.1 (was 6.7.0)"}, {"type": "INSERT", "lineNumber": 47, "content": "> another_flushbar 1.12.31 (was 1.12.30)"}, {"type": "INSERT", "lineNumber": 48, "content": "> any_link_preview 3.0.3 (was 3.0.2)"}, {"type": "INSERT", "lineNumber": 49, "content": "! archive 3.6.1 (overridden) (4.0.7 available)"}, {"type": "INSERT", "lineNumber": 50, "content": "> args 2.7.0 (was 2.6.0)"}, {"type": "INSERT", "lineNumber": 51, "content": "> async 2.13.0 (was 2.11.0)"}, {"type": "INSERT", "lineNumber": 52, "content": "> audio_waveforms 1.3.0 (was 1.2.0)"}, {"type": "INSERT", "lineNumber": 53, "content": "> boolean_selector 2.1.2 (was 2.1.1)"}, {"type": "INSERT", "lineNumber": 54, "content": "> build 3.1.0 (was 2.4.1) (4.0.0 available)"}, {"type": "INSERT", "lineNumber": 55, "content": "> build_config 1.2.0 (was 1.1.1)"}, {"type": "INSERT", "lineNumber": 56, "content": "> build_daemon 4.0.4 (was 4.0.2)"}, {"type": "INSERT", "lineNumber": 57, "content": "> build_resolvers 3.0.3 (was 2.4.2) (3.0.4 available)"}, {"type": "INSERT", "lineNumber": 58, "content": "> build_runner 2.7.1 (was 2.4.13) (2.7.2 available)"}, {"type": "INSERT", "lineNumber": 59, "content": "> build_runner_core 9.3.1 (was 7.3.2) (9.3.2 available)"}, {"type": "INSERT", "lineNumber": 60, "content": "> built_value 8.11.1 (was 8.9.2)"}, {"type": "INSERT", "lineNumber": 61, "content": "> carousel_slider 5.1.1 (was 5.0.0)"}, {"type": "INSERT", "lineNumber": 62, "content": "> characters 1.4.0 (was 1.3.0) (1.4.1 available)"}, {"type": "INSERT", "lineNumber": 63, "content": "> chatview 2.5.0 (was 2.3.0)"}, {"type": "INSERT", "lineNumber": 64, "content": "+ chatview_utils 0.0.1"}, {"type": "INSERT", "lineNumber": 65, "content": "> checked_yaml 2.0.4 (was 2.0.3)"}, {"type": "INSERT", "lineNumber": 66, "content": "> cli_util 0.4.2 (was 0.4.1)"}, {"type": "INSERT", "lineNumber": 67, "content": "> clock 1.1.2 (was 1.1.1)"}, {"type": "INSERT", "lineNumber": 68, "content": "> code_builder 4.10.1 (was 4.10.0)"}, {"type": "INSERT", "lineNumber": 69, "content": "> collection 1.19.1 (was 1.18.0)"}, {"type": "INSERT", "lineNumber": 70, "content": "> convert 3.1.2 (was 3.1.1)"}, {"type": "INSERT", "lineNumber": 71, "content": "> crypto 3.0.6 (was 3.0.5)"}, {"type": "INSERT", "lineNumber": 72, "content": "> csslib 1.0.2 (was 1.0.0)"}, {"type": "INSERT", "lineNumber": 73, "content": "> dart_style 3.1.2 (was 2.3.7)"}, {"type": "INSERT", "lineNumber": 74, "content": "+ dbus 0.7.11"}, {"type": "INSERT", "lineNumber": 75, "content": "> emoji_picker_flutter 4.3.0 (was 3.1.0)"}, {"type": "INSERT", "lineNumber": 76, "content": "> equatable 2.0.7 (was 2.0.5)"}, {"type": "INSERT", "lineNumber": 77, "content": "> fake_async 1.3.3 (was 1.3.1)"}, {"type": "INSERT", "lineNumber": 78, "content": "> ffi 2.1.4 (was 2.1.3)"}, {"type": "INSERT", "lineNumber": 79, "content": "> file_selector_linux 0.9.3+2 (was 0.9.3)"}, {"type": "INSERT", "lineNumber": 80, "content": "> file_selector_macos 0.9.4+4 (was 0.9.4+2)"}, {"type": "INSERT", "lineNumber": 81, "content": "> file_selector_windows 0.9.3+4 (was 0.9.3+3)"}, {"type": "INSERT", "lineNumber": 82, "content": "> firebase_core 4.1.0 (was 3.6.0)"}, {"type": "INSERT", "lineNumber": 83, "content": "> firebase_core_platform_interface 6.0.0 (was 5.3.0)"}, {"type": "INSERT", "lineNumber": 84, "content": "> firebase_core_web 3.1.0 (was 2.18.1)"}, {"type": "INSERT", "lineNumber": 85, "content": "> firebase_messaging 16.0.1 (was 15.1.3)"}, {"type": "INSERT", "lineNumber": 86, "content": "> firebase_messaging_platform_interface 4.7.1 (was 4.5.46)"}, {"type": "INSERT", "lineNumber": 87, "content": "> firebase_messaging_web 4.0.1 (was 3.9.2)"}, {"type": "INSERT", "lineNumber": 88, "content": "> fixnum 1.1.1 (was 1.1.0)"}, {"type": "INSERT", "lineNumber": 89, "content": "> flutter_form_builder 10.2.0 (was 9.5.0)"}, {"type": "INSERT", "lineNumber": 90, "content": "> flutter_gen_core 5.11.0 (was 5.8.0)"}, {"type": "INSERT", "lineNumber": 91, "content": "> flutter_gen_runner 5.11.0 (was 5.8.0)"}, {"type": "INSERT", "lineNumber": 92, "content": "> flutter_hooks 0.21.3+1 (was 0.20.5)"}, {"type": "INSERT", "lineNumber": 93, "content": "> flutter_launcher_icons 0.14.4 (was 0.13.1)"}, {"type": "INSERT", "lineNumber": 94, "content": "> flutter_lints 6.0.0 (was 4.0.0)"}, {"type": "INSERT", "lineNumber": 95, "content": "> flutter_native_splash 2.4.6 (was 2.4.1)"}, {"type": "INSERT", "lineNumber": 96, "content": "> flutter_plugin_android_lifecycle 2.0.30 (was 2.0.23)"}, {"type": "INSERT", "lineNumber": 97, "content": "> flutter_riverpod 2.6.1 (was 2.5.3)"}, {"type": "INSERT", "lineNumber": 98, "content": "> flutter_svg 2.2.0 (was 2.0.10+1)"}, {"type": "INSERT", "lineNumber": 99, "content": "> fluttertoast 8.2.12 (was 8.2.8)"}, {"type": "INSERT", "lineNumber": 100, "content": "> font_awesome_flutter 10.10.0 (was 10.7.0)"}, {"type": "INSERT", "lineNumber": 101, "content": "> form_builder_image_picker 4.3.1 (was 4.1.0)"}, {"type": "INSERT", "lineNumber": 102, "content": "> form_builder_validators 11.2.0 (was 11.0.0)"}, {"type": "INSERT", "lineNumber": 103, "content": "+ geoclue 0.1.1"}, {"type": "INSERT", "lineNumber": 104, "content": "> geolocator 14.0.2 (was 13.0.1)"}, {"type": "INSERT", "lineNumber": 105, "content": "> geolocator_android 5.0.2 (was 4.6.1)"}, {"type": "INSERT", "lineNumber": 106, "content": "> geolocator_apple 2.3.13 (was 2.3.7)"}, {"type": "INSERT", "lineNumber": 107, "content": "+ geolocator_linux 0.2.3"}, {"type": "INSERT", "lineNumber": 108, "content": "> geolocator_platform_interface 4.2.6 (was 4.2.4)"}, {"type": "INSERT", "lineNumber": 109, "content": "> geolocator_web 4.1.3 (was 4.1.1)"}, {"type": "INSERT", "lineNumber": 110, "content": "> geolocator_windows 0.2.5 (was 0.2.3)"}, {"type": "INSERT", "lineNumber": 111, "content": "> get 4.7.2 (was 4.6.6)"}, {"type": "INSERT", "lineNumber": 112, "content": "> glob 2.1.3 (was 2.1.2)"}, {"type": "INSERT", "lineNumber": 113, "content": "> google_fonts 6.3.1 (was 6.2.1)"}, {"type": "INSERT", "lineNumber": 114, "content": "> google_maps_flutter 2.13.1 (was 2.9.0)"}, {"type": "INSERT", "lineNumber": 115, "content": "> google_maps_flutter_android 2.18.2 (was 2.14.10)"}, {"type": "INSERT", "lineNumber": 116, "content": "> google_maps_flutter_ios 2.15.5 (was 2.13.1)"}, {"type": "INSERT", "lineNumber": 117, "content": "> google_maps_flutter_platform_interface 2.14.0 (was 2.9.5)"}, {"type": "INSERT", "lineNumber": 118, "content": "> google_maps_flutter_web 0.5.14 (was 0.5.10)"}, {"type": "INSERT", "lineNumber": 119, "content": "+ gsettings 0.2.8"}, {"type": "INSERT", "lineNumber": 120, "content": "> hooks_riverpod 2.6.1 (was 2.5.4)"}, {"type": "INSERT", "lineNumber": 121, "content": "> html 0.15.6 (was 0.15.4)"}, {"type": "INSERT", "lineNumber": 122, "content": "> http 1.5.0 (was 1.2.2)"}, {"type": "INSERT", "lineNumber": 123, "content": "> http_multi_server 3.2.2 (was 3.2.1)"}, {"type": "INSERT", "lineNumber": 124, "content": "> http_parser 4.1.2 (was 4.0.2)"}, {"type": "INSERT", "lineNumber": 125, "content": "> image 4.5.4 (was 4.2.0)"}, {"type": "INSERT", "lineNumber": 126, "content": "> image_picker 1.2.0 (was 1.1.2)"}, {"type": "INSERT", "lineNumber": 127, "content": "> image_picker_android 0.8.13+1 (was 0.8.12+17)"}, {"type": "INSERT", "lineNumber": 128, "content": "> image_picker_for_web 3.1.0 (was 3.0.6)"}, {"type": "INSERT", "lineNumber": 129, "content": "> image_picker_ios 0.8.13 (was 0.8.12+1)"}, {"type": "INSERT", "lineNumber": 130, "content": "> image_picker_linux 0.2.2 (was 0.2.1+1)"}, {"type": "INSERT", "lineNumber": 131, "content": "> image_picker_macos 0.2.2 (was 0.2.1+1)"}, {"type": "INSERT", "lineNumber": 132, "content": "> image_picker_platform_interface 2.11.0 (was 2.10.0)"}, {"type": "INSERT", "lineNumber": 133, "content": "> image_picker_windows 0.2.2 (was 0.2.1+1)"}, {"type": "INSERT", "lineNumber": 134, "content": "> image_size_getter 2.4.1 (was 2.2.0)"}, {"type": "INSERT", "lineNumber": 135, "content": "> intl 0.20.2 (was 0.19.0)"}, {"type": "INSERT", "lineNumber": 136, "content": "> io 1.0.5 (was 1.0.4)"}, {"type": "INSERT", "lineNumber": 137, "content": "> leak_tracker 11.0.1 (was 10.0.5)"}, {"type": "INSERT", "lineNumber": 138, "content": "> leak_tracker_flutter_testing 3.0.10 (was 3.0.5)"}, {"type": "INSERT", "lineNumber": 139, "content": "> leak_tracker_testing 3.0.2 (was 3.0.1)"}, {"type": "INSERT", "lineNumber": 140, "content": "> lints 6.0.0 (was 4.0.0)"}, {"type": "INSERT", "lineNumber": 141, "content": "> logger 2.6.1 (was 2.4.0)"}, {"type": "INSERT", "lineNumber": 142, "content": "> logging 1.3.0 (was 1.2.0)"}, {"type": "INSERT", "lineNumber": 143, "content": "> lottie 3.3.1 (was 3.1.3)"}, {"type": "INSERT", "lineNumber": 144, "content": "> matcher 0.12.17 (was 0.12.16+1)"}, {"type": "INSERT", "lineNumber": 145, "content": "  material_color_utilities 0.11.1 (0.13.0 available)"}, {"type": "INSERT", "lineNumber": 146, "content": "> meta 1.16.0 (was 1.15.0) (1.17.0 available)"}, {"type": "INSERT", "lineNumber": 147, "content": "> package_config 2.2.0 (was 2.1.0)"}, {"type": "INSERT", "lineNumber": 148, "content": "+ package_info_plus 8.3.1"}, {"type": "INSERT", "lineNumber": 149, "content": "+ package_info_plus_platform_interface 3.2.1"}, {"type": "INSERT", "lineNumber": 150, "content": "> path 1.9.1 (was 1.9.0)"}, {"type": "INSERT", "lineNumber": 151, "content": "> path_parsing 1.1.0 (was 1.0.1)"}, {"type": "INSERT", "lineNumber": 152, "content": "> path_provider 2.1.5 (was 2.1.4)"}, {"type": "INSERT", "lineNumber": 153, "content": "> path_provider_android 2.2.18 (was 2.2.12)"}, {"type": "INSERT", "lineNumber": 154, "content": "> path_provider_foundation 2.4.2 (was 2.4.0)"}, {"type": "INSERT", "lineNumber": 155, "content": "> permission_handler 12.0.1 (was 11.3.1)"}, {"type": "INSERT", "lineNumber": 156, "content": "> permission_handler_android 13.0.1 (was 12.0.13)"}, {"type": "INSERT", "lineNumber": 157, "content": "> permission_handler_apple 9.4.7 (was 9.4.5)"}, {"type": "INSERT", "lineNumber": 158, "content": "> permission_handler_html 0.1.3+5 (was 0.1.3+2)"}, {"type": "INSERT", "lineNumber": 159, "content": "> permission_handler_platform_interface 4.3.0 (was 4.2.3)"}, {"type": "INSERT", "lineNumber": 160, "content": "> petitparser 7.0.1 (was 6.0.2)"}, {"type": "INSERT", "lineNumber": 161, "content": "> platform 3.1.6 (was 3.1.5)"}, {"type": "INSERT", "lineNumber": 162, "content": "> provider 6.1.5+1 (was 6.1.2)"}, {"type": "INSERT", "lineNumber": 163, "content": "> pub_semver 2.2.0 (was 2.1.4)"}, {"type": "INSERT", "lineNumber": 164, "content": "> pubspec_parse 1.5.0 (was 1.3.0)"}, {"type": "INSERT", "lineNumber": 165, "content": "> riverpod 2.6.1 (was 2.5.3)"}, {"type": "INSERT", "lineNumber": 166, "content": "> shared_preferences 2.5.3 (was 2.3.3)"}, {"type": "INSERT", "lineNumber": 167, "content": "> shared_preferences_android 2.4.12 (was 2.3.4)"}, {"type": "INSERT", "lineNumber": 168, "content": "> shared_preferences_foundation 2.5.4 (was 2.5.3)"}, {"type": "INSERT", "lineNumber": 169, "content": "> shared_preferences_web 2.4.3 (was 2.4.2)"}, {"type": "INSERT", "lineNumber": 170, "content": "> shelf 1.4.2 (was 1.4.1)"}, {"type": "INSERT", "lineNumber": 171, "content": "> shelf_web_socket 3.0.0 (was 2.0.0)"}, {"type": "INSERT", "lineNumber": 172, "content": "< sky_engine 0.0.0 from sdk flutter (was 0.0.99 from sdk flutter)"}, {"type": "INSERT", "lineNumber": 173, "content": "> smooth_page_indicator 1.2.1 (was 1.2.0+3)"}, {"type": "INSERT", "lineNumber": 174, "content": "> source_span 1.10.1 (was 1.10.0)"}, {"type": "INSERT", "lineNumber": 175, "content": "> sqflite 2.4.2 (was 2.4.0)"}, {"type": "INSERT", "lineNumber": 176, "content": "> sqflite_android 2.4.2+2 (was 2.4.0)"}, {"type": "INSERT", "lineNumber": 177, "content": "> sqflite_common 2.5.6 (was 2.5.4+5)"}, {"type": "INSERT", "lineNumber": 178, "content": "> sqflite_darwin 2.4.2 (was 2.4.1-1)"}, {"type": "INSERT", "lineNumber": 179, "content": "> stack_trace 1.12.1 (was 1.11.1)"}, {"type": "INSERT", "lineNumber": 180, "content": "> stream_channel 2.1.4 (was 2.1.2)"}, {"type": "INSERT", "lineNumber": 181, "content": "> stream_transform 2.1.1 (was 2.1.0)"}, {"type": "INSERT", "lineNumber": 182, "content": "> string_scanner 1.4.1 (was 1.2.0)"}, {"type": "INSERT", "lineNumber": 183, "content": "> string_validator 1.2.0 (was 1.1.0)"}, {"type": "INSERT", "lineNumber": 184, "content": "> synchronized 3.4.0 (was 3.3.0+3)"}, {"type": "INSERT", "lineNumber": 185, "content": "> term_glyph 1.2.2 (was 1.2.1)"}, {"type": "INSERT", "lineNumber": 186, "content": "> test_api 0.7.6 (was 0.7.2) (0.7.7 available)"}, {"type": "INSERT", "lineNumber": 187, "content": "> time 2.1.5 (was 2.1.4)"}, {"type": "INSERT", "lineNumber": 188, "content": "> timing 1.0.2 (was 1.0.1)"}, {"type": "INSERT", "lineNumber": 189, "content": "> typed_data 1.4.0 (was 1.3.2)"}, {"type": "INSERT", "lineNumber": 190, "content": "> url_launcher 6.3.2 (was 6.3.1)"}, {"type": "INSERT", "lineNumber": 191, "content": "> url_launcher_android 6.3.18 (was 6.3.12)"}, {"type": "INSERT", "lineNumber": 192, "content": "> url_launcher_ios 6.3.4 (was 6.3.1)"}, {"type": "INSERT", "lineNumber": 193, "content": "> url_launcher_linux 3.2.1 (was 3.2.0)"}, {"type": "INSERT", "lineNumber": 194, "content": "> url_launcher_macos 3.2.3 (was 3.2.1)"}, {"type": "INSERT", "lineNumber": 195, "content": "> url_launcher_web 2.4.1 (was 2.3.3)"}, {"type": "INSERT", "lineNumber": 196, "content": "> url_launcher_windows 3.1.4 (was 3.1.2)"}, {"type": "INSERT", "lineNumber": 197, "content": "> vector_graphics 1.1.19 (was 1.1.11+1)"}, {"type": "INSERT", "lineNumber": 198, "content": "> vector_graphics_codec 1.1.13 (was 1.1.11+1)"}, {"type": "INSERT", "lineNumber": 199, "content": "> vector_graphics_compiler 1.1.19 (was 1.1.11+1)"}, {"type": "INSERT", "lineNumber": 200, "content": "> vector_math 2.2.0 (was 2.1.4)"}, {"type": "INSERT", "lineNumber": 201, "content": "> video_player 2.10.0 (was 2.9.2)"}, {"type": "INSERT", "lineNumber": 202, "content": "> video_player_android 2.8.13 (was 2.7.13)"}, {"type": "INSERT", "lineNumber": 203, "content": "> video_player_avfoundation 2.8.4 (was 2.6.2)"}, {"type": "INSERT", "lineNumber": 204, "content": "> video_player_platform_interface 6.4.0 (was 6.2.3)"}, {"type": "INSERT", "lineNumber": 205, "content": "> video_player_web 2.4.0 (was 2.3.2)"}, {"type": "INSERT", "lineNumber": 206, "content": "> vm_service 15.0.2 (was 14.2.5)"}, {"type": "INSERT", "lineNumber": 207, "content": "> watcher 1.1.3 (was 1.1.0)"}, {"type": "INSERT", "lineNumber": 208, "content": "> web 1.1.1 (was 1.1.0)"}, {"type": "INSERT", "lineNumber": 209, "content": "> web_socket 1.0.1 (was 0.1.6)"}, {"type": "INSERT", "lineNumber": 210, "content": "> web_socket_channel 3.0.3 (was 3.0.1)"}, {"type": "INSERT", "lineNumber": 211, "content": "! win32 5.14.0 (overridden)"}, {"type": "INSERT", "lineNumber": 212, "content": "> xml 6.6.1 (was 6.5.0)"}, {"type": "INSERT", "lineNumber": 213, "content": "> yaml 3.1.3 (was 3.1.2)"}, {"type": "INSERT", "lineNumber": 214, "content": "These packages are no longer being depended on:"}, {"type": "INSERT", "lineNumber": 215, "content": "- _macros 0.3.2 from sdk dart"}, {"type": "INSERT", "lineNumber": 216, "content": "- js 0.7.1"}, {"type": "INSERT", "lineNumber": 217, "content": "- macros 0.1.2-main.4"}, {"type": "INSERT", "lineNumber": 218, "content": "- timeago 3.7.0"}, {"type": "INSERT", "lineNumber": 219, "content": "Changed 171 dependencies in `/Users/<USER>/Flutter-Projects/Ajory/dropx`!"}, {"type": "INSERT", "lineNumber": 220, "content": "9 packages have newer versions incompatible with dependency constraints."}, {"type": "INSERT", "lineNumber": 221, "content": "Try `flutter pub outdated` for more information."}, {"type": "INSERT", "lineNumber": 222, "content": "xrgouda@Amrs-MacBook-Air packages % "}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/packages/xr_helper/pubspec.yaml": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/packages/xr_helper/pubspec.yaml", "baseContent": "name: xr_helper\ndescription: \"A new Flutter package project.\"\nversion: 0.0.1\nhomepage:\n\nenvironment:\n  sdk: '>=3.2.0 <4.0.0'\n  flutter: \">=1.17.0\"\n\ndependencies:\n  flutter:\n    sdk: flutter\n\n  cupertino_icons: ^1.0.6\n\n  #? URL Launcher (Make calls, send SMS, open URLs)\n  url_launcher: ^6.2.1\n\n  #? Local Storage\n  get_storage: ^2.1.1\n\n  #? Remote (HTTP Requests)\n  http: ^1.1.2\n\n  #? Sized Box (Height & Width)\n  gap: ^3.0.1\n\n  #? Intl (Date Formatting)\n  intl: ^0.20.2\n\n  #? Theme (Fonts)\n  google_fonts: ^6.1.0\n\n  #? Alerts\n  another_flushbar: ^1.12.30\n  fluttertoast: ^8.2.8\n\n  #? Firebase\n  firebase_core: ^4.1.0\n  firebase_messaging: ^16.0.1\n\n  #? Hooks\n  flutter_hooks: ^0.21.3+1\n\n  #? UI\n  cached_network_image:\n  shimmer: ^3.0.0\n  flutter_form_builder: ^10.2.0\n\n  #? Logger (Log messages with colors)\n  logger: ^2.0.2+1\n\n  #? State Management\n  provider: ^6.1.1\n\n\ndev_dependencies:\n  flutter_test:\n    sdk: flutter\n  flutter_lints: ^6.0.0\n\nflutter:\n", "baseTimestamp": 1756912724040}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/pubspec.yaml": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/pubspec.yaml", "baseContent": "name: dropx\ndescription: \"DropX Application.\"\n\npublish_to: 'none'\n\nscripts:\n  build_runner: dart run build_runner build --delete-conflicting-outputs\n  launch_icons: flutter pub run flutter_launcher_icons:main\n  launch_splash: flutter pub run flutter_native_splash:create\n\nversion: 1.0.0+0\n\nenvironment:\n  sdk: '>=3.0.6 <4.0.0'\n\ndependencies:\n  flutter:\n    sdk: flutter\n\n  #? Localization\n  flutter_localizations:\n    sdk: flutter\n\n  # * Helper Package *\n  xr_helper:\n    path: packages/xr_helper\n\n  cupertino_icons: ^1.0.6\n\n  #? State Management\n  flutter_riverpod: ^2.4.9\n  riverpod: ^2.4.9\n  hooks_riverpod: ^2.4.9\n  flutter_hooks: ^0.21.3+1\n  equatable:\n\n  #? responsive\n  flutter_screenutil: ^5.9.3\n\n\n  #? Google Fonts\n  google_fonts:\n\n  #? Assets\n  lottie: ^3.0.0\n  flutter_svg:\n  smooth_page_indicator: ^1.2.0+3\n\n  #? form Builder\n  flutter_form_builder: ^10.2.0\n  form_builder_image_picker: ^4.1.0\n  form_builder_validators: ^11.0.0\n\n\n  #? Utils\n  no_context_navigation: ^3.0.0\n  fluttertoast: ^8.2.8\n  permission_handler: ^12.0.1\n\n  #? Location\n  google_maps_flutter: ^2.9.0\n  geolocator: ^14.0.2\n\n  #? Firebase\n  firebase_core: ^4.1.0\n\n  #? UI\n  loading_animation_widget: ^1.3.0\n  carousel_slider: ^5.0.0\n  multi_video_player: ^0.0.5\n  font_awesome_flutter: ^10.7.0\n  flutter_staggered_grid_view: ^0.7.0\n  chatview: ^2.3.0\n\ndependency_overrides:\n  archive: ^3.6.1\n  win32: ^5.5.4\n\n\ndev_dependencies:\n  flutter_test:\n    sdk: flutter\n\n  flutter_lints: ^6.0.0\n  flutter_launcher_icons: ^0.14.4\n  flutter_native_splash: ^2.3.9\n  build_runner:\n  flutter_gen_runner:\n\n#? dart run flutter_launcher_icons:main\nflutter_launcher_icons:\n  android: true\n  ios: true\n  remove_alpha_ios: true\n  image_path: \"assets/images/app_icon.png\"\n  adaptive_icon_background: \"assets/images/app_icon.png\"\n  adaptive_icon_foreground: \"assets/images/app_icon.png\"\n  adaptive_icon_foreground_inset: 16\n\n# ? dart run flutter_native_splash:create\nflutter_native_splash:\n  android: true\n  ios: true\n  web: false\n  fullscreen: false\n  color: '#ffffff'\n  image: 'assets/images/app_icon.png'\n  android_12:\n    color: '#ffffff'\n    image: 'assets/images/app_icon.png'\n\nflutter:\n  uses-material-design: true\n  generate: true\n\n  assets:\n    - assets/images/\n#    - assets/animated/\n#    - assets/icons/\n\nflutter_intl:\n  enabled: true\n\n\nflutter_gen:\n  output: lib/generated", "baseTimestamp": 1756912796821}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/app/src/main/AndroidManifest.xml": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/app/src/main/AndroidManifest.xml", "baseContent": "<manifest xmlns:android=\"http://schemas.android.com/apk/res/android\">\n\n    <uses-permission android:name=\"android.permission.INTERNET\"/>\n    <uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>\n    <uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>\n\n    <application\n            android:label=\"DropX\"\n            android:name=\"${applicationName}\"\n            android:icon=\"@mipmap/ic_launcher\">\n\n\n        <meta-data\n                android:name=\"com.google.android.geo.API_KEY\"\n                android:value=\"AIzaSyCTUQH9tBTBxjAJSpDmDEVllVhWqmR0nR8\"/>\n\n        <activity\n                android:name=\".MainActivity\"\n                android:exported=\"true\"\n                android:launchMode=\"singleTop\"\n                android:taskAffinity=\"\"\n                android:theme=\"@style/LaunchTheme\"\n                android:configChanges=\"orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode\"\n                android:hardwareAccelerated=\"true\"\n                android:windowSoftInputMode=\"adjustResize\">\n\n\n            <!-- Specifies an Android theme to apply to this Activity as soon as\n                 the Android process has started. This theme is visible to the user\n                 while the Flutter UI initializes. After that, this theme continues\n                 to determine the Window background behind the Flutter UI. -->\n            <meta-data\n                    android:name=\"io.flutter.embedding.android.NormalTheme\"\n                    android:resource=\"@style/NormalTheme\"\n            />\n            <intent-filter>\n                <action android:name=\"android.intent.action.MAIN\"/>\n                <category android:name=\"android.intent.category.LAUNCHER\"/>\n            </intent-filter>\n        </activity>\n        <!-- Don't delete the meta-data below.\n             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->\n        <meta-data\n                android:name=\"flutterEmbedding\"\n                android:value=\"2\"/>\n    </application>\n    <!-- Required to query activities that can process text, see:\n         https://developer.android.com/training/package-visibility and\n         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.\n\n         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->\n    <queries>\n        <intent>\n            <action android:name=\"android.intent.action.PROCESS_TEXT\"/>\n            <data android:mimeType=\"text/plain\"/>\n        </intent>\n    </queries>\n</manifest>\n", "baseTimestamp": 1756913020326}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/consts/app_constants.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/consts/app_constants.dart", "baseContent": "import 'package:flutter/material.dart' show Locale, LocalizationsDelegate;\nimport 'package:flutter_localizations/flutter_localizations.dart';\nimport 'package:dropx/generated/l10n.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass AppConsts {\n  static const String appName = 'DropX';\n  static const Locale locale = Locale('en');\n\n  static const List<Locale> supportedLocales = [\n    locale,\n    Locale('ar'),\n  ];\n\n  static bool get isEnglish =>\n      GetStorageService.getData(key: LocalKeys.language) == 'en';\n\n  static const List<LocalizationsDelegate> localizationsDelegates = [\n    S.delegate,\n    GlobalMaterialLocalizations.delegate,\n    GlobalCupertinoLocalizations.delegate,\n    GlobalWidgetsLocalizations.delegate,\n  ];\n\n  //? Test Login\n  static const String testEmail = 'admin';\n  static const String testPass = 'test@123';\n}\n", "baseTimestamp": 1756913096385, "deltas": [{"timestamp": 1756968383891, "changes": [{"type": "MODIFY", "lineNumber": 11, "content": "    locale,", "oldContent": "    locale,"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/widgets/doctors_list.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/widgets/doctors_list.widget.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport 'see_all_button.widget.dart';\n\nclass DoctorsListWidget extends StatelessWidget {\n  const DoctorsListWidget({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    return Column(\n      children: [\n        Row(\n          children: [\n            Text(context.tr.doctors, style: AppTextStyles.title),\n            const Spacer(),\n            SeeAllButtonWidget(\n              onPressed: () {\n                const DoctorsScreen().navigate;\n              },\n            )\n          ],\n        ),\n        AppGaps.gap8,\n        SizedBox(\n          height: 110.h,\n          child: ListView.separated(\n              scrollDirection: Axis.horizontal,\n              itemBuilder: (context, index) => Column(\n                    children: [\n                      CircleAvatar(\n                          radius: 40.r,\n                          backgroundColor: ColorManager.lightGrey,\n                          child: const Padding(\n                              padding: EdgeInsets.all(AppSpaces.padding4),\n                              child: BaseCachedImage(\n                                'https://t4.ftcdn.net/jpg/02/60/04/09/360_F_260040900_oO6YW1sHTnKxby4GcjCvtypUCWjnQRg5.jpg',\n                                radius: AppRadius.radius100,\n                                height: 100,\n                                fit: BoxFit.cover,\n                              ))),\n                      AppGaps.gap4,\n                      Text(\n                        'Amr Gouda',\n                        style: AppTextStyles.labelLarge,\n                      ),\n                    ],\n                  ),\n              separatorBuilder: (context, index) => AppGaps.gap16,\n              itemCount: 6),\n        )\n      ],\n    );\n  }\n}\n", "baseTimestamp": 1756930315842, "deltas": [{"timestamp": 1756930319151, "changes": [{"type": "DELETE", "lineNumber": 15, "oldContent": "        Row("}, {"type": "DELETE", "lineNumber": 16, "oldContent": "          children: ["}, {"type": "DELETE", "lineNumber": 17, "oldContent": "            Text(context.tr.doctors, style: AppTextStyles.title),"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "            const Spacer(),"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "            SeeAllButtonWidget("}, {"type": "DELETE", "lineNumber": 20, "oldContent": "              onPressed: () {"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "                const Doctors<PERSON><PERSON>en().navigate;"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "              },"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "            )"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "          ],"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "        ),"}, {"type": "INSERT", "lineNumber": 15, "content": "     "}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/home.screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/home.screen.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:dropx/src/screens/home/<USER>/widgets/home_slider.widget.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport 'widgets/animals_list.widget.dart';\nimport 'widgets/doctors_list.widget.dart';\nimport 'widgets/products_list.widget.dart';\n\nclass HomeScreen extends StatelessWidget {\n  const HomeScreen({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(\n        backgroundColor: ColorManager.white,\n        surfaceTintColor: ColorManager.white,\n        centerTitle: false,\n        title: Text(\n          context.tr.welcomeWithName('Amr'),\n          style: AppTextStyles.title,\n        ),\n        leading: Padding(\n          padding: const EdgeInsets.all(AppSpaces.padding8),\n          child: CircleAvatar(\n            backgroundColor: ColorManager.lightPrimaryColor,\n            radius: 40.r,\n            child: ClipOval(\n              child: BaseCachedImage(\n                height: 80.h,\n                width: 80.w,\n                'https://cdn4.iconfinder.com/data/icons/gamer-player-3d-avatars-3d-illustration-pack/512/19_Man_T-Shirt_Suit.png',\n                fit: BoxFit.cover, // Ensures the image covers the circular area\n              ),\n            ),\n          ),\n        ),\n        actions: [\n          IconButton(\n              onPressed: () {},\n              icon: const CircleAvatar(\n                  backgroundColor: ColorManager.primaryColor,\n                  child: Icon(Icons.notifications))),\n        ],\n      ),\n      body: ListView(\n        padding:\n            const EdgeInsets.symmetric(horizontal: AppSpaces.screenPadding),\n        children: const [\n          AppGaps.gap16,\n\n          // * Home Slider\n          HomeSliderWidget(),\n\n          AppGaps.gap16,\n\n          // * Animals\n          AnimalsListWidget(),\n\n          AppGaps.gap24,\n\n          // * Products\n          ProductsListWidget(),\n\n          AppGaps.gap24,\n\n          // * Stores\n          HomeStoresListWidget(),\n\n          AppGaps.gap24,\n\n          // * Doctors\n          DoctorsListWidget(),\n\n          AppGaps.gap24,\n        ],\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1756930331759, "deltas": [{"timestamp": 1756930334911, "changes": [{"type": "DELETE", "lineNumber": 69, "oldContent": "          // * Stores"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "          HomeStoresListWidget(),"}]}, {"timestamp": 1756930336991, "changes": [{"type": "DELETE", "lineNumber": 67, "oldContent": "          AppGaps.gap24,"}]}, {"timestamp": 1756931515216, "changes": [{"type": "DELETE", "lineNumber": 67, "oldContent": ""}, {"type": "DELETE", "lineNumber": 68, "oldContent": ""}]}, {"timestamp": 1756931533740, "changes": [{"type": "INSERT", "lineNumber": 67, "content": ""}, {"type": "INSERT", "lineNumber": 68, "content": ""}]}, {"timestamp": 1756931537596, "changes": [{"type": "DELETE", "lineNumber": 67, "oldContent": ""}, {"type": "DELETE", "lineNumber": 70, "oldContent": ""}]}, {"timestamp": 1756931593376, "changes": [{"type": "DELETE", "lineNumber": 8, "oldContent": "import 'widgets/doctors_list.widget.dart';"}]}, {"timestamp": 1756931596885, "changes": [{"type": "DELETE", "lineNumber": 66, "oldContent": "          AppGaps.gap24,"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "          // * Doctors"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "          DoctorsListWidget(),"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/main_screen/view/main.screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/main_screen/view/main.screen.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/shared/widgets/navigations/bottom_nav_bar.widget.dart';\nimport 'package:dropx/src/screens/home/<USER>/home.screen.dart';\n\nimport '../../../core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';\n\nclass MainScreen extends ConsumerWidget {\n  const MainScreen({super.key});\n\n  @override\n  Widget build(BuildContext context, WidgetRef ref) {\n    final currentIndex = ref.watch(bottomNavigationControllerProvider);\n    return Scaffold(\n      body: _SelectedScreen(currentIndex: currentIndex),\n      bottomNavigationBar: const BottomNavBarWidget(),\n    );\n  }\n}\n\nString selectedTitle(int currentIndex, BuildContext context) {\n  switch (currentIndex) {\n    case 0:\n      return context.tr.home;\n    case 1:\n      return context.tr.reels;\n    case 2:\n      return context.tr.doctors;\n    case 3:\n      return context.tr.shops;\n  }\n\n  return context.tr.home;\n}\n\nclass _SelectedScreen extends StatelessWidget {\n  final int currentIndex;\n\n  const _SelectedScreen({required this.currentIndex});\n\n  @override\n  Widget build(BuildContext context) {\n    switch (currentIndex) {\n      case 0:\n        return const HomeScreen();\n      case 1:\n        return const ReelsScreen();\n    }\n    return const SizedBox.shrink();\n  }\n}\n", "baseTimestamp": 1756930341284, "deltas": [{"timestamp": 1756930344323, "changes": [{"type": "DELETE", "lineNumber": 46, "oldContent": "      case 1:"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "        return const <PERSON><PERSON>S<PERSON><PERSON>();"}, {"type": "INSERT", "lineNumber": 46, "content": ""}]}, {"timestamp": 1756930639830, "changes": [{"type": "MODIFY", "lineNumber": 39, "content": "  const _SelectedScreen({", "oldContent": "  const _SelectedScreen({required this.currentIndex});"}, {"type": "INSERT", "lineNumber": 40, "content": "    required this.currentIndex,"}, {"type": "INSERT", "lineNumber": 41, "content": "  });"}, {"type": "DELETE", "lineNumber": 46, "oldContent": ""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/widgets/products_list.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/home/<USER>/widgets/products_list.widget.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport 'see_all_button.widget.dart';\n\nclass ProductsListWidget extends StatelessWidget {\n  const ProductsListWidget({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    final width = 140.w;\n\n    return Column(\n      children: [\n        Row(\n          children: [\n            Text(context.tr.products, style: AppTextStyles.title),\n            const Spacer(),\n            SeeAllButtonWidget(\n              onPressed: () {\n                const ProductsScreen().navigate;\n              },\n            )\n          ],\n        ),\n        AppGaps.gap8,\n        SizedBox(\n          height: 110.h,\n          child: ListView.separated(\n              scrollDirection: Axis.horizontal,\n              itemBuilder: (context, index) => Stack(\n                    children: [\n                      BaseCachedImage(\n                        'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTwyXeKDN29AmZgZPLS7n0Bepe8QmVappBwZCeA3XWEbWNdiDFB',\n                        width: width,\n                        height: 110.h,\n                        radius: AppRadius.radius20,\n                      ),\n                      Container(\n                        width: width,\n                        padding: const EdgeInsets.symmetric(\n                          horizontal: AppSpaces.padding12,\n                          vertical: AppSpaces.padding8,\n                        ),\n                        alignment: Alignment.bottomLeft,\n                        decoration: BoxDecoration(\n                          color: Colors.black.withOpacity(0.2),\n                          borderRadius:\n                              BorderRadius.circular(AppSpaces.padding20),\n                        ),\n                        child: Row(\n                          mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                          crossAxisAlignment: CrossAxisAlignment.end,\n                          children: [\n                            Expanded(\n                              child: Column(\n                                crossAxisAlignment: CrossAxisAlignment.start,\n                                mainAxisSize: MainAxisSize.min,\n                                children: [\n                                  Expanded(\n                                    child: Align(\n                                      alignment: Alignment.bottomLeft,\n                                      child: Text(\n                                        'Dogs Food',\n                                        style: AppTextStyles.title\n                                            .copyWith(color: Colors.white),\n                                      ),\n                                    ),\n                                  ),\n                                  Text(\n                                    '\\$20',\n                                    style: AppTextStyles.title.copyWith(\n                                        color: ColorManager.primaryColor),\n                                  ),\n                                ],\n                              ),\n                            ),\n                            CircleAvatar(\n                              backgroundColor: ColorManager.primaryColor,\n                              radius: 14.r,\n                              child: const Icon(\n                                Icons.add_shopping_cart_outlined,\n                                color: Colors.white,\n                                size: 18,\n                              ),\n                            ),\n                          ],\n                        ),\n                      ),\n                    ],\n                  ),\n              separatorBuilder: (context, index) => AppGaps.gap16,\n              itemCount: 6),\n        )\n      ],\n    );\n  }\n}\n", "baseTimestamp": 1756930371310, "deltas": [{"timestamp": 1756930376171, "changes": [{"type": "INSERT", "lineNumber": 6, "content": "import '../../../stores/view/products_screen/products.screen.dart';"}]}, {"timestamp": 1756930389063, "changes": [{"type": "MODIFY", "lineNumber": 6, "content": "import '../../../stores/view/products_screen/stores.screen.dart';", "oldContent": "import '../../../stores/view/products_screen/products.screen.dart';"}]}]}, "/a.dummy": {"filePath": "/a.dummy", "baseContent": "stores.screen.dart", "baseTimestamp": 1756930385283}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/products_screen/products.screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/products_screen/products.screen.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/shared/widgets/app_bar/base_appbar.dart';\nimport 'package:dropx/src/core/shared/widgets/search_bar_widget/search_bar.widget.dart';\nimport 'package:dropx/src/screens/products/view/products_screen/widgets/products_grid.widget.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass ProductsScreen extends StatelessWidget {\n  const ProductsScreen({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: BaseAppBar(\n        title: context.tr.stores,\n      ),\n      body: Padding(\n        padding:\n            const EdgeInsets.symmetric(horizontal: AppSpaces.screenPadding),\n        child: Column(\n          children: [\n            AppGaps.gap12,\n\n            // * Search Bar\n            SearchBarWidget(\n              label: context.tr.searchForProducts,\n            ),\n\n            AppGaps.gap12,\n\n            // * Products Grid\n            const Expanded(child: ProductsGridWidget()),\n          ],\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1756930392570, "deltas": [{"timestamp": 1756930397625, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "import 'package:dropx/src/screens/stores/view/products_screen/widgets/products_grid.widget.dart';"}]}, {"timestamp": 1756930435762, "changes": [{"type": "DELETE", "lineNumber": 5, "oldContent": "import 'package:dropx/src/screens/products/view/products_screen/widgets/products_grid.widget.dart';"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/product_details_screen/store_details.screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/product_details_screen/store_details.screen.dart", "baseContent": "import 'package:flutter/cupertino.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass StoreDetailsScreen extends StatelessWidget {\n  const StoreDetailsScreen({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      body: Column(\n        children: [\n          // * Store Top Section (Image - Fav - Back Arrow)\n          const StoreTopSectionDetailsWidget(),\n\n          AppGaps.gap24,\n\n          // * Store Tabs\n          Expanded(\n            child: ListView(\n              padding:\n                  const EdgeInsets.symmetric(horizontal: AppSpaces.padding12),\n              children: [\n                // about the store\n                Row(\n                  mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                  children: [\n                    // * About\n                    Column(\n                      children: [\n                        Container(\n                          width: 80,\n                          height: 60.h,\n                          decoration: BoxDecoration(\n                            color: ColorManager.primaryColor.withOpacity(.8),\n                            borderRadius:\n                                BorderRadius.circular(AppRadius.radius20),\n                          ),\n                          child: const Icon(\n                            CupertinoIcons.info,\n                            color: ColorManager.white,\n                            size: 40,\n                          ),\n                        ),\n                        AppGaps.gap8,\n                        Text(\n                          context.tr.about,\n                          style: AppTextStyles.title.copyWith(\n                            fontSize: 16,\n                            color: ColorManager.primaryColor,\n                          ),\n                        ),\n                      ],\n                    ),\n\n                    // * Products\n                    Column(\n                      children: [\n                        Container(\n                          width: 80,\n                          height: 60.h,\n                          decoration: BoxDecoration(\n                            border: Border.all(\n                              color: ColorManager.primaryColor.withOpacity(.8),\n                              width: 2,\n                            ),\n                            borderRadius:\n                                BorderRadius.circular(AppRadius.radius20),\n                          ),\n                          child: const Icon(\n                            Icons.shopping_bag_outlined,\n                            color: ColorManager.primaryColor,\n                            size: 40,\n                          ),\n                        ),\n                        AppGaps.gap8,\n                        Text(\n                          context.tr.products,\n                          style: AppTextStyles.title.copyWith(\n                            fontSize: 16,\n                            fontWeight: FontWeight.normal,\n                            color: ColorManager.primaryColor,\n                          ),\n                        ),\n                      ],\n                    ),\n\n                    // * Reels\n                    Column(\n                      children: [\n                        Container(\n                          width: 80,\n                          height: 60.h,\n                          decoration: BoxDecoration(\n                            border: Border.all(\n                              color: ColorManager.primaryColor.withOpacity(.8),\n                              width: 2,\n                            ),\n                            borderRadius:\n                                BorderRadius.circular(AppRadius.radius20),\n                          ),\n                          child: const Icon(\n                            Icons.video_collection_outlined,\n                            color: ColorManager.primaryColor,\n                            size: 40,\n                          ),\n                        ),\n                        AppGaps.gap8,\n                        Text(\n                          'Reels',\n                          style: AppTextStyles.title.copyWith(\n                            fontSize: 16,\n                            fontWeight: FontWeight.normal,\n                            color: ColorManager.primaryColor,\n                          ),\n                        ),\n                      ],\n                    ),\n\n                    // * Animals\n                    Column(\n                      children: [\n                        Container(\n                          width: 80,\n                          height: 60.h,\n                          decoration: BoxDecoration(\n                            border: Border.all(\n                              color: ColorManager.primaryColor.withOpacity(.8),\n                              width: 2,\n                            ),\n                            borderRadius:\n                                BorderRadius.circular(AppRadius.radius20),\n                          ),\n                          child: const Icon(\n                            Icons.pets_outlined,\n                            color: ColorManager.primaryColor,\n                            size: 40,\n                          ),\n                        ),\n                        AppGaps.gap8,\n                        Text(\n                          context.tr.animals,\n                          style: AppTextStyles.title.copyWith(\n                            fontSize: 16,\n                            fontWeight: FontWeight.normal,\n                            color: ColorManager.primaryColor,\n                          ),\n                        ),\n                      ],\n                    ),\n                  ],\n                ),\n              ],\n            ),\n          ),\n        ],\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1756930405920, "deltas": [{"timestamp": 1756930408090, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "import 'package:dropx/src/screens/stores/view/product_details_screen/widgets/store_top_section_details.widget.dart';"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/products_screen/widgets/products_grid.widget.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/stores/view/products_screen/widgets/products_grid.widget.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:dropx/src/core/shared/extensions/riverpod_extensions.dart';\nimport 'package:dropx/src/core/shared/widgets/lists/base_list.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:dropx/src/screens/stores/providers/store.providers.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass ProductsGridWidget extends ConsumerWidget {\n  const ProductsGridWidget({super.key});\n\n  @override\n  Widget build(BuildContext context, WidgetRef ref) {\n    final storesFuture = ref.watch(getStoresFutureProvider);\n\n    return BaseList.grid(\n      data: const [0, 1, 2, 3, 4],\n      crossAxisCount: 2,\n      mainAxisSpacing: AppSpaces.padding16,\n      itemBuilder: (context, index) => const ProductCard(),\n      separatorGap: AppGaps.gap16,\n    );\n\n    return storesFuture.get(\n      data: (stores) {\n        return BaseList(\n          data: stores,\n          isGrid: true,\n          itemBuilder: (context, index) => GestureDetector(\n            onTap: () {\n              const StoreDetailsScreen().navigate;\n            },\n            child: Stack(\n              children: [\n                BaseCachedImage(\n                  'https://static.wixstatic.com/media/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png/v1/fill/w_614,h_614,al_c,lg_1,q_90/0fc321_b46df26bf3fb4af59452459f29e56f71~mv2.png',\n                  height: 120.h,\n                  width: 220.w,\n                  radius: AppRadius.radius20,\n                ),\n                Container(\n                  width: 220.w,\n                  padding: const EdgeInsets.symmetric(\n                      horizontal: AppSpaces.padding12,\n                      vertical: AppSpaces.padding8),\n                  alignment: Alignment.bottomCenter,\n                  decoration: BoxDecoration(\n                      color: Colors.black.withOpacity(0.2),\n                      borderRadius: BorderRadius.circular(AppRadius.radius20)),\n                  child: Row(\n                    mainAxisAlignment: MainAxisAlignment.spaceBetween,\n                    children: [\n                      Text(\n                        'Pet Store',\n                        style: AppTextStyles.whiteTitle.copyWith(\n                          fontSize: 20,\n                        ),\n                      ),\n                      const CircleAvatar(\n                        radius: 16,\n                        backgroundColor: ColorManager.primaryColor,\n                        child: Icon(\n                          Icons.arrow_forward_ios,\n                          color: Colors.white,\n                          size: 18,\n                        ),\n                      )\n                    ],\n                  ),\n                )\n              ],\n            ),\n          ),\n          separatorGap: AppGaps.gap16,\n        );\n      },\n    );\n  }\n}\n", "baseTimestamp": 1756930414434, "deltas": [{"timestamp": 1756930418181, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "import 'package:dropx/src/screens/stores/view/products_screen/widgets/product_card.dart';"}]}, {"timestamp": 1756930420500, "changes": [{"type": "DELETE", "lineNumber": 17, "oldContent": "    return BaseList.grid("}, {"type": "DELETE", "lineNumber": 18, "oldContent": "      data: const [0, 1, 2, 3, 4],"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "      crossAxisCount: 2,"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "      mainAxisSpacing: AppSpaces.padding16,"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "      itemBuilder: (context, index) => const ProductCard(),"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "      separatorGap: AppGaps.gap16,"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 24, "oldContent": ""}]}, {"timestamp": *************, "changes": [{"type": "INSERT", "lineNumber": 10, "content": "import '../../product_details_screen/store_details.screen.dart';"}, {"type": "INSERT", "lineNumber": 11, "content": ""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/l10n/intl_en.arb": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/l10n/intl_en.arb", "baseContent": "{\n  \"login\": \"تسجيل الدخول\",\n  \"register\": \"تسجيل\",\n  \"email\": \"البريد الإلكتروني\",\n  \"dontHaveAnAccount\": \"ليس لديك حساب؟\",\n  \"haveAnAccount\": \"لديك حساب؟\",\n  \"password\": \"كلمة المرور\",\n  \"confirmPassword\": \"تأكيد كلمة المرور\",\n  \"forgotPassword\": \"هل نسيت كلمة المرور؟\",\n  \"resetPassword\": \"إعادة تعيين كلمة المرور\",\n  \"mobileNumber\": \"رقم الجوال\",\n  \"loginWithYourAccountNow\": \"سجل الدخول بحسابك الآن!\",\n  \"registerWithYourAccountNow\": \"سجل بحسابك الآن!\",\n  \"skip\": \"تخطي\",\n  \"stores\": \"المتاجر\",\n  \"fullName\": \"الاسم الكامل\",\n  \"registerAsStore\": \"التسجيل كمتجر؟\",\n  \"registerAsDoctor\": \"التسجيل كطبيب؟\",\n  \"home\": \"الرئيسية\",\n  \"reels\": \"ريلز\",\n  \"shops\": \"المحلات\",\n  \"doctors\": \"الأطباء\",\n  \"seeAll\": \"عرض الكل\",\n  \"welcomeWithName\": \"مرحبًا، {name}\",\n  \"animals\": \"الحيوانات\",\n  \"products\": \"المنتجات\",\n  \"about\": \"حول\",\n  \"onBoardingTitle1\": \"اعثر على حيوانك المثالي\",\n  \"onBoardingTitle2\": \"استكشف أفضل متاجر الحيوانات\",\n  \"onBoardingTitle3\": \"اعتنِ بصحة حيوانك الأليف\",\n", "baseTimestamp": *************, "deltas": [{"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 29, "oldContent": "  \"onBoardingTitle3\": \"اعتنِ بصحة حيوانك الأليف\","}, {"type": "DELETE", "lineNumber": 30, "oldContent": ""}, {"type": "INSERT", "lineNumber": 29, "content": "  \"onBoardingTitle3\": \"اعتنِ بصحة حيوانك الأليف\""}, {"type": "INSERT", "lineNumber": 30, "content": "}"}]}, {"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 1, "oldContent": "  \"login\": \"تسجيل الدخول\","}, {"type": "DELETE", "lineNumber": 2, "oldContent": "  \"register\": \"تسجيل\","}, {"type": "DELETE", "lineNumber": 3, "oldContent": "  \"email\": \"ال<PERSON><PERSON><PERSON><PERSON> الإلكتروني\","}, {"type": "DELETE", "lineNumber": 4, "oldContent": "  \"dontHaveAnAccount\": \"ليس لديك حساب؟\","}, {"type": "DELETE", "lineNumber": 5, "oldContent": "  \"haveAnAccount\": \"لديك حساب؟\","}, {"type": "DELETE", "lineNumber": 6, "oldContent": "  \"password\": \"كلمة المرور\","}, {"type": "DELETE", "lineNumber": 7, "oldContent": "  \"confirmPassword\": \"تأكيد كلمة المرور\","}, {"type": "DELETE", "lineNumber": 8, "oldContent": "  \"forgotPassword\": \"هل نسيت كلمة المرور؟\","}, {"type": "DELETE", "lineNumber": 9, "oldContent": "  \"resetPassword\": \"إعادة تعيين كلمة المرور\","}, {"type": "DELETE", "lineNumber": 10, "oldContent": "  \"mobileNumber\": \"رقم الجوال\","}, {"type": "DELETE", "lineNumber": 11, "oldContent": "  \"loginWithYourAccountNow\": \"سجل الدخول بحسابك الآن!\","}, {"type": "DELETE", "lineNumber": 12, "oldContent": "  \"registerWithYourAccountNow\": \"سجل بحسابك الآن!\","}, {"type": "DELETE", "lineNumber": 13, "oldContent": "  \"skip\": \"تخطي\","}, {"type": "DELETE", "lineNumber": 14, "oldContent": "  \"stores\": \"المتاجر\","}, {"type": "DELETE", "lineNumber": 15, "oldContent": "  \"fullName\": \"الاسم الكامل\","}, {"type": "DELETE", "lineNumber": 16, "oldContent": "  \"registerAsStore\": \"التسجيل كمتجر؟\","}, {"type": "DELETE", "lineNumber": 17, "oldContent": "  \"registerAsDoctor\": \"التسجيل كطبيب؟\","}, {"type": "DELETE", "lineNumber": 18, "oldContent": "  \"home\": \"الرئيسية\","}, {"type": "DELETE", "lineNumber": 19, "oldContent": "  \"reels\": \"ريلز\","}, {"type": "DELETE", "lineNumber": 20, "oldContent": "  \"shops\": \"المحلات\","}, {"type": "DELETE", "lineNumber": 21, "oldContent": "  \"doctors\": \"الأطباء\","}, {"type": "DELETE", "lineNumber": 22, "oldContent": "  \"seeAll\": \"عرض الكل\","}, {"type": "DELETE", "lineNumber": 23, "oldContent": "  \"welcomeWithName\": \"مرحبًا، {name}\","}, {"type": "DELETE", "lineNumber": 24, "oldContent": "  \"animals\": \"الحيوانات\","}, {"type": "DELETE", "lineNumber": 25, "oldContent": "  \"products\": \"المنتجات\","}, {"type": "DELETE", "lineNumber": 26, "oldContent": "  \"about\": \"حول\","}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  \"onBoardingTitle1\": \"اعثر على حيوانك المثالي\","}, {"type": "DELETE", "lineNumber": 28, "oldContent": "  \"onBoardingTitle2\": \"استكشف أفضل متاجر الحيوانات\","}, {"type": "DELETE", "lineNumber": 29, "oldContent": "  \"onBoardingTitle3\": \"اعتنِ بصحة حيوانك الأليف\""}, {"type": "INSERT", "lineNumber": 1, "content": "  \"login\": \"Login\","}, {"type": "INSERT", "lineNumber": 2, "content": "  \"register\": \"Register\","}, {"type": "INSERT", "lineNumber": 3, "content": "  \"email\": \"Email\","}, {"type": "INSERT", "lineNumber": 4, "content": "  \"dontHaveAnAccount\": \"Don't have an account?\","}, {"type": "INSERT", "lineNumber": 5, "content": "  \"haveAnAccount\": \"Have an account?\","}, {"type": "INSERT", "lineNumber": 6, "content": "  \"password\": \"Password\","}, {"type": "INSERT", "lineNumber": 7, "content": "  \"confirmPassword\": \"Confirm Password\","}, {"type": "INSERT", "lineNumber": 8, "content": "  \"forgotPassword\": \"Forgot Password?\","}, {"type": "INSERT", "lineNumber": 9, "content": "  \"resetPassword\": \"Reset Password\","}, {"type": "INSERT", "lineNumber": 10, "content": "  \"mobileNumber\": \"Mobile Number\","}, {"type": "INSERT", "lineNumber": 11, "content": "  \"loginWithYourAccountNow\": \"Login with your account now !\","}, {"type": "INSERT", "lineNumber": 12, "content": "  \"registerWithYourAccountNow\": \"Register with your account now !\","}, {"type": "INSERT", "lineNumber": 13, "content": "  \"skip\": \"Ski<PERSON>\","}, {"type": "INSERT", "lineNumber": 14, "content": "  \"stores\": \"Stores\","}, {"type": "INSERT", "lineNumber": 15, "content": "  \"fullName\": \"Full Name\","}, {"type": "INSERT", "lineNumber": 16, "content": "  \"registerAsStore\": \"Register as Store?\","}, {"type": "INSERT", "lineNumber": 17, "content": "  \"registerAsDoctor\": \"Register as Doctor?\","}, {"type": "INSERT", "lineNumber": 18, "content": "  \"home\": \"Home\","}, {"type": "INSERT", "lineNumber": 19, "content": "  \"reels\": \"Reels\","}, {"type": "INSERT", "lineNumber": 20, "content": "  \"shops\": \"Shops\","}, {"type": "INSERT", "lineNumber": 21, "content": "  \"doctors\": \"Doctors\","}, {"type": "INSERT", "lineNumber": 22, "content": "  \"seeAll\": \"See All\","}, {"type": "INSERT", "lineNumber": 23, "content": "  \"welcomeWithName\": \"Welcome, {name}\","}, {"type": "INSERT", "lineNumber": 24, "content": "  \"animals\": \"Animals\","}, {"type": "INSERT", "lineNumber": 25, "content": "  \"products\": \"Products\","}, {"type": "INSERT", "lineNumber": 26, "content": "  \"about\": \"About\","}, {"type": "INSERT", "lineNumber": 27, "content": "  \"onBoardingTitle1\": \"Find Your Perfect Pet\","}, {"type": "INSERT", "lineNumber": 28, "content": "  \"onBoardingTitle2\": \"Explore Top Pet Stores\","}, {"type": "INSERT", "lineNumber": 29, "content": "  \"onBoardingTitle3\": \"Care for Your Pet's Health\","}, {"type": "INSERT", "lineNumber": 30, "content": "  \"onBoardingDescription1\": \"Discover a wide variety of pets to adopt and bring home the perfect companion.\","}, {"type": "INSERT", "lineNumber": 31, "content": "  \"onBoardingDescription2\": \"Browse through trusted pet stores and find the best products for your furry friends.\","}, {"type": "INSERT", "lineNumber": 32, "content": "  \"onBoardingDescription3\": \"Connect with professional veterinarians and ensure your pets receive the best care.\","}, {"type": "INSERT", "lineNumber": 33, "content": "  \"next\": \"Next\","}, {"type": "INSERT", "lineNumber": 34, "content": "  \"startNow\": \"Start Now\","}, {"type": "INSERT", "lineNumber": 35, "content": "  \"description\": \"Description\","}, {"type": "INSERT", "lineNumber": 36, "content": "  \"address\": \"Address\","}, {"type": "INSERT", "lineNumber": 37, "content": "  \"enter\": \"Enter\","}, {"type": "INSERT", "lineNumber": 38, "content": "  \"doctor<PERSON>ame\": \"Doctor Name\","}, {"type": "INSERT", "lineNumber": 39, "content": "  \"emailOptional\": \"Email (Optional)\","}, {"type": "INSERT", "lineNumber": 40, "content": "  \"doctor<PERSON><PERSON>\": \"Doctor <PERSON><PERSON>\","}, {"type": "INSERT", "lineNumber": 41, "content": "  \"save\": \"Save\","}, {"type": "INSERT", "lineNumber": 42, "content": "  \"submit\": \"Submit\","}, {"type": "INSERT", "lineNumber": 43, "content": "  \"pickImage\": \"Pick Image\","}, {"type": "INSERT", "lineNumber": 44, "content": "  \"locationPickedSuccessfully\": \"Location picked successfully\","}, {"type": "INSERT", "lineNumber": 45, "content": "  \"pickLocation\": \"Pick Location\","}, {"type": "INSERT", "lineNumber": 46, "content": "  \"tapToSelectLocation\": \"Tap to select location\","}, {"type": "INSERT", "lineNumber": 47, "content": "  \"changeLocation\": \"Change Location\","}, {"type": "INSERT", "lineNumber": 48, "content": "  \"noDataFound\": \"No data found\","}, {"type": "INSERT", "lineNumber": 49, "content": "  \"changeSocial\": \"Change Social\","}, {"type": "INSERT", "lineNumber": 50, "content": "  \"pleaseAddValidLink\": \"Please add valid link\","}, {"type": "INSERT", "lineNumber": 51, "content": "  \"socialMedia\": \"Social Media\","}, {"type": "INSERT", "lineNumber": 52, "content": "  \"doctorBackground\": \"Doctor Background\","}, {"type": "INSERT", "lineNumber": 53, "content": "  \"pleaseAddYourSocialMedia\": \"Please add your social media\","}, {"type": "INSERT", "lineNumber": 54, "content": "  \"pleaseAddYourLocation\": \"Please add your location\","}, {"type": "INSERT", "lineNumber": 55, "content": "  \"youCanAlsoRegisterAsDoctor\": \"You can also register as a doctor from your profile.\","}, {"type": "INSERT", "lineNumber": 56, "content": "  \"youCanAlsoRegisterAsStore\": \"You can also register as a doctor from your profile.\","}, {"type": "INSERT", "lineNumber": 57, "content": "  \"storeLogo\": \"Store Logo\","}, {"type": "INSERT", "lineNumber": 58, "content": "  \"storeBackground\": \"Store Background\","}, {"type": "INSERT", "lineNumber": 59, "content": "  \"storeName\": \"Store Name\","}, {"type": "INSERT", "lineNumber": 60, "content": "  \"search\": \"Search\","}, {"type": "INSERT", "lineNumber": 61, "content": "  \"searchForProducts\": \"Search for products\","}, {"type": "INSERT", "lineNumber": 62, "content": "  \"searchForStores\": \"Search for stores\","}, {"type": "INSERT", "lineNumber": 63, "content": "  \"searchForDoctors\": \"Search for doctors\""}]}, {"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 1, "oldContent": "  \"login\": \"Login\","}, {"type": "DELETE", "lineNumber": 2, "oldContent": "  \"register\": \"Register\","}, {"type": "DELETE", "lineNumber": 3, "oldContent": "  \"email\": \"Email\","}, {"type": "DELETE", "lineNumber": 4, "oldContent": "  \"dontHaveAnAccount\": \"Don't have an account?\","}, {"type": "DELETE", "lineNumber": 5, "oldContent": "  \"haveAnAccount\": \"Have an account?\","}, {"type": "DELETE", "lineNumber": 6, "oldContent": "  \"password\": \"Password\","}, {"type": "DELETE", "lineNumber": 7, "oldContent": "  \"confirmPassword\": \"Confirm Password\","}, {"type": "DELETE", "lineNumber": 8, "oldContent": "  \"forgotPassword\": \"Forgot Password?\","}, {"type": "DELETE", "lineNumber": 9, "oldContent": "  \"resetPassword\": \"Reset Password\","}, {"type": "DELETE", "lineNumber": 10, "oldContent": "  \"mobileNumber\": \"Mobile Number\","}, {"type": "DELETE", "lineNumber": 11, "oldContent": "  \"loginWithYourAccountNow\": \"Login with your account now !\","}, {"type": "DELETE", "lineNumber": 12, "oldContent": "  \"registerWithYourAccountNow\": \"Register with your account now !\","}, {"type": "DELETE", "lineNumber": 13, "oldContent": "  \"skip\": \"Ski<PERSON>\","}, {"type": "DELETE", "lineNumber": 14, "oldContent": "  \"stores\": \"Stores\","}, {"type": "DELETE", "lineNumber": 15, "oldContent": "  \"fullName\": \"Full Name\","}, {"type": "DELETE", "lineNumber": 16, "oldContent": "  \"registerAsStore\": \"Register as Store?\","}, {"type": "DELETE", "lineNumber": 17, "oldContent": "  \"registerAsDoctor\": \"Register as Doctor?\","}, {"type": "DELETE", "lineNumber": 18, "oldContent": "  \"home\": \"Home\","}, {"type": "DELETE", "lineNumber": 19, "oldContent": "  \"reels\": \"Reels\","}, {"type": "DELETE", "lineNumber": 20, "oldContent": "  \"shops\": \"Shops\","}, {"type": "DELETE", "lineNumber": 21, "oldContent": "  \"doctors\": \"Doctors\","}, {"type": "DELETE", "lineNumber": 22, "oldContent": "  \"seeAll\": \"See All\","}, {"type": "DELETE", "lineNumber": 23, "oldContent": "  \"welcomeWithName\": \"Welcome, {name}\","}, {"type": "DELETE", "lineNumber": 24, "oldContent": "  \"animals\": \"Animals\","}, {"type": "DELETE", "lineNumber": 25, "oldContent": "  \"products\": \"Products\","}, {"type": "DELETE", "lineNumber": 26, "oldContent": "  \"about\": \"About\","}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  \"onBoardingTitle1\": \"Find Your Perfect Pet\","}, {"type": "DELETE", "lineNumber": 28, "oldContent": "  \"onBoardingTitle2\": \"Explore Top Pet Stores\","}, {"type": "DELETE", "lineNumber": 29, "oldContent": "  \"onBoardingTitle3\": \"Care for Your Pet's Health\","}, {"type": "DELETE", "lineNumber": 30, "oldContent": "  \"onBoardingDescription1\": \"Discover a wide variety of pets to adopt and bring home the perfect companion.\","}, {"type": "INSERT", "lineNumber": 1, "content": "  \"login\": \"تسجيل الدخول\","}, {"type": "INSERT", "lineNumber": 2, "content": "  \"register\": \"التسجيل\","}, {"type": "INSERT", "lineNumber": 3, "content": "  \"email\": \"ال<PERSON><PERSON><PERSON><PERSON> الإلكتروني\","}, {"type": "INSERT", "lineNumber": 4, "content": "  \"dontHaveAnAccount\": \"ليس لديك حساب؟\","}, {"type": "INSERT", "lineNumber": 5, "content": "  \"haveAnAccount\": \"لديك حساب؟\","}, {"type": "INSERT", "lineNumber": 6, "content": "  \"password\": \"كلمة المرور\","}, {"type": "INSERT", "lineNumber": 7, "content": "  \"confirmPassword\": \"تأكيد كلمة المرور\","}, {"type": "INSERT", "lineNumber": 8, "content": "  \"forgotPassword\": \"نسيت كلمة المرور؟\","}, {"type": "INSERT", "lineNumber": 9, "content": "  \"resetPassword\": \"إعادة تعيين كلمة المرور\","}, {"type": "INSERT", "lineNumber": 10, "content": "  \"mobileNumber\": \"رقم الهاتف المحمول\","}, {"type": "INSERT", "lineNumber": 11, "content": "  \"loginWithYourAccountNow\": \"سجل دخولك بحسابك الآن!\","}, {"type": "INSERT", "lineNumber": 12, "content": "  \"registerWithYourAccountNow\": \"سجل حسابك الآن!\","}, {"type": "INSERT", "lineNumber": 13, "content": "  \"skip\": \"تخطي\","}, {"type": "INSERT", "lineNumber": 14, "content": "  \"stores\": \"المتاجر\","}, {"type": "INSERT", "lineNumber": 15, "content": "  \"fullName\": \"الاسم الكامل\","}, {"type": "INSERT", "lineNumber": 16, "content": "  \"registerAsStore\": \"التسجيل كمتجر؟\","}, {"type": "INSERT", "lineNumber": 17, "content": "  \"registerAsDoctor\": \"التسجيل كطبيب؟\","}, {"type": "INSERT", "lineNumber": 18, "content": "  \"home\": \"الرئيسية\","}, {"type": "INSERT", "lineNumber": 19, "content": "  \"reels\": \"الريلز\","}, {"type": "INSERT", "lineNumber": 20, "content": "  \"shops\": \"المتاجر\","}, {"type": "INSERT", "lineNumber": 21, "content": "  \"doctors\": \"الأطباء\","}, {"type": "INSERT", "lineNumber": 22, "content": "  \"seeAll\": \"عرض الكل\","}, {"type": "INSERT", "lineNumber": 23, "content": "  \"welcomeWithName\": \"مرحباً، {name}\","}, {"type": "INSERT", "lineNumber": 24, "content": "  \"animals\": \"الحيوانات\","}, {"type": "INSERT", "lineNumber": 25, "content": "  \"products\": \"المنتجات\","}, {"type": "INSERT", "lineNumber": 26, "content": "  \"about\": \"حول\","}, {"type": "INSERT", "lineNumber": 27, "content": "  \"onBoardingTitle1\": \"اعثر على حيوانك الأليف المثالي\","}, {"type": "INSERT", "lineNumber": 28, "content": "  \"onBoardingTitle2\": \"استكشف أفضل متاجر الحيوانات الأليفة\","}, {"type": "INSERT", "lineNumber": 29, "content": "  \"onBoardingTitle3\": \"اعتن بصحة حيوانك الأليف\","}, {"type": "INSERT", "lineNumber": 30, "content": "  \"onBoardingDescription1\": \"اكتشف مجموعة واسعة من الحيوانات الأليفة للتبني واصطحب الرفيق المثالي إلى المنزل.\","}, {"type": "INSERT", "lineNumber": 31, "content": "  \"onBoardingDescription2\": \"تصفح متاجر الحيوانات الأليفة الموثوقة واعثر على أفضل المنتجات لأصدقائك ذوي الفراء.\","}, {"type": "INSERT", "lineNumber": 32, "content": "  \"onBoardingDescription3\": \"تواصل مع الأطباء البيطريين المحترفين وتأكد من حصول حيواناتك الأليفة على أفضل رعاية.\","}, {"type": "INSERT", "lineNumber": 33, "content": "  \"next\": \"التالي\","}, {"type": "INSERT", "lineNumber": 34, "content": "  \"startNow\": \"ابد<PERSON> الآن\","}, {"type": "INSERT", "lineNumber": 35, "content": "  \"description\": \"الوصف\","}, {"type": "INSERT", "lineNumber": 36, "content": "  \"address\": \"الع<PERSON><PERSON><PERSON>\","}, {"type": "INSERT", "lineNumber": 37, "content": "  \"enter\": \"أدخل\","}, {"type": "INSERT", "lineNumber": 38, "content": "  \"doctorName\": \"اسم الطبيب\","}, {"type": "INSERT", "lineNumber": 39, "content": "  \"emailOptional\": \"ال<PERSON><PERSON>ي<PERSON> الإلكتروني (اختياري)\","}, {"type": "INSERT", "lineNumber": 40, "content": "  \"doctorLogo\": \"شعار الطبيب\","}, {"type": "INSERT", "lineNumber": 41, "content": "  \"save\": \"حفظ\","}, {"type": "INSERT", "lineNumber": 42, "content": "  \"submit\": \"إرسال\","}, {"type": "INSERT", "lineNumber": 43, "content": "  \"pickImage\": \"اختيار صورة\","}, {"type": "INSERT", "lineNumber": 44, "content": "  \"locationPickedSuccessfully\": \"تم اختيار الموقع بنجاح\","}, {"type": "INSERT", "lineNumber": 45, "content": "  \"pickLocation\": \"اختيار الموقع\","}, {"type": "INSERT", "lineNumber": 46, "content": "  \"tapToSelectLocation\": \"اضغط لاختيار الموقع\","}, {"type": "INSERT", "lineNumber": 47, "content": "  \"changeLocation\": \"تغيير الموقع\","}, {"type": "INSERT", "lineNumber": 48, "content": "  \"noDataFound\": \"لم يتم العثور على بيانات\","}, {"type": "INSERT", "lineNumber": 49, "content": "  \"changeSocial\": \"تغيير وسائل التواصل\","}, {"type": "INSERT", "lineNumber": 50, "content": "  \"pleaseAddValidLink\": \"يرجى إضافة رابط صحيح\","}, {"type": "INSERT", "lineNumber": 51, "content": "  \"socialMedia\": \"وسائل التواصل الاجتماعي\","}, {"type": "INSERT", "lineNumber": 52, "content": "  \"doctorBackground\": \"خلفية الطبيب\","}, {"type": "INSERT", "lineNumber": 53, "content": "  \"pleaseAddYourSocialMedia\": \"يرجى إضافة وسائل التواصل الاجتماعي الخاصة بك\","}, {"type": "INSERT", "lineNumber": 54, "content": "  \"pleaseAddYourLocation\": \"يرجى إضافة موقعك\","}, {"type": "INSERT", "lineNumber": 55, "content": "  \"youCanAlsoRegisterAsDoctor\": \"يمكنك أيضاً التسجيل كطبيب من ملفك الشخصي.\","}, {"type": "INSERT", "lineNumber": 56, "content": "  \"youCanAlsoRegisterAsStore\": \"يمكنك أيضاً التسجيل كمتجر من ملفك الشخصي.\","}, {"type": "INSERT", "lineNumber": 57, "content": "  \"storeLogo\": \"شعار المتجر\","}, {"type": "INSERT", "lineNumber": 58, "content": "  \"storeBackground\": \"خلفية المتجر\","}, {"type": "INSERT", "lineNumber": 59, "content": "  \"storeName\": \"اسم المتجر\","}, {"type": "INSERT", "lineNumber": 60, "content": "  \"search\": \"البح<PERSON>\","}, {"type": "INSERT", "lineNumber": 61, "content": "  \"searchForProducts\": \"البحث عن المنتجات\","}, {"type": "INSERT", "lineNumber": 62, "content": "  \"searchForStores\": \"البحث عن المتاجر\","}, {"type": "INSERT", "lineNumber": 63, "content": "  \"searchForDoctors\": \"البحث عن الأطباء\""}, {"type": "DELETE", "lineNumber": 32, "oldContent": "  \"onBoardingDescription2\": \"Browse through trusted pet stores and find the best products for your furry friends.\","}, {"type": "DELETE", "lineNumber": 33, "oldContent": "  \"searchForDoctors\": \"Search for doctors\""}, {"type": "DELETE", "lineNumber": 34, "oldContent": "  \"onBoardingDescription3\": \"Connect with professional veterinarians and ensure your pets receive the best care.\","}, {"type": "DELETE", "lineNumber": 35, "oldContent": "  \"searchForStores\": \"Search for stores\","}, {"type": "DELETE", "lineNumber": 36, "oldContent": "  \"next\": \"Next\","}, {"type": "DELETE", "lineNumber": 37, "oldContent": "  \"searchForProducts\": \"Search for products\","}, {"type": "DELETE", "lineNumber": 38, "oldContent": "  \"startNow\": \"Start Now\","}, {"type": "DELETE", "lineNumber": 39, "oldContent": "  \"search\": \"Search\","}, {"type": "DELETE", "lineNumber": 40, "oldContent": "  \"description\": \"Description\","}, {"type": "DELETE", "lineNumber": 41, "oldContent": "  \"storeName\": \"Store Name\","}, {"type": "DELETE", "lineNumber": 42, "oldContent": "  \"address\": \"Address\","}, {"type": "DELETE", "lineNumber": 43, "oldContent": "  \"storeBackground\": \"Store Background\","}, {"type": "DELETE", "lineNumber": 44, "oldContent": "  \"enter\": \"Enter\","}, {"type": "DELETE", "lineNumber": 45, "oldContent": "  \"storeLogo\": \"Store Logo\","}, {"type": "DELETE", "lineNumber": 46, "oldContent": "  \"doctor<PERSON>ame\": \"Doctor Name\","}, {"type": "DELETE", "lineNumber": 47, "oldContent": "  \"youCanAlsoRegisterAsStore\": \"You can also register as a doctor from your profile.\","}, {"type": "DELETE", "lineNumber": 48, "oldContent": "  \"emailOptional\": \"Email (Optional)\","}, {"type": "DELETE", "lineNumber": 49, "oldContent": "  \"youCanAlsoRegisterAsDoctor\": \"You can also register as a doctor from your profile.\","}, {"type": "DELETE", "lineNumber": 50, "oldContent": "  \"doctor<PERSON><PERSON>\": \"Doctor <PERSON><PERSON>\","}, {"type": "DELETE", "lineNumber": 51, "oldContent": "  \"pleaseAddYourLocation\": \"Please add your location\","}, {"type": "DELETE", "lineNumber": 52, "oldContent": "  \"save\": \"Save\","}, {"type": "DELETE", "lineNumber": 53, "oldContent": "  \"pleaseAddYourSocialMedia\": \"Please add your social media\","}, {"type": "DELETE", "lineNumber": 54, "oldContent": "  \"submit\": \"Submit\","}, {"type": "DELETE", "lineNumber": 55, "oldContent": "  \"doctorBackground\": \"Doctor Background\","}, {"type": "DELETE", "lineNumber": 56, "oldContent": "  \"pickImage\": \"Pick Image\","}, {"type": "DELETE", "lineNumber": 57, "oldContent": "  \"socialMedia\": \"Social Media\","}, {"type": "DELETE", "lineNumber": 58, "oldContent": "  \"locationPickedSuccessfully\": \"Location picked successfully\","}, {"type": "DELETE", "lineNumber": 59, "oldContent": "  \"pleaseAddValidLink\": \"Please add valid link\","}, {"type": "DELETE", "lineNumber": 60, "oldContent": "  \"pickLocation\": \"Pick Location\","}, {"type": "DELETE", "lineNumber": 61, "oldContent": "  \"changeSocial\": \"Change Social\","}, {"type": "DELETE", "lineNumber": 62, "oldContent": "  \"tapToSelectLocation\": \"Tap to select location\","}, {"type": "DELETE", "lineNumber": 63, "oldContent": "  \"noDataFound\": \"No data found\","}, {"type": "DELETE", "lineNumber": 64, "oldContent": "  \"changeLocation\": \"Change Location\","}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/l10n/intl_ar.arb": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/l10n/intl_ar.arb", "baseContent": "{\n  \"login\": \"تسجيل الدخول\",\n  \"register\": \"التسجيل\",\n  \"email\": \"البريد الإلكتروني\",\n  \"dontHaveAnAccount\": \"ليس لديك حساب؟\",\n  \"haveAnAccount\": \"لديك حساب؟\",\n  \"password\": \"كلمة المرور\",\n  \"confirmPassword\": \"تأكيد كلمة المرور\",\n  \"forgotPassword\": \"نسيت كلمة المرور؟\",\n  \"resetPassword\": \"إعادة تعيين كلمة المرور\",\n  \"mobileNumber\": \"رقم الهاتف المحمول\",\n  \"loginWithYourAccountNow\": \"سجل دخولك بحسابك الآن!\",\n  \"registerWithYourAccountNow\": \"سجل حسابك الآن!\",\n  \"skip\": \"تخطي\",\n  \"stores\": \"المتاجر\",\n  \"fullName\": \"الاسم الكامل\",\n  \"registerAsStore\": \"التسجيل كمتجر؟\",\n  \"registerAsDoctor\": \"التسجيل كطبيب؟\",\n  \"home\": \"الرئيسية\",\n  \"reels\": \"الريلز\",\n  \"shops\": \"المتاجر\",\n  \"doctors\": \"الأطباء\",\n  \"seeAll\": \"عرض الكل\",\n  \"welcomeWithName\": \"مرحباً، {name}\",\n  \"animals\": \"الحيوانات\",\n  \"products\": \"المنتجات\",\n  \"about\": \"حول\",\n  \"onBoardingTitle1\": \"اعثر على حيوانك الأليف المثالي\",\n  \"onBoardingTitle2\": \"استكشف أفضل متاجر الحيوانات الأليفة\",\n  \"onBoardingTitle3\": \"اعتن بصحة حيوانك الأليف\",\n  \"onBoardingDescription1\": \"اكتشف مجموعة واسعة من الحيوانات الأليفة للتبني واصطحب الرفيق المثالي إلى المنزل.\",\n  \"onBoardingDescription2\": \"تصفح متاجر الحيوانات الأليفة الموثوقة واعثر على أفضل المنتجات لأصدقائك ذوي الفراء.\",\n  \"onBoardingDescription3\": \"تواصل مع الأطباء البيطريين المحترفين وتأكد من حصول حيواناتك الأليفة على أفضل رعاية.\",\n  \"next\": \"التالي\",\n  \"startNow\": \"ابدأ الآن\",\n  \"description\": \"الوصف\",\n  \"address\": \"العنوان\",\n  \"enter\": \"أدخل\",\n  \"doctorName\": \"اسم الطبيب\",\n  \"emailOptional\": \"البريد الإلكتروني (اختياري)\",\n  \"doctorLogo\": \"شعار الطبيب\",\n  \"save\": \"حفظ\",\n  \"submit\": \"إرسال\",\n  \"pickImage\": \"اختيار صورة\",\n  \"locationPickedSuccessfully\": \"تم اختيار الموقع بنجاح\",\n  \"pickLocation\": \"اختيار الموقع\",\n  \"tapToSelectLocation\": \"اضغط لاختيار الموقع\",\n  \"changeLocation\": \"تغيير الموقع\",\n  \"noDataFound\": \"لم يتم العثور على بيانات\",\n  \"changeSocial\": \"تغيير وسائل التواصل\",\n  \"pleaseAddValidLink\": \"يرجى إضافة رابط صحيح\",\n  \"socialMedia\": \"وسائل التواصل الاجتماعي\",\n  \"doctorBackground\": \"خلفية الطبيب\",\n  \"pleaseAddYourSocialMedia\": \"يرجى إضافة وسائل التواصل الاجتماعي الخاصة بك\",\n  \"pleaseAddYourLocation\": \"يرجى إضافة موقعك\",\n  \"youCanAlsoRegisterAsDoctor\": \"يمكنك أيضاً التسجيل كطبيب من ملفك الشخصي.\",\n  \"youCanAlsoRegisterAsStore\": \"يمكنك أيضاً التسجيل كمتجر من ملفك الشخصي.\",\n  \"storeLogo\": \"شعار المتجر\",\n  \"storeBackground\": \"خلفية المتجر\",\n  \"storeName\": \"اسم المتجر\",\n  \"search\": \"البحث\",\n  \"searchForProducts\": \"البحث عن المنتجات\",\n  \"searchForStores\": \"البحث عن المتاجر\",\n  \"searchForDoctors\": \"البحث عن الأطباء\"\n}", "baseTimestamp": *************, "deltas": [{"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 1, "oldContent": "  \"login\": \"تسجيل الدخول\","}, {"type": "DELETE", "lineNumber": 2, "oldContent": "  \"register\": \"التسجيل\","}, {"type": "DELETE", "lineNumber": 3, "oldContent": "  \"email\": \"ال<PERSON><PERSON><PERSON><PERSON> الإلكتروني\","}, {"type": "DELETE", "lineNumber": 4, "oldContent": "  \"dontHaveAnAccount\": \"ليس لديك حساب؟\","}, {"type": "DELETE", "lineNumber": 5, "oldContent": "  \"haveAnAccount\": \"لديك حساب؟\","}, {"type": "DELETE", "lineNumber": 6, "oldContent": "  \"password\": \"كلمة المرور\","}, {"type": "DELETE", "lineNumber": 7, "oldContent": "  \"confirmPassword\": \"تأكيد كلمة المرور\","}, {"type": "DELETE", "lineNumber": 8, "oldContent": "  \"forgotPassword\": \"نسيت كلمة المرور؟\","}, {"type": "DELETE", "lineNumber": 9, "oldContent": "  \"resetPassword\": \"إعادة تعيين كلمة المرور\","}, {"type": "DELETE", "lineNumber": 10, "oldContent": "  \"mobileNumber\": \"رقم الهاتف المحمول\","}, {"type": "DELETE", "lineNumber": 11, "oldContent": "  \"loginWithYourAccountNow\": \"سجل دخولك بحسابك الآن!\","}, {"type": "DELETE", "lineNumber": 12, "oldContent": "  \"registerWithYourAccountNow\": \"سجل حسابك الآن!\","}, {"type": "DELETE", "lineNumber": 13, "oldContent": "  \"skip\": \"تخطي\","}, {"type": "DELETE", "lineNumber": 14, "oldContent": "  \"stores\": \"المتاجر\","}, {"type": "DELETE", "lineNumber": 15, "oldContent": "  \"fullName\": \"الاسم الكامل\","}, {"type": "DELETE", "lineNumber": 16, "oldContent": "  \"registerAsStore\": \"التسجيل كمتجر؟\","}, {"type": "DELETE", "lineNumber": 17, "oldContent": "  \"registerAsDoctor\": \"التسجيل كطبيب؟\","}, {"type": "DELETE", "lineNumber": 18, "oldContent": "  \"home\": \"الرئيسية\","}, {"type": "DELETE", "lineNumber": 19, "oldContent": "  \"reels\": \"الريلز\","}, {"type": "DELETE", "lineNumber": 20, "oldContent": "  \"shops\": \"المتاجر\","}, {"type": "DELETE", "lineNumber": 21, "oldContent": "  \"doctors\": \"الأطباء\","}, {"type": "DELETE", "lineNumber": 22, "oldContent": "  \"seeAll\": \"عرض الكل\","}, {"type": "DELETE", "lineNumber": 23, "oldContent": "  \"welcomeWithName\": \"مرحباً، {name}\","}, {"type": "DELETE", "lineNumber": 24, "oldContent": "  \"animals\": \"الحيوانات\","}, {"type": "DELETE", "lineNumber": 25, "oldContent": "  \"products\": \"المنتجات\","}, {"type": "DELETE", "lineNumber": 26, "oldContent": "  \"about\": \"حول\","}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  \"onBoardingTitle1\": \"اعثر على حيوانك الأليف المثالي\","}, {"type": "DELETE", "lineNumber": 28, "oldContent": "  \"onBoardingTitle2\": \"استكشف أفضل متاجر الحيوانات الأليفة\","}, {"type": "DELETE", "lineNumber": 29, "oldContent": "  \"onBoardingTitle3\": \"اعتن بصحة حيوانك الأليف\","}, {"type": "DELETE", "lineNumber": 30, "oldContent": "  \"onBoardingDescription1\": \"اكتشف مجموعة واسعة من الحيوانات الأليفة للتبني واصطحب الرفيق المثالي إلى المنزل.\","}, {"type": "DELETE", "lineNumber": 31, "oldContent": "  \"onBoardingDescription2\": \"تصفح متاجر الحيوانات الأليفة الموثوقة واعثر على أفضل المنتجات لأصدقائك ذوي الفراء.\","}, {"type": "DELETE", "lineNumber": 32, "oldContent": "  \"onBoardingDescription3\": \"تواصل مع الأطباء البيطريين المحترفين وتأكد من حصول حيواناتك الأليفة على أفضل رعاية.\","}, {"type": "DELETE", "lineNumber": 33, "oldContent": "  \"next\": \"التالي\","}, {"type": "DELETE", "lineNumber": 34, "oldContent": "  \"startNow\": \"ابد<PERSON> الآن\","}, {"type": "DELETE", "lineNumber": 35, "oldContent": "  \"description\": \"الوصف\","}, {"type": "DELETE", "lineNumber": 36, "oldContent": "  \"address\": \"الع<PERSON><PERSON><PERSON>\","}, {"type": "DELETE", "lineNumber": 37, "oldContent": "  \"enter\": \"أدخل\","}, {"type": "DELETE", "lineNumber": 38, "oldContent": "  \"doctorName\": \"اسم الطبيب\","}, {"type": "DELETE", "lineNumber": 39, "oldContent": "  \"emailOptional\": \"ال<PERSON><PERSON>ي<PERSON> الإلكتروني (اختياري)\","}, {"type": "DELETE", "lineNumber": 40, "oldContent": "  \"doctorLogo\": \"شعار الطبيب\","}, {"type": "DELETE", "lineNumber": 41, "oldContent": "  \"save\": \"حفظ\","}, {"type": "DELETE", "lineNumber": 42, "oldContent": "  \"submit\": \"إرسال\","}, {"type": "DELETE", "lineNumber": 43, "oldContent": "  \"pickImage\": \"اختيار صورة\","}, {"type": "DELETE", "lineNumber": 44, "oldContent": "  \"locationPickedSuccessfully\": \"تم اختيار الموقع بنجاح\","}, {"type": "DELETE", "lineNumber": 45, "oldContent": "  \"pickLocation\": \"اختيار الموقع\","}, {"type": "DELETE", "lineNumber": 46, "oldContent": "  \"tapToSelectLocation\": \"اضغط لاختيار الموقع\","}, {"type": "DELETE", "lineNumber": 47, "oldContent": "  \"changeLocation\": \"تغيير الموقع\","}, {"type": "DELETE", "lineNumber": 48, "oldContent": "  \"noDataFound\": \"لم يتم العثور على بيانات\","}, {"type": "DELETE", "lineNumber": 49, "oldContent": "  \"changeSocial\": \"تغيير وسائل التواصل\","}, {"type": "DELETE", "lineNumber": 50, "oldContent": "  \"pleaseAddValidLink\": \"يرجى إضافة رابط صحيح\","}, {"type": "DELETE", "lineNumber": 51, "oldContent": "  \"socialMedia\": \"وسائل التواصل الاجتماعي\","}, {"type": "DELETE", "lineNumber": 52, "oldContent": "  \"doctorBackground\": \"خلفية الطبيب\","}, {"type": "DELETE", "lineNumber": 53, "oldContent": "  \"pleaseAddYourSocialMedia\": \"يرجى إضافة وسائل التواصل الاجتماعي الخاصة بك\","}, {"type": "DELETE", "lineNumber": 54, "oldContent": "  \"pleaseAddYourLocation\": \"يرجى إضافة موقعك\","}, {"type": "DELETE", "lineNumber": 55, "oldContent": "  \"youCanAlsoRegisterAsDoctor\": \"يمكنك أيضاً التسجيل كطبيب من ملفك الشخصي.\","}, {"type": "DELETE", "lineNumber": 56, "oldContent": "  \"youCanAlsoRegisterAsStore\": \"يمكنك أيضاً التسجيل كمتجر من ملفك الشخصي.\","}, {"type": "DELETE", "lineNumber": 57, "oldContent": "  \"storeLogo\": \"شعار المتجر\","}, {"type": "DELETE", "lineNumber": 58, "oldContent": "  \"storeBackground\": \"خلفية المتجر\","}, {"type": "DELETE", "lineNumber": 59, "oldContent": "  \"storeName\": \"اسم المتجر\","}, {"type": "DELETE", "lineNumber": 60, "oldContent": "  \"search\": \"البح<PERSON>\","}, {"type": "DELETE", "lineNumber": 61, "oldContent": "  \"searchForProducts\": \"البحث عن المنتجات\","}, {"type": "DELETE", "lineNumber": 62, "oldContent": "  \"searchForStores\": \"البحث عن المتاجر\","}, {"type": "DELETE", "lineNumber": 63, "oldContent": "  \"searchForDoctors\": \"البحث عن الأطباء\""}, {"type": "INSERT", "lineNumber": 1, "content": ""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/main.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/main.dart", "baseContent": "import 'dart:io';\n\nimport 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:dropx/src/app.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nvoid main() async {\n  WidgetsFlutterBinding.ensureInitialized();\n\n  await GetStorageService.init();\n\n  HttpOverrides.global = MyHttpOverrides();\n\n  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);\n\n  runApp(const ProviderScope(child: BaseApp()));\n}\n", "baseTimestamp": 1756931861026, "deltas": [{"timestamp": 1756969404987, "changes": [{"type": "DELETE", "lineNumber": 66, "oldContent": "        <PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 67, "oldContent": "          clipper: A,"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "          child: Container("}, {"type": "DELETE", "lineNumber": 69, "oldContent": "            height: 220,"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "            color: const Color(0xFF2DB45D),"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "        ),"}, {"type": "INSERT", "lineNumber": 66, "content": "       "}]}, {"timestamp": 1756969409884, "changes": [{"type": "MODIFY", "lineNumber": 66, "content": "       Assets/", "oldContent": "       "}]}, {"timestamp": 1756969415197, "changes": [{"type": "MODIFY", "lineNumber": 66, "content": "       Assets.", "oldContent": "       Assets/"}]}, {"timestamp": 1756969419037, "changes": [{"type": "DELETE", "lineNumber": 66, "oldContent": "       Assets."}, {"type": "MODIFY", "lineNumber": 66, "content": "        Assets.SafeArea(", "oldContent": "        SafeArea("}]}, {"timestamp": 1756969431943, "changes": [{"type": "MODIFY", "lineNumber": 66, "content": "       Assets.images.topClipper.image(),", "oldContent": "        Assets.SafeArea("}, {"type": "INSERT", "lineNumber": 67, "content": "        SafeArea("}]}, {"timestamp": 1756969437234, "changes": [{"type": "INSERT", "lineNumber": 5, "content": "import 'generated/assets.gen.dart';"}, {"type": "INSERT", "lineNumber": 6, "content": ""}, {"type": "DELETE", "lineNumber": 368, "oldContent": "//"}]}, {"timestamp": 1756969464653, "changes": [{"type": "MODIFY", "lineNumber": 7, "content": "void main() {", "oldContent": "void main() {"}, {"type": "MODIFY", "lineNumber": 68, "content": "        Assets.images.topClipper.image(),", "oldContent": "       Assets.images.topClipper.image(),"}]}, {"timestamp": 1756969475728, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "import 'package:flutter/gestures.dart';", "oldContent": "import 'dart:io';"}, {"type": "DELETE", "lineNumber": 2, "oldContent": "import 'package:dropx/src/app.dart';"}, {"type": "DELETE", "lineNumber": 3, "oldContent": "import 'package:xr_helper/xr_helper.dart';"}, {"type": "DELETE", "lineNumber": 4, "oldContent": "void main() async {"}, {"type": "INSERT", "lineNumber": 2, "content": "import 'package:flutter/services.dart';"}, {"type": "INSERT", "lineNumber": 3, "content": "import 'package:google_fonts/google_fonts.dart';"}, {"type": "INSERT", "lineNumber": 4, "content": ""}, {"type": "INSERT", "lineNumber": 8, "content": "  runApp(const MyApp());"}, {"type": "INSERT", "lineNumber": 9, "content": "}"}, {"type": "INSERT", "lineNumber": 11, "content": "class MyApp extends StatelessWidget {"}, {"type": "INSERT", "lineNumber": 12, "content": "  const MyApp({super.key});"}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}, {"type": "INSERT", "lineNumber": 14, "content": "  @override"}, {"type": "INSERT", "lineNumber": 15, "content": "  Widget build(BuildContext context) {"}, {"type": "INSERT", "lineNumber": 16, "content": "    return MaterialApp("}, {"type": "INSERT", "lineNumber": 17, "content": "      title: 'Flutter Sign Up UI',"}, {"type": "INSERT", "lineNumber": 18, "content": "      theme: Theme<PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 19, "content": "        primarySwatch: Colors.green,"}, {"type": "INSERT", "lineNumber": 20, "content": "        textTheme: GoogleFonts.cairoTextTheme(Theme.of(context).textTheme),"}, {"type": "INSERT", "lineNumber": 21, "content": "        scaffoldBackgroundColor: Colors.white,"}, {"type": "INSERT", "lineNumber": 22, "content": "      ),"}, {"type": "INSERT", "lineNumber": 23, "content": "      debugShowCheckedModeBanner: false,"}, {"type": "INSERT", "lineNumber": 24, "content": "      home: const SignUpScreen(),"}, {"type": "INSERT", "lineNumber": 25, "content": "    );"}, {"type": "INSERT", "lineNumber": 26, "content": "  }"}, {"type": "DELETE", "lineNumber": 13, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 14, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 17, "oldContent": "// void main() async {"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "    return false;"}, {"type": "INSERT", "lineNumber": 29, "content": "class SignUpScreen extends StatefulWidget {"}, {"type": "INSERT", "lineNumber": 30, "content": "  const SignUpScreen({super.key});"}, {"type": "INSERT", "lineNumber": 31, "content": ""}, {"type": "DELETE", "lineNumber": 24, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "    path.close();"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "      size.width,"}, {"type": "DELETE", "lineNumber": 28, "oldContent": "      size.width * 0.85,"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "      size.width * 0.5,"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "      size.width * 0.2,"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "    path.lineTo(0, size.height * 0.75);"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "  Path getClip(Size size) {"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "class HeaderClipper extends CustomClipper<Path> {"}, {"type": "INSERT", "lineNumber": 33, "content": "  State<SignUpScreen> createState() => _SignUpScreenState();"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "            color: primary<PERSON>reen,"}, {"type": "DELETE", "lineNumber": 39, "oldContent": "            fontSize: 18,"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "          'تسجيل دخول',"}, {"type": "DELETE", "lineNumber": 41, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "            borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "          side: BorderSide(color: primaryGreen, width: 1.5),"}, {"type": "DELETE", "lineNumber": 44, "oldContent": "        style: OutlinedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 45, "oldContent": "      child: Out<PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 46, "oldContent": "    return SizedBox("}, {"type": "DELETE", "lineNumber": 48, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 50, "oldContent": "          'انشاء حساب',"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "        ),"}, {"type": "INSERT", "lineNumber": 36, "content": "class _SignUpScreenState extends State<SignUpScreen> {"}, {"type": "INSERT", "lineNumber": 37, "content": "  bool _passwordVisible = false;"}, {"type": "INSERT", "lineNumber": 38, "content": "  bool _confirmPasswordVisible = false;"}, {"type": "INSERT", "lineNumber": 39, "content": "  bool _termsAccepted = false;"}, {"type": "INSERT", "lineNumber": 40, "content": ""}, {"type": "INSERT", "lineNumber": 41, "content": "  @override"}, {"type": "INSERT", "lineNumber": 42, "content": "  Widget build(BuildContext context) {"}, {"type": "INSERT", "lineNumber": 43, "content": "    return AnnotatedRegion<SystemUiOverlayStyle>("}, {"type": "INSERT", "lineNumber": 44, "content": "      value: SystemUiOverlayStyle.light.copyWith("}, {"type": "INSERT", "lineNumber": 45, "content": "        statusBarColor: const Color(0xFF2DB45D),"}, {"type": "INSERT", "lineNumber": 46, "content": "        systemNavigationBarColor: Colors.white,"}, {"type": "INSERT", "lineNumber": 47, "content": "        systemNavigationBarIconBrightness: Brightness.dark,"}, {"type": "INSERT", "lineNumber": 48, "content": "      ),"}, {"type": "INSERT", "lineNumber": 49, "content": "      child: Directionality("}, {"type": "INSERT", "lineNumber": 50, "content": "        textDirection: TextDirection.rtl,"}, {"type": "INSERT", "lineNumber": 51, "content": "        child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 52, "content": "          body: SingleChildScrollView("}, {"type": "INSERT", "lineNumber": 53, "content": "            child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 54, "content": "              children: ["}, {"type": "INSERT", "lineNumber": 55, "content": "                _buildHeader(context),"}, {"type": "INSERT", "lineNumber": 56, "content": "                _buildForm(context),"}, {"type": "INSERT", "lineNumber": 57, "content": "              ],"}, {"type": "INSERT", "lineNumber": 58, "content": "            ),"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "          shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 54, "oldContent": "          backgroundColor: const Color(0xFFC4C4C4),"}, {"type": "DELETE", "lineNumber": 55, "oldContent": "        onPressed: () {},"}, {"type": "DELETE", "lineNumber": 56, "oldContent": "      height: 50,"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "  Widget _buildSignUpButton() {"}, {"type": "INSERT", "lineNumber": 60, "content": "        ),"}, {"type": "INSERT", "lineNumber": 61, "content": "      ),"}, {"type": "INSERT", "lineNumber": 62, "content": "    );"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "      ],"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "              ],"}, {"type": "DELETE", "lineNumber": 62, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 63, "oldContent": "                      // Handle terms and conditions tap"}, {"type": "DELETE", "lineNumber": 64, "oldContent": "                  recognizer: TapGestureRecognizer()"}, {"type": "DELETE", "lineNumber": 65, "oldContent": "                    decoration: TextDecoration.underline,"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "                  style: const TextStyle("}, {"type": "DELETE", "lineNumber": 67, "oldContent": "                TextSpan("}, {"type": "DELETE", "lineNumber": 68, "oldContent": "        Assets.images.topClipper.image(),"}, {"type": "INSERT", "lineNumber": 64, "content": ""}, {"type": "INSERT", "lineNumber": 65, "content": "  Widget _buildHeader(BuildContext context) {"}, {"type": "INSERT", "lineNumber": 66, "content": "    return Stack("}, {"type": "INSERT", "lineNumber": 67, "content": "      children: ["}, {"type": "INSERT", "lineNumber": 68, "content": "        Assets.images.topClipper.image("}, {"type": "INSERT", "lineNumber": 69, "content": "          width: double.infinity,"}, {"type": "INSERT", "lineNumber": 70, "content": "          height: 200,"}, {"type": "INSERT", "lineNumber": 71, "content": "        ),"}, {"type": "DELETE", "lineNumber": 73, "oldContent": "          height: 24,"}, {"type": "DELETE", "lineNumber": 74, "oldContent": "        SizedBox("}, {"type": "DELETE", "lineNumber": 75, "oldContent": "      crossAxisAlignment: CrossAxisAlignment.center,"}, {"type": "MODIFY", "lineNumber": 76, "content": "            child: Row(", "oldContent": "  Widget _buildTermsAndConditions() {"}, {"type": "INSERT", "lineNumber": 77, "content": "              mainAxisAlignment: MainAxisAlignment.spaceBetween,"}, {"type": "INSERT", "lineNumber": 78, "content": "              crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "INSERT", "lineNumber": 79, "content": "              children: ["}, {"type": "DELETE", "lineNumber": 98, "oldContent": "                    icon: Icon("}, {"type": "DELETE", "lineNumber": 99, "oldContent": "            suffixIcon: isPassword"}, {"type": "DELETE", "lineNumber": 100, "oldContent": "            prefixIconConstraints:"}, {"type": "INSERT", "lineNumber": 101, "content": "                    );"}, {"type": "INSERT", "lineNumber": 102, "content": "                  },"}, {"type": "INSERT", "lineNumber": 103, "content": "                ),"}, {"type": "DELETE", "lineNumber": 213, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 214, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 215, "oldContent": "//     return false;"}, {"type": "MODIFY", "lineNumber": 216, "content": "                      color: Colors.grey,", "oldContent": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 217, "content": "                    ),"}, {"type": "INSERT", "lineNumber": 218, "content": "                    onPressed: onToggleVisibility,"}, {"type": "INSERT", "lineNumber": 219, "content": "                  )"}, {"type": "INSERT", "lineNumber": 220, "content": "                : null,"}, {"type": "INSERT", "lineNumber": 221, "content": "            enabledBorder: const UnderlineInputBorder("}, {"type": "INSERT", "lineNumber": 222, "content": "              borderSide: BorderSide(color: primaryGreen, width: 1.0),"}, {"type": "INSERT", "lineNumber": 223, "content": "            ),"}, {"type": "INSERT", "lineNumber": 224, "content": "            focusedBorder: const UnderlineInputBorder("}, {"type": "INSERT", "lineNumber": 225, "content": "              borderSide: BorderSide(color: primaryGreen, width: 2.0),"}, {"type": "INSERT", "lineNumber": 226, "content": "            ),"}, {"type": "INSERT", "lineNumber": 227, "content": "            contentPadding: const EdgeInsets.only(top: 15, bottom: 10),"}, {"type": "INSERT", "lineNumber": 228, "content": "          ),"}, {"type": "INSERT", "lineNumber": 229, "content": "        ),"}, {"type": "INSERT", "lineNumber": 230, "content": "      ],"}, {"type": "INSERT", "lineNumber": 231, "content": "    );"}, {"type": "INSERT", "lineNumber": 232, "content": "  }"}, {"type": "INSERT", "lineNumber": 233, "content": ""}, {"type": "INSERT", "lineNumber": 234, "content": "  Widget _buildTermsAndConditions() {"}, {"type": "INSERT", "lineNumber": 235, "content": "    return Row("}, {"type": "INSERT", "lineNumber": 236, "content": "      crossAxisAlignment: CrossAxisAlignment.center,"}, {"type": "INSERT", "lineNumber": 237, "content": "      children: ["}, {"type": "INSERT", "lineNumber": 238, "content": "        SizedBox("}, {"type": "INSERT", "lineNumber": 239, "content": "          width: 24,"}, {"type": "INSERT", "lineNumber": 240, "content": "          height: 24,"}, {"type": "INSERT", "lineNumber": 241, "content": "          child: Checkbox("}, {"type": "INSERT", "lineNumber": 242, "content": "            value: _termsAccepted,"}, {"type": "INSERT", "lineNumber": 243, "content": "            onChanged: (bool? value) {"}, {"type": "INSERT", "lineNumber": 244, "content": "              setState(() {"}, {"type": "INSERT", "lineNumber": 245, "content": "                _termsAccepted = value ?? false;"}, {"type": "INSERT", "lineNumber": 246, "content": "              });"}, {"type": "INSERT", "lineNumber": 247, "content": "            },"}, {"type": "INSERT", "lineNumber": 248, "content": "            activeColor: const Color(0xFF2DB45D),"}, {"type": "INSERT", "lineNumber": 249, "content": "            side: const BorderSide(color: Colors.black, width: 1.5),"}, {"type": "INSERT", "lineNumber": 250, "content": "            shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 251, "content": "              borderRadius: BorderRadius.circular(4),"}, {"type": "INSERT", "lineNumber": 252, "content": "            ),"}, {"type": "INSERT", "lineNumber": 253, "content": "          ),"}, {"type": "INSERT", "lineNumber": 254, "content": "        ),"}, {"type": "INSERT", "lineNumber": 255, "content": "        const SizedBox(width: 12),"}, {"type": "INSERT", "lineNumber": 256, "content": "        Expanded("}, {"type": "INSERT", "lineNumber": 257, "content": "          child: <PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 258, "content": "            text: TextSpan("}, {"type": "INSERT", "lineNumber": 259, "content": "              style: GoogleFonts.cairo(fontSize: 13, color: Colors.black87),"}, {"type": "INSERT", "lineNumber": 260, "content": "              children: ["}, {"type": "INSERT", "lineNumber": 261, "content": "                const TextSpan(text: 'أقر بأنني قد قرأت وفهمت وأوافق على '),"}, {"type": "INSERT", "lineNumber": 262, "content": "                TextSpan("}, {"type": "INSERT", "lineNumber": 263, "content": "                  text: 'الشروط والأحكام',"}, {"type": "INSERT", "lineNumber": 264, "content": "                  style: const TextStyle("}, {"type": "INSERT", "lineNumber": 265, "content": "                    color: Color(0xFF3366CC),"}, {"type": "INSERT", "lineNumber": 266, "content": "                    decoration: TextDecoration.underline,"}, {"type": "INSERT", "lineNumber": 267, "content": "                  ),"}, {"type": "INSERT", "lineNumber": 268, "content": "                  recognizer: TapGestureRecognizer()"}, {"type": "INSERT", "lineNumber": 269, "content": "                    ..onTap = () {"}, {"type": "INSERT", "lineNumber": 270, "content": "                      // Handle terms and conditions tap"}, {"type": "INSERT", "lineNumber": 271, "content": "                    },"}, {"type": "INSERT", "lineNumber": 272, "content": "                ),"}, {"type": "INSERT", "lineNumber": 273, "content": "                const TextSpan(text: ' الخاصة باستخدام هذا التطبيق'),"}, {"type": "INSERT", "lineNumber": 274, "content": "              ],"}, {"type": "INSERT", "lineNumber": 275, "content": "            ),"}, {"type": "INSERT", "lineNumber": 276, "content": "          ),"}, {"type": "INSERT", "lineNumber": 277, "content": "        ),"}, {"type": "INSERT", "lineNumber": 278, "content": "      ],"}, {"type": "INSERT", "lineNumber": 279, "content": "    );"}, {"type": "INSERT", "lineNumber": 280, "content": "  }"}, {"type": "INSERT", "lineNumber": 281, "content": ""}, {"type": "INSERT", "lineNumber": 282, "content": "  Widget _buildSignUpButton() {"}, {"type": "INSERT", "lineNumber": 283, "content": "    return SizedBox("}, {"type": "INSERT", "lineNumber": 284, "content": "      height: 50,"}, {"type": "INSERT", "lineNumber": 285, "content": "      child: El<PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 286, "content": "        onPressed: () {},"}, {"type": "INSERT", "lineNumber": 287, "content": "        style: ElevatedButton.styleFrom("}, {"type": "INSERT", "lineNumber": 288, "content": "          backgroundColor: const Color(0xFFC4C4C4),"}, {"type": "INSERT", "lineNumber": 289, "content": "          foregroundColor: Colors.black,"}, {"type": "INSERT", "lineNumber": 290, "content": "          shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 291, "content": "            borderRadius: BorderRadius.circular(12),"}, {"type": "INSERT", "lineNumber": 292, "content": "          ),"}, {"type": "INSERT", "lineNumber": 293, "content": "          elevation: 0,"}, {"type": "INSERT", "lineNumber": 294, "content": "        ),"}, {"type": "INSERT", "lineNumber": 295, "content": "        child: const Text("}, {"type": "INSERT", "lineNumber": 296, "content": "          'انشاء حساب',"}, {"type": "INSERT", "lineNumber": 297, "content": "          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),"}, {"type": "INSERT", "lineNumber": 298, "content": "        ),"}, {"type": "INSERT", "lineNumber": 299, "content": "      ),"}, {"type": "INSERT", "lineNumber": 300, "content": "    );"}, {"type": "INSERT", "lineNumber": 301, "content": "  }"}, {"type": "INSERT", "lineNumber": 302, "content": ""}, {"type": "INSERT", "lineNumber": 303, "content": "  Widget _buildLoginButton(Color primaryGreen) {"}, {"type": "INSERT", "lineNumber": 304, "content": "    return SizedBox("}, {"type": "INSERT", "lineNumber": 305, "content": "      height: 50,"}, {"type": "INSERT", "lineNumber": 306, "content": "      child: Out<PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 307, "content": "        onPressed: () {},"}, {"type": "INSERT", "lineNumber": 308, "content": "        style: OutlinedButton.styleFrom("}, {"type": "INSERT", "lineNumber": 309, "content": "          foregroundColor: primaryGreen,"}, {"type": "INSERT", "lineNumber": 310, "content": "          side: BorderSide(color: primaryGreen, width: 1.5),"}, {"type": "INSERT", "lineNumber": 311, "content": "          shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 312, "content": "            borderRadius: BorderRadius.circular(12),"}, {"type": "INSERT", "lineNumber": 313, "content": "          ),"}, {"type": "INSERT", "lineNumber": 314, "content": "        ),"}, {"type": "INSERT", "lineNumber": 315, "content": "        child: Text("}, {"type": "INSERT", "lineNumber": 316, "content": "          'تسجيل دخول',"}, {"type": "INSERT", "lineNumber": 317, "content": "          style: TextStyle("}, {"type": "INSERT", "lineNumber": 318, "content": "            fontSize: 18,"}, {"type": "INSERT", "lineNumber": 319, "content": "            fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 320, "content": "            color: primary<PERSON>reen,"}, {"type": "INSERT", "lineNumber": 321, "content": "          ),"}, {"type": "INSERT", "lineNumber": 322, "content": "        ),"}, {"type": "INSERT", "lineNumber": 323, "content": "      ),"}, {"type": "INSERT", "lineNumber": 324, "content": "    );"}, {"type": "INSERT", "lineNumber": 325, "content": "  }"}, {"type": "INSERT", "lineNumber": 326, "content": "}"}, {"type": "INSERT", "lineNumber": 327, "content": ""}, {"type": "INSERT", "lineNumber": 328, "content": "// class HeaderClipper extends CustomClipper<Path> {"}, {"type": "DELETE", "lineNumber": 218, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 219, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 220, "oldContent": "//     return path;"}, {"type": "DELETE", "lineNumber": 221, "oldContent": "//     path.close();"}, {"type": "DELETE", "lineNumber": 222, "oldContent": "//     path.lineTo(size.width, 0);"}, {"type": "DELETE", "lineNumber": 223, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 224, "oldContent": "//       size.height * 0.7,"}, {"type": "DELETE", "lineNumber": 225, "oldContent": "//       size.width,"}, {"type": "DELETE", "lineNumber": 226, "oldContent": "//       size.height * 0.65,"}, {"type": "DELETE", "lineNumber": 227, "oldContent": "//       size.width * 0.85,"}, {"type": "INSERT", "lineNumber": 330, "content": "//   Path getClip(Size size) {"}, {"type": "INSERT", "lineNumber": 331, "content": "//     final path = Path();"}, {"type": "INSERT", "lineNumber": 332, "content": "//     path.lineTo(0, size.height * 0.75);"}, {"type": "DELETE", "lineNumber": 229, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 230, "oldContent": "//       size.height * 0.85,"}, {"type": "DELETE", "lineNumber": 231, "oldContent": "//       size.width * 0.5,"}, {"type": "DELETE", "lineNumber": 232, "oldContent": "//       size.height,"}, {"type": "INSERT", "lineNumber": 335, "content": "//       size.height,"}, {"type": "INSERT", "lineNumber": 336, "content": "//       size.width * 0.5,"}, {"type": "INSERT", "lineNumber": 337, "content": "//       size.height * 0.85,"}, {"type": "INSERT", "lineNumber": 338, "content": "//     );"}, {"type": "DELETE", "lineNumber": 235, "oldContent": "//     path.lineTo(0, size.height * 0.75);"}, {"type": "DELETE", "lineNumber": 236, "oldContent": "//     final path = Path();"}, {"type": "DELETE", "lineNumber": 237, "oldContent": "//   Path getClip(Size size) {"}, {"type": "INSERT", "lineNumber": 340, "content": "//       size.width * 0.85,"}, {"type": "INSERT", "lineNumber": 341, "content": "//       size.height * 0.65,"}, {"type": "INSERT", "lineNumber": 342, "content": "//       size.width,"}, {"type": "INSERT", "lineNumber": 343, "content": "//       size.height * 0.7,"}, {"type": "INSERT", "lineNumber": 344, "content": "//     );"}, {"type": "INSERT", "lineNumber": 345, "content": "//     path.lineTo(size.width, 0);"}, {"type": "INSERT", "lineNumber": 346, "content": "//     path.close();"}, {"type": "INSERT", "lineNumber": 347, "content": "//     return path;"}, {"type": "INSERT", "lineNumber": 348, "content": "//   }"}, {"type": "INSERT", "lineNumber": 349, "content": "//"}, {"type": "DELETE", "lineNumber": 239, "oldContent": "// class HeaderClipper extends CustomClipper<Path> {"}, {"type": "INSERT", "lineNumber": 351, "content": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 352, "content": "//     return false;"}, {"type": "INSERT", "lineNumber": 353, "content": "//   }"}, {"type": "INSERT", "lineNumber": 354, "content": "// }"}, {"type": "INSERT", "lineNumber": 355, "content": "// import 'dart:io';"}, {"type": "INSERT", "lineNumber": 356, "content": "//"}, {"type": "INSERT", "lineNumber": 357, "content": "// import 'package:flutter/material.dart';"}, {"type": "INSERT", "lineNumber": 358, "content": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "INSERT", "lineNumber": 359, "content": "// import 'package:dropx/src/app.dart';"}, {"type": "INSERT", "lineNumber": 360, "content": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "INSERT", "lineNumber": 361, "content": "//"}, {"type": "INSERT", "lineNumber": 362, "content": "// void main() async {"}, {"type": "INSERT", "lineNumber": 363, "content": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "INSERT", "lineNumber": 364, "content": "//"}, {"type": "INSERT", "lineNumber": 365, "content": "//   await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 366, "content": "//"}, {"type": "INSERT", "lineNumber": 367, "content": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 368, "content": "//"}, {"type": "INSERT", "lineNumber": 369, "content": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 370, "content": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 371, "content": "// }"}, {"type": "INSERT", "lineNumber": 372, "content": ""}]}, {"timestamp": 1756969481991, "changes": [{"type": "MODIFY", "lineNumber": 1, "content": "import 'package:flutter/material.dart';", "oldContent": ""}, {"type": "MODIFY", "lineNumber": 6, "content": "", "oldContent": "import 'package:xr_helper/xr_helper.dart';"}, {"type": "DELETE", "lineNumber": 9, "oldContent": ""}, {"type": "DELETE", "lineNumber": 11, "oldContent": "void main() async {"}, {"type": "MODIFY", "lineNumber": 11, "content": "class MyApp extends StatelessWidget {", "oldContent": "class MyApp extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "  await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "DELETE", "lineNumber": 26, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 27, "content": "}"}, {"type": "INSERT", "lineNumber": 28, "content": ""}, {"type": "INSERT", "lineNumber": 32, "content": "  @override"}, {"type": "MODIFY", "lineNumber": 34, "content": "}", "oldContent": "  Widget _buildTextField({"}, {"type": "INSERT", "lineNumber": 35, "content": ""}, {"type": "DELETE", "lineNumber": 47, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "              keyboardType: TextInputType.number),"}, {"type": "MODIFY", "lineNumber": 59, "content": "          ),", "oldContent": "            },"}, {"type": "INSERT", "lineNumber": 63, "content": "  }"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "        SafeArea("}, {"type": "DELETE", "lineNumber": 72, "oldContent": "          child: Padding("}, {"type": "INSERT", "lineNumber": 71, "content": "          fit: BoxFit.cover,"}, {"type": "INSERT", "lineNumber": 73, "content": "        SafeArea("}, {"type": "INSERT", "lineNumber": 74, "content": "          child: Padding("}, {"type": "DELETE", "lineNumber": 78, "oldContent": "                const Padding("}, {"type": "DELETE", "lineNumber": 80, "oldContent": "                  padding: EdgeInsets.only(top: 10.0),"}, {"type": "INSERT", "lineNumber": 81, "content": "                const Padding("}, {"type": "INSERT", "lineNumber": 82, "content": "                  padding: EdgeInsets.only(top: 10.0),"}, {"type": "DELETE", "lineNumber": 102, "oldContent": "              ],"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 106, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "            value: _termsAccepted,"}, {"type": "DELETE", "lineNumber": 108, "oldContent": "          height: 24,"}, {"type": "DELETE", "lineNumber": 109, "oldContent": "          width: 24,"}, {"type": "DELETE", "lineNumber": 110, "oldContent": "      children: ["}, {"type": "DELETE", "lineNumber": 111, "oldContent": "    return Row("}, {"type": "DELETE", "lineNumber": 112, "oldContent": "  Widget _buildTermsAndConditions() {"}, {"type": "DELETE", "lineNumber": 113, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 114, "oldContent": "      ],"}, {"type": "INSERT", "lineNumber": 105, "content": "              ],"}, {"type": "INSERT", "lineNumber": 106, "content": "            ),"}, {"type": "INSERT", "lineNumber": 107, "content": "          ),"}, {"type": "DELETE", "lineNumber": 116, "oldContent": "            contentPadding: const EdgeInsets.only(top: 15, bottom: 10),"}, {"type": "DELETE", "lineNumber": 117, "oldContent": "              borderSide: BorderSide(color: primaryGreen, width: 2.0),"}, {"type": "DELETE", "lineNumber": 118, "oldContent": "            focusedBorder: const UnderlineInputBorder("}, {"type": "DELETE", "lineNumber": 119, "oldContent": "              borderSide: BorderSide(color: primaryGreen, width: 1.0),"}, {"type": "DELETE", "lineNumber": 120, "oldContent": "                : null,"}, {"type": "DELETE", "lineNumber": 121, "oldContent": "                  )"}, {"type": "DELETE", "lineNumber": 122, "oldContent": "                    ),"}, {"type": "DELETE", "lineNumber": 123, "oldContent": "                          : Icons.visibility_outlined,"}, {"type": "DELETE", "lineNumber": 124, "oldContent": "                          ? Icons.visibility_off_outlined"}, {"type": "INSERT", "lineNumber": 109, "content": "      ],"}, {"type": "INSERT", "lineNumber": 110, "content": "    );"}, {"type": "INSERT", "lineNumber": 111, "content": "  }"}, {"type": "INSERT", "lineNumber": 112, "content": ""}, {"type": "INSERT", "lineNumber": 113, "content": "  Widget _buildForm(BuildContext context) {"}, {"type": "INSERT", "lineNumber": 114, "content": "    const Color primaryGreen = Color(0xFF2DB45D);"}, {"type": "INSERT", "lineNumber": 115, "content": ""}, {"type": "INSERT", "lineNumber": 116, "content": "    return Padding("}, {"type": "INSERT", "lineNumber": 117, "content": "      padding: const EdgeInsets.symmetric(horizontal: 32.0),"}, {"type": "INSERT", "lineNumber": 118, "content": "      child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 119, "content": "        crossAxisAlignment: CrossAxisAlignment.stretch,"}, {"type": "INSERT", "lineNumber": 120, "content": "        children: ["}, {"type": "INSERT", "lineNumber": 121, "content": "          const SizedBox(height: 20),"}, {"type": "INSERT", "lineNumber": 122, "content": "          _buildTextField(label: 'الاسم', hint: 'الاسم'),"}, {"type": "INSERT", "lineNumber": 123, "content": "          const SizedBox(height: 20),"}, {"type": "INSERT", "lineNumber": 124, "content": "          _buildTextField("}, {"type": "INSERT", "lineNumber": 125, "content": "            label: 'رقم الهاتف:',"}, {"type": "DELETE", "lineNumber": 126, "oldContent": "                ? Padding("}, {"type": "DELETE", "lineNumber": 127, "oldContent": "            hintStyle: const TextStyle(color: Colors.grey, fontSize: 14),"}, {"type": "DELETE", "lineNumber": 128, "oldContent": "          decoration: InputDecoration("}, {"type": "DELETE", "lineNumber": 129, "oldContent": "          style: const TextStyle(fontSize: 16, color: Colors.black87),"}, {"type": "DELETE", "lineNumber": 130, "oldContent": "          obscureText: obscureText,"}, {"type": "DELETE", "lineNumber": 131, "oldContent": "        ),"}, {"type": "INSERT", "lineNumber": 127, "content": "            keyboardType: TextInputType.phone,"}, {"type": "DELETE", "lineNumber": 133, "oldContent": "            fontSize: 16,"}, {"type": "DELETE", "lineNumber": 134, "oldContent": "          style: const TextStyle("}, {"type": "DELETE", "lineNumber": 135, "oldContent": "          label,"}, {"type": "DELETE", "lineNumber": 136, "oldContent": "      children: ["}, {"type": "DELETE", "lineNumber": 137, "oldContent": "    return Column("}, {"type": "DELETE", "lineNumber": 138, "oldContent": "      crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "DELETE", "lineNumber": 139, "oldContent": "    return Column("}, {"type": "DELETE", "lineNumber": 140, "oldContent": "    const Color primaryGreen = Color(0xFF2DB45D);"}, {"type": "DELETE", "lineNumber": 141, "oldContent": "  }) {"}, {"type": "DELETE", "lineNumber": 142, "oldContent": "    TextInputType? keyboardType,"}, {"type": "DELETE", "lineNumber": 143, "oldContent": "    VoidCallback? onToggleVisibility,"}, {"type": "DELETE", "lineNumber": 144, "oldContent": "    bool obscureText = false,"}, {"type": "DELETE", "lineNumber": 145, "oldContent": "    bool isPassword = false,"}, {"type": "DELETE", "lineNumber": 146, "oldContent": "    Widget? prefix,"}, {"type": "DELETE", "lineNumber": 147, "oldContent": "    required String hint,"}, {"type": "DELETE", "lineNumber": 148, "oldContent": "    required String label,"}, {"type": "INSERT", "lineNumber": 129, "content": "          const SizedBox(height: 20),"}, {"type": "INSERT", "lineNumber": 130, "content": "          _buildTextField("}, {"type": "INSERT", "lineNumber": 131, "content": "            label: 'كلمة المرور',"}, {"type": "INSERT", "lineNumber": 132, "content": "            hint: 'كلمة المرور',"}, {"type": "INSERT", "lineNumber": 133, "content": "            isPassword: true,"}, {"type": "INSERT", "lineNumber": 134, "content": "            obscureText: !_passwordVisible,"}, {"type": "INSERT", "lineNumber": 135, "content": "            onToggleVisibility: () {"}, {"type": "INSERT", "lineNumber": 136, "content": "              setState(() {"}, {"type": "INSERT", "lineNumber": 137, "content": "                _passwordVisible = !_passwordVisible;"}, {"type": "INSERT", "lineNumber": 143, "content": "            label: 'تأكيد كلمة المرور',"}, {"type": "INSERT", "lineNumber": 144, "content": "            hint: 'تأكيد كلمة المرور',"}, {"type": "INSERT", "lineNumber": 145, "content": "            isPassword: true,"}, {"type": "INSERT", "lineNumber": 146, "content": "            obscureText: !_confirmPasswordVisible,"}, {"type": "INSERT", "lineNumber": 147, "content": "            onToggleVisibility: () {"}, {"type": "INSERT", "lineNumber": 148, "content": "              setState(() {"}, {"type": "INSERT", "lineNumber": 149, "content": "                _confirmPasswordVisible = !_confirmPasswordVisible;"}, {"type": "INSERT", "lineNumber": 150, "content": "              });"}, {"type": "INSERT", "lineNumber": 151, "content": "            },"}, {"type": "INSERT", "lineNumber": 152, "content": "          ),"}, {"type": "INSERT", "lineNumber": 153, "content": "          const SizedBox(height: 20),"}, {"type": "INSERT", "lineNumber": 154, "content": "          _buildTextField("}, {"type": "DELETE", "lineNumber": 163, "oldContent": "                    child: prefix)"}, {"type": "DELETE", "lineNumber": 164, "oldContent": "                ? Padding("}, {"type": "DELETE", "lineNumber": 165, "oldContent": "            prefixIcon: prefix != null"}, {"type": "DELETE", "lineNumber": 166, "oldContent": "            hintStyle: const TextStyle(color: Colors.grey, fontSize: 14),"}, {"type": "DELETE", "lineNumber": 167, "oldContent": "          style: const TextStyle(fontSize: 16, color: Colors.black87),"}, {"type": "DELETE", "lineNumber": 168, "oldContent": "          keyboardType: keyboardType,"}, {"type": "DELETE", "lineNumber": 169, "oldContent": "        TextField("}, {"type": "DELETE", "lineNumber": 170, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 171, "oldContent": "            color: Colors.black,"}, {"type": "DELETE", "lineNumber": 172, "oldContent": "            fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 173, "oldContent": "          style: const TextStyle("}, {"type": "INSERT", "lineNumber": 164, "content": "            'تملك حساب؟',"}, {"type": "INSERT", "lineNumber": 165, "content": "            textAlign: TextAlign.center,"}, {"type": "INSERT", "lineNumber": 166, "content": "            style: TextStyle(color: Colors.black54, fontSize: 14),"}, {"type": "INSERT", "lineNumber": 167, "content": "          ),"}, {"type": "INSERT", "lineNumber": 168, "content": "          const SizedBox(height: 10),"}, {"type": "INSERT", "lineNumber": 169, "content": "          _build<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(primaryGreen),"}, {"type": "INSERT", "lineNumber": 170, "content": "          const SizedBox(height: 20),"}, {"type": "INSERT", "lineNumber": 171, "content": "        ],"}, {"type": "INSERT", "lineNumber": 172, "content": "      ),"}, {"type": "INSERT", "lineNumber": 173, "content": "    );"}, {"type": "INSERT", "lineNumber": 174, "content": "  }"}, {"type": "DELETE", "lineNumber": 182, "oldContent": "          ),"}, {"type": "INSERT", "lineNumber": 183, "content": "    TextInputType? keyboardType,"}, {"type": "DELETE", "lineNumber": 192, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 193, "oldContent": "//     return false;"}, {"type": "DELETE", "lineNumber": 194, "oldContent": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "DELETE", "lineNumber": 195, "oldContent": "//   @override"}, {"type": "DELETE", "lineNumber": 196, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 197, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 198, "oldContent": "//     return path;"}, {"type": "DELETE", "lineNumber": 199, "oldContent": "//     path.close();"}, {"type": "DELETE", "lineNumber": 200, "oldContent": "//     path.lineTo(size.width, 0);"}, {"type": "DELETE", "lineNumber": 201, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 202, "oldContent": "//       size.height * 0.7,"}, {"type": "DELETE", "lineNumber": 203, "oldContent": "//       size.width,"}, {"type": "DELETE", "lineNumber": 204, "oldContent": "//       size.height * 0.65,"}, {"type": "DELETE", "lineNumber": 205, "oldContent": "//       size.width * 0.85,"}, {"type": "DELETE", "lineNumber": 206, "oldContent": "//     path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 207, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 208, "oldContent": "//       size.height * 0.85,"}, {"type": "DELETE", "lineNumber": 209, "oldContent": "//       size.width * 0.5,"}, {"type": "DELETE", "lineNumber": 210, "oldContent": "//       size.height,"}, {"type": "DELETE", "lineNumber": 211, "oldContent": "//       size.width * 0.2,"}, {"type": "DELETE", "lineNumber": 212, "oldContent": "//     path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 213, "oldContent": "//     path.lineTo(0, size.height * 0.75);"}, {"type": "DELETE", "lineNumber": 214, "oldContent": "//     final path = Path();"}, {"type": "DELETE", "lineNumber": 215, "oldContent": "//   Path getClip(Size size) {"}, {"type": "INSERT", "lineNumber": 193, "content": "            fontSize: 16,"}, {"type": "INSERT", "lineNumber": 194, "content": "            color: Colors.black,"}, {"type": "INSERT", "lineNumber": 195, "content": "          ),"}, {"type": "INSERT", "lineNumber": 196, "content": "        ),"}, {"type": "INSERT", "lineNumber": 197, "content": "        TextField("}, {"type": "INSERT", "lineNumber": 198, "content": "          obscureText: obscureText,"}, {"type": "INSERT", "lineNumber": 199, "content": "          keyboardType: keyboardType,"}, {"type": "INSERT", "lineNumber": 200, "content": "          style: const TextStyle(fontSize: 16, color: Colors.black87),"}, {"type": "INSERT", "lineNumber": 201, "content": "          decoration: InputDecoration("}, {"type": "INSERT", "lineNumber": 202, "content": "            hintText: hint,"}, {"type": "INSERT", "lineNumber": 203, "content": "            hintStyle: const TextStyle(color: Colors.grey, fontSize: 14),"}, {"type": "INSERT", "lineNumber": 204, "content": "            prefixIcon: prefix != null"}, {"type": "INSERT", "lineNumber": 205, "content": "                ? Padding("}, {"type": "INSERT", "lineNumber": 206, "content": "                    padding: const EdgeInsets.only(top: 12.0, left: 8.0),"}, {"type": "INSERT", "lineNumber": 207, "content": "                    child: prefix)"}, {"type": "INSERT", "lineNumber": 208, "content": "                : null,"}, {"type": "INSERT", "lineNumber": 209, "content": "            prefixIconConstraints:"}, {"type": "INSERT", "lineNumber": 210, "content": "                const BoxConstraints(minWidth: 0, minHeight: 0),"}, {"type": "INSERT", "lineNumber": 211, "content": "            suffixIcon: isPassword"}, {"type": "INSERT", "lineNumber": 212, "content": "                ? IconButton("}, {"type": "INSERT", "lineNumber": 213, "content": "                    icon: Icon("}, {"type": "INSERT", "lineNumber": 214, "content": "                      obscureText"}, {"type": "INSERT", "lineNumber": 215, "content": "                          ? Icons.visibility_off_outlined"}, {"type": "INSERT", "lineNumber": 216, "content": "                          : Icons.visibility_outlined,"}, {"type": "DELETE", "lineNumber": 218, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 230, "oldContent": "// import 'package:dropx/src/app.dart';"}, {"type": "DELETE", "lineNumber": 236, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 238, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 243, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 246, "oldContent": "//     path.close();"}, {"type": "DELETE", "lineNumber": 248, "oldContent": "//     path.lineTo(size.width, 0);"}, {"type": "DELETE", "lineNumber": 250, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 252, "oldContent": "//       size.height * 0.7,"}, {"type": "DELETE", "lineNumber": 254, "oldContent": "//       size.width,"}, {"type": "DELETE", "lineNumber": 256, "oldContent": "//       size.height * 0.65,"}, {"type": "DELETE", "lineNumber": 258, "oldContent": "//       size.width * 0.85,"}, {"type": "DELETE", "lineNumber": 260, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 262, "oldContent": "//       size.height * 0.85,"}, {"type": "DELETE", "lineNumber": 264, "oldContent": "//       size.width * 0.5,"}, {"type": "DELETE", "lineNumber": 266, "oldContent": "//       size.height,"}, {"type": "DELETE", "lineNumber": 268, "oldContent": "//     path.lineTo(0, size.height * 0.75);"}, {"type": "DELETE", "lineNumber": 270, "oldContent": "//     final path = Path();"}, {"type": "DELETE", "lineNumber": 272, "oldContent": "//   Path getClip(Size size) {"}, {"type": "DELETE", "lineNumber": 274, "oldContent": "// class HeaderClipper extends CustomClipper<Path> {"}, {"type": "DELETE", "lineNumber": 276, "oldContent": ""}, {"type": "DELETE", "lineNumber": 278, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 280, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 282, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 284, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 286, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 288, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 290, "oldContent": "            color: primary<PERSON>reen,"}, {"type": "DELETE", "lineNumber": 292, "oldContent": "            fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 294, "oldContent": "            fontSize: 18,"}, {"type": "DELETE", "lineNumber": 296, "oldContent": "          style: TextStyle("}, {"type": "DELETE", "lineNumber": 298, "oldContent": "          'تسجيل دخول',"}, {"type": "DELETE", "lineNumber": 300, "oldContent": "        child: Text("}, {"type": "DELETE", "lineNumber": 302, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 304, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 306, "oldContent": "            borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 308, "oldContent": "          shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 310, "oldContent": "          side: BorderSide(color: primaryGreen, width: 1.5),"}, {"type": "DELETE", "lineNumber": 312, "oldContent": "          foregroundColor: primaryGreen,"}, {"type": "DELETE", "lineNumber": 314, "oldContent": "        style: OutlinedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 316, "oldContent": "        onPressed: () {},"}, {"type": "DELETE", "lineNumber": 318, "oldContent": "      child: Out<PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 320, "oldContent": "      height: 50,"}, {"type": "DELETE", "lineNumber": 322, "oldContent": "    return SizedBox("}, {"type": "DELETE", "lineNumber": 324, "oldContent": "  Widget _buildLoginButton(Color primaryGreen) {"}, {"type": "DELETE", "lineNumber": 327, "oldContent": ""}, {"type": "DELETE", "lineNumber": 328, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 330, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 332, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 334, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 336, "oldContent": "          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),"}, {"type": "DELETE", "lineNumber": 338, "oldContent": "          'انشاء حساب',"}, {"type": "DELETE", "lineNumber": 340, "oldContent": "        child: const Text("}, {"type": "DELETE", "lineNumber": 342, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 344, "oldContent": "          elevation: 0,"}, {"type": "INSERT", "lineNumber": 292, "content": "            borderRadius: BorderRadius.circular(12),"}, {"type": "INSERT", "lineNumber": 294, "content": "          elevation: 0,"}, {"type": "INSERT", "lineNumber": 295, "content": "        ),"}, {"type": "INSERT", "lineNumber": 296, "content": "        child: const Text("}, {"type": "INSERT", "lineNumber": 297, "content": "          'انشاء حساب',"}, {"type": "INSERT", "lineNumber": 298, "content": "          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),"}, {"type": "INSERT", "lineNumber": 299, "content": "        ),"}, {"type": "INSERT", "lineNumber": 300, "content": "      ),"}, {"type": "INSERT", "lineNumber": 301, "content": "    );"}, {"type": "INSERT", "lineNumber": 302, "content": "  }"}, {"type": "INSERT", "lineNumber": 303, "content": ""}, {"type": "INSERT", "lineNumber": 304, "content": "  Widget _buildLoginButton(Color primaryGreen) {"}, {"type": "INSERT", "lineNumber": 305, "content": "    return SizedBox("}, {"type": "INSERT", "lineNumber": 306, "content": "      height: 50,"}, {"type": "INSERT", "lineNumber": 307, "content": "      child: Out<PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 308, "content": "        onPressed: () {},"}, {"type": "INSERT", "lineNumber": 309, "content": "        style: OutlinedButton.styleFrom("}, {"type": "INSERT", "lineNumber": 310, "content": "          foregroundColor: primaryGreen,"}, {"type": "INSERT", "lineNumber": 311, "content": "          side: BorderSide(color: primaryGreen, width: 1.5),"}, {"type": "INSERT", "lineNumber": 312, "content": "          shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 314, "content": "          ),"}, {"type": "INSERT", "lineNumber": 315, "content": "        ),"}, {"type": "INSERT", "lineNumber": 316, "content": "        child: Text("}, {"type": "INSERT", "lineNumber": 317, "content": "          'تسجيل دخول',"}, {"type": "INSERT", "lineNumber": 318, "content": "          style: TextStyle("}, {"type": "INSERT", "lineNumber": 319, "content": "            fontSize: 18,"}, {"type": "INSERT", "lineNumber": 320, "content": "            fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 321, "content": "            color: primary<PERSON>reen,"}, {"type": "INSERT", "lineNumber": 322, "content": "          ),"}, {"type": "INSERT", "lineNumber": 323, "content": "        ),"}, {"type": "INSERT", "lineNumber": 324, "content": "      ),"}, {"type": "INSERT", "lineNumber": 325, "content": "    );"}, {"type": "INSERT", "lineNumber": 326, "content": "  }"}, {"type": "INSERT", "lineNumber": 327, "content": "}"}, {"type": "INSERT", "lineNumber": 328, "content": ""}, {"type": "INSERT", "lineNumber": 329, "content": "// class HeaderClipper extends CustomClipper<Path> {"}, {"type": "INSERT", "lineNumber": 330, "content": "//   @override"}, {"type": "INSERT", "lineNumber": 331, "content": "//   Path getClip(Size size) {"}, {"type": "INSERT", "lineNumber": 332, "content": "//     final path = Path();"}, {"type": "INSERT", "lineNumber": 333, "content": "//     path.lineTo(0, size.height * 0.75);"}, {"type": "INSERT", "lineNumber": 334, "content": "//     path.quadraticBezierTo("}, {"type": "INSERT", "lineNumber": 335, "content": "//       size.width * 0.2,"}, {"type": "INSERT", "lineNumber": 336, "content": "//       size.height,"}, {"type": "INSERT", "lineNumber": 337, "content": "//       size.width * 0.5,"}, {"type": "INSERT", "lineNumber": 338, "content": "//       size.height * 0.85,"}, {"type": "INSERT", "lineNumber": 339, "content": "//     );"}, {"type": "INSERT", "lineNumber": 340, "content": "//     path.quadraticBezierTo("}, {"type": "INSERT", "lineNumber": 341, "content": "//       size.width * 0.85,"}, {"type": "INSERT", "lineNumber": 342, "content": "//       size.height * 0.65,"}, {"type": "INSERT", "lineNumber": 343, "content": "//       size.width,"}, {"type": "INSERT", "lineNumber": 344, "content": "//       size.height * 0.7,"}, {"type": "INSERT", "lineNumber": 345, "content": "//     );"}, {"type": "INSERT", "lineNumber": 346, "content": "//     path.lineTo(size.width, 0);"}, {"type": "INSERT", "lineNumber": 347, "content": "//     path.close();"}, {"type": "INSERT", "lineNumber": 348, "content": "//     return path;"}, {"type": "INSERT", "lineNumber": 349, "content": "//   }"}, {"type": "INSERT", "lineNumber": 350, "content": "//"}, {"type": "INSERT", "lineNumber": 351, "content": "//   @override"}, {"type": "INSERT", "lineNumber": 352, "content": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 353, "content": "//     return false;"}, {"type": "INSERT", "lineNumber": 354, "content": "//   }"}, {"type": "INSERT", "lineNumber": 355, "content": "// }"}, {"type": "INSERT", "lineNumber": 356, "content": "// import 'dart:io';"}, {"type": "INSERT", "lineNumber": 357, "content": "//"}, {"type": "INSERT", "lineNumber": 358, "content": "// import 'package:flutter/material.dart';"}, {"type": "INSERT", "lineNumber": 359, "content": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "INSERT", "lineNumber": 360, "content": "// import 'package:dropx/src/app.dart';"}, {"type": "INSERT", "lineNumber": 361, "content": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "INSERT", "lineNumber": 362, "content": "//"}, {"type": "INSERT", "lineNumber": 363, "content": "// void main() async {"}, {"type": "INSERT", "lineNumber": 364, "content": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "INSERT", "lineNumber": 365, "content": "//"}, {"type": "INSERT", "lineNumber": 366, "content": "//   await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 367, "content": "//"}, {"type": "INSERT", "lineNumber": 368, "content": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 369, "content": "//"}, {"type": "INSERT", "lineNumber": 370, "content": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 371, "content": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 372, "content": "// }"}, {"type": "INSERT", "lineNumber": 373, "content": ""}]}, {"timestamp": 1756969486228, "changes": [{"type": "DELETE", "lineNumber": 10, "oldContent": "class MyApp extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "INSERT", "lineNumber": 11, "content": "class MyApp extends StatelessWidget {"}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "DELETE", "lineNumber": 24, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 26, "oldContent": ""}, {"type": "INSERT", "lineNumber": 27, "content": "}"}, {"type": "INSERT", "lineNumber": 28, "content": ""}, {"type": "MODIFY", "lineNumber": 32, "content": "  @override", "oldContent": "  @override"}, {"type": "INSERT", "lineNumber": 58, "content": "            ),"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "                : null,"}, {"type": "MODIFY", "lineNumber": 63, "content": "  }", "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "          height: 200,"}, {"type": "MODIFY", "lineNumber": 72, "content": "        SafeArea(", "oldContent": "        SafeArea("}, {"type": "DELETE", "lineNumber": 75, "oldContent": "        Text("}, {"type": "DELETE", "lineNumber": 76, "oldContent": "      crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "INSERT", "lineNumber": 74, "content": "            padding:"}, {"type": "INSERT", "lineNumber": 75, "content": "                const EdgeInsets.symmetric(horizontal: 24.0, vertical: 20.0),"}, {"type": "MODIFY", "lineNumber": 80, "content": "                const Padding(", "oldContent": "                const Padding("}, {"type": "MODIFY", "lineNumber": 104, "content": "              ],", "oldContent": "              ],"}, {"type": "INSERT", "lineNumber": 107, "content": "        ),"}, {"type": "DELETE", "lineNumber": 115, "oldContent": "                    child: prefix)"}, {"type": "INSERT", "lineNumber": 127, "content": "          ),"}, {"type": "DELETE", "lineNumber": 132, "oldContent": "      children: ["}, {"type": "INSERT", "lineNumber": 137, "content": "              });"}, {"type": "INSERT", "lineNumber": 138, "content": "            },"}, {"type": "INSERT", "lineNumber": 139, "content": "          ),"}, {"type": "INSERT", "lineNumber": 140, "content": "          const SizedBox(height: 20),"}, {"type": "INSERT", "lineNumber": 141, "content": "          _buildTextField("}, {"type": "DELETE", "lineNumber": 145, "oldContent": "          style: const TextStyle("}, {"type": "DELETE", "lineNumber": 147, "oldContent": "          label,"}, {"type": "DELETE", "lineNumber": 149, "oldContent": "        Text("}, {"type": "DELETE", "lineNumber": 151, "oldContent": "          label,"}, {"type": "DELETE", "lineNumber": 153, "oldContent": "        Text("}, {"type": "DELETE", "lineNumber": 155, "oldContent": "      children: ["}, {"type": "DELETE", "lineNumber": 156, "oldContent": "      crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "DELETE", "lineNumber": 157, "oldContent": ""}, {"type": "DELETE", "lineNumber": 158, "oldContent": "            fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 159, "oldContent": "          style: const TextStyle("}, {"type": "DELETE", "lineNumber": 160, "oldContent": "    const Color primaryGreen = Color(0xFF2DB45D);"}, {"type": "DELETE", "lineNumber": 161, "oldContent": "            fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 162, "oldContent": "          style: const TextStyle("}, {"type": "DELETE", "lineNumber": 163, "oldContent": "    const Color primaryGreen = Color(0xFF2DB45D);"}, {"type": "INSERT", "lineNumber": 154, "content": "              label: 'رقم الهوية',"}, {"type": "INSERT", "lineNumber": 155, "content": "              hint: 'رقم الهوية',"}, {"type": "INSERT", "lineNumber": 156, "content": "              keyboardType: TextInputType.number),"}, {"type": "INSERT", "lineNumber": 157, "content": "          const SizedBox(height: 20),"}, {"type": "INSERT", "lineNumber": 158, "content": "          _buildTermsAndConditions(),"}, {"type": "INSERT", "lineNumber": 159, "content": "          const SizedBox(height: 30),"}, {"type": "INSERT", "lineNumber": 160, "content": "          _buildSignUpButton(),"}, {"type": "INSERT", "lineNumber": 161, "content": "          const SizedBox(height: 20),"}, {"type": "INSERT", "lineNumber": 162, "content": "          const Text("}, {"type": "DELETE", "lineNumber": 175, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 176, "oldContent": "//       size.height * 0.7,"}, {"type": "DELETE", "lineNumber": 177, "oldContent": "//       size.width,"}, {"type": "DELETE", "lineNumber": 178, "oldContent": "//       size.height * 0.65,"}, {"type": "DELETE", "lineNumber": 179, "oldContent": "//       size.width * 0.85,"}, {"type": "DELETE", "lineNumber": 180, "oldContent": "//     path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 181, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 182, "oldContent": "//       size.height * 0.85,"}, {"type": "INSERT", "lineNumber": 174, "content": ""}, {"type": "INSERT", "lineNumber": 175, "content": "  Widget _buildTextField({"}, {"type": "INSERT", "lineNumber": 176, "content": "    required String label,"}, {"type": "INSERT", "lineNumber": 177, "content": "    required String hint,"}, {"type": "INSERT", "lineNumber": 178, "content": "    Widget? prefix,"}, {"type": "INSERT", "lineNumber": 179, "content": "    bool isPassword = false,"}, {"type": "INSERT", "lineNumber": 180, "content": "    bool obscureText = false,"}, {"type": "INSERT", "lineNumber": 181, "content": "    VoidCallback? onToggleVisibility,"}, {"type": "DELETE", "lineNumber": 185, "oldContent": "//       size.height,"}, {"type": "DELETE", "lineNumber": 186, "oldContent": "//     path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 187, "oldContent": "//     path.lineTo(0, size.height * 0.75);"}, {"type": "DELETE", "lineNumber": 188, "oldContent": "//     final path = Path();"}, {"type": "DELETE", "lineNumber": 189, "oldContent": "//   Path getClip(Size size) {"}, {"type": "INSERT", "lineNumber": 184, "content": "    const Color primaryGreen = Color(0xFF2DB45D);"}, {"type": "INSERT", "lineNumber": 185, "content": "    return Column("}, {"type": "INSERT", "lineNumber": 186, "content": "      crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "INSERT", "lineNumber": 187, "content": "      children: ["}, {"type": "INSERT", "lineNumber": 188, "content": "        Text("}, {"type": "DELETE", "lineNumber": 191, "oldContent": "//   @override"}, {"type": "DELETE", "lineNumber": 192, "oldContent": "// class HeaderClipper extends CustomClipper<Path> {"}, {"type": "INSERT", "lineNumber": 190, "content": "          style: const TextStyle("}, {"type": "INSERT", "lineNumber": 191, "content": "            fontWeight: FontWeight.bold,"}, {"type": "MODIFY", "lineNumber": 265, "content": "                    color: Color(0xFF3366CC),", "oldContent": "            borderRadius: BorderRadius.circular(12),"}, {"type": "INSERT", "lineNumber": 266, "content": "                    decoration: TextDecoration.underline,"}, {"type": "INSERT", "lineNumber": 267, "content": "                  ),"}, {"type": "INSERT", "lineNumber": 268, "content": "                  recognizer: TapGestureRecognizer()"}, {"type": "INSERT", "lineNumber": 269, "content": "                    ..onTap = () {"}, {"type": "INSERT", "lineNumber": 270, "content": "                      // Handle terms and conditions tap"}, {"type": "INSERT", "lineNumber": 271, "content": "                    },"}, {"type": "INSERT", "lineNumber": 272, "content": "                ),"}, {"type": "INSERT", "lineNumber": 273, "content": "                const TextSpan(text: ' الخاصة باستخدام هذا التطبيق'),"}, {"type": "INSERT", "lineNumber": 274, "content": "              ],"}, {"type": "INSERT", "lineNumber": 275, "content": "            ),"}, {"type": "INSERT", "lineNumber": 276, "content": "          ),"}, {"type": "INSERT", "lineNumber": 277, "content": "        ),"}, {"type": "INSERT", "lineNumber": 278, "content": "      ],"}, {"type": "INSERT", "lineNumber": 279, "content": "    );"}, {"type": "INSERT", "lineNumber": 280, "content": "  }"}, {"type": "INSERT", "lineNumber": 281, "content": ""}, {"type": "INSERT", "lineNumber": 282, "content": "  Widget _buildSignUpButton() {"}, {"type": "INSERT", "lineNumber": 283, "content": "    return SizedBox("}, {"type": "INSERT", "lineNumber": 284, "content": "      height: 50,"}, {"type": "INSERT", "lineNumber": 285, "content": "      child: El<PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 286, "content": "        onPressed: () {},"}, {"type": "INSERT", "lineNumber": 287, "content": "        style: ElevatedButton.styleFrom("}, {"type": "INSERT", "lineNumber": 288, "content": "          backgroundColor: const Color(0xFFC4C4C4),"}, {"type": "INSERT", "lineNumber": 289, "content": "          foregroundColor: Colors.black,"}, {"type": "INSERT", "lineNumber": 290, "content": "          shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 291, "content": "            borderRadius: BorderRadius.circular(12),"}, {"type": "INSERT", "lineNumber": 292, "content": "          ),"}, {"type": "DELETE", "lineNumber": 269, "oldContent": "                    color: Color(0xFF3366CC),"}, {"type": "DELETE", "lineNumber": 272, "oldContent": "                    decoration: TextDecoration.underline,"}, {"type": "DELETE", "lineNumber": 275, "oldContent": "                  ),"}, {"type": "DELETE", "lineNumber": 278, "oldContent": "                  recognizer: TapGestureRecognizer()"}, {"type": "DELETE", "lineNumber": 281, "oldContent": "                    ..onTap = () {"}, {"type": "DELETE", "lineNumber": 284, "oldContent": "                      // Handle terms and conditions tap"}, {"type": "DELETE", "lineNumber": 287, "oldContent": "                    },"}, {"type": "DELETE", "lineNumber": 290, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 293, "oldContent": "                const TextSpan(text: ' الخاصة باستخدام هذا التطبيق'),"}, {"type": "DELETE", "lineNumber": 295, "oldContent": "              ],"}, {"type": "INSERT", "lineNumber": 312, "content": "            borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 298, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 301, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 304, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 307, "oldContent": "      ],"}, {"type": "DELETE", "lineNumber": 310, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 313, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 315, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 319, "oldContent": "//   await GetStorageService.init();"}, {"type": "DELETE", "lineNumber": 322, "oldContent": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "DELETE", "lineNumber": 325, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 328, "oldContent": "// import 'package:dropx/src/app.dart';"}, {"type": "DELETE", "lineNumber": 331, "oldContent": "// import 'package:flutter/material.dart';"}, {"type": "DELETE", "lineNumber": 334, "oldContent": "// import 'dart:io';"}, {"type": "DELETE", "lineNumber": 337, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 340, "oldContent": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "DELETE", "lineNumber": 343, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 345, "oldContent": "//   }"}, {"type": "INSERT", "lineNumber": 348, "content": "//   }"}, {"type": "INSERT", "lineNumber": 349, "content": "//"}, {"type": "INSERT", "lineNumber": 350, "content": "//   @override"}, {"type": "INSERT", "lineNumber": 351, "content": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 352, "content": "//     return false;"}, {"type": "INSERT", "lineNumber": 353, "content": "//   }"}, {"type": "INSERT", "lineNumber": 354, "content": "// }"}, {"type": "INSERT", "lineNumber": 355, "content": "// import 'dart:io';"}, {"type": "INSERT", "lineNumber": 356, "content": "//"}, {"type": "INSERT", "lineNumber": 357, "content": "// import 'package:flutter/material.dart';"}, {"type": "INSERT", "lineNumber": 358, "content": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "INSERT", "lineNumber": 359, "content": "// import 'package:dropx/src/app.dart';"}, {"type": "INSERT", "lineNumber": 360, "content": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "INSERT", "lineNumber": 361, "content": "//"}, {"type": "INSERT", "lineNumber": 362, "content": "// void main() async {"}, {"type": "INSERT", "lineNumber": 363, "content": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "INSERT", "lineNumber": 364, "content": "//"}, {"type": "INSERT", "lineNumber": 365, "content": "//   await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 366, "content": "//"}, {"type": "INSERT", "lineNumber": 367, "content": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 368, "content": "//"}, {"type": "INSERT", "lineNumber": 369, "content": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 370, "content": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 371, "content": "// }"}, {"type": "INSERT", "lineNumber": 372, "content": ""}]}, {"timestamp": 1756969526756, "changes": [{"type": "DELETE", "lineNumber": 10, "oldContent": "class MyApp extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "INSERT", "lineNumber": 11, "content": "class MyApp extends StatelessWidget {"}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "DELETE", "lineNumber": 25, "oldContent": "}"}, {"type": "MODIFY", "lineNumber": 27, "content": "}", "oldContent": ""}, {"type": "INSERT", "lineNumber": 28, "content": ""}, {"type": "INSERT", "lineNumber": 31, "content": ""}, {"type": "DELETE", "lineNumber": 32, "oldContent": "  @override"}, {"type": "INSERT", "lineNumber": 62, "content": "    );"}, {"type": "DELETE", "lineNumber": 63, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "        SafeArea("}, {"type": "DELETE", "lineNumber": 73, "oldContent": "            padding:"}, {"type": "INSERT", "lineNumber": 72, "content": "        SafeArea("}, {"type": "DELETE", "lineNumber": 75, "oldContent": "                const EdgeInsets.symmetric(horizontal: 24.0, vertical: 20.0),"}, {"type": "INSERT", "lineNumber": 74, "content": "            padding:"}, {"type": "INSERT", "lineNumber": 75, "content": "                const EdgeInsets.symmetric(horizontal: 24.0, vertical: 5.0),"}, {"type": "MODIFY", "lineNumber": 80, "content": "                const Padding(", "oldContent": "                const Padding("}, {"type": "MODIFY", "lineNumber": 104, "content": "              ],", "oldContent": "              ],"}, {"type": "MODIFY", "lineNumber": 107, "content": "        ),", "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 125, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 126, "oldContent": "          ),"}, {"type": "INSERT", "lineNumber": 125, "content": "            hint: '05xxxxxxxx',"}, {"type": "INSERT", "lineNumber": 127, "content": "          ),"}, {"type": "MODIFY", "lineNumber": 137, "content": "              });", "oldContent": "              });"}, {"type": "DELETE", "lineNumber": 139, "oldContent": "            label: 'تأكيد كلمة المرور',"}, {"type": "DELETE", "lineNumber": 141, "oldContent": "            hint: 'تأكيد كلمة المرور',"}, {"type": "DELETE", "lineNumber": 143, "oldContent": "            isPassword: true,"}, {"type": "INSERT", "lineNumber": 142, "content": "            label: 'تأكيد كلمة المرور',"}, {"type": "INSERT", "lineNumber": 143, "content": "            hint: 'تأكيد كلمة المرور',"}, {"type": "INSERT", "lineNumber": 144, "content": "            isPassword: true,"}, {"type": "MODIFY", "lineNumber": 154, "content": "              label: 'رقم الهوية',", "oldContent": "              label: 'رقم الهوية',"}, {"type": "MODIFY", "lineNumber": 174, "content": "", "oldContent": ""}, {"type": "INSERT", "lineNumber": 183, "content": "  }) {"}, {"type": "DELETE", "lineNumber": 184, "oldContent": "//       size.width * 0.5,"}, {"type": "INSERT", "lineNumber": 189, "content": "          label,"}, {"type": "DELETE", "lineNumber": 190, "oldContent": ""}, {"type": "DELETE", "lineNumber": 257, "oldContent": ""}, {"type": "DELETE", "lineNumber": 258, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 259, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 260, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 261, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 262, "oldContent": "// void main() async {"}, {"type": "DELETE", "lineNumber": 263, "oldContent": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "INSERT", "lineNumber": 257, "content": "          child: <PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 258, "content": "            text: TextSpan("}, {"type": "INSERT", "lineNumber": 259, "content": "              style: GoogleFonts.cairo(fontSize: 13, color: Colors.black87),"}, {"type": "INSERT", "lineNumber": 260, "content": "              children: ["}, {"type": "INSERT", "lineNumber": 261, "content": "                const TextSpan(text: 'أقر بأنني قد قرأت وفهمت وأوافق على '),"}, {"type": "INSERT", "lineNumber": 262, "content": "                TextSpan("}, {"type": "INSERT", "lineNumber": 263, "content": "                  text: 'الشروط والأحكام',"}, {"type": "INSERT", "lineNumber": 264, "content": "                  style: const TextStyle("}, {"type": "DELETE", "lineNumber": 266, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 268, "oldContent": "          elevation: 0,"}, {"type": "DELETE", "lineNumber": 270, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 273, "oldContent": "        child: const Text("}, {"type": "DELETE", "lineNumber": 275, "oldContent": "          'انشاء حساب',"}, {"type": "DELETE", "lineNumber": 278, "oldContent": "          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),"}, {"type": "DELETE", "lineNumber": 280, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 283, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 285, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 289, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 291, "oldContent": ""}, {"type": "DELETE", "lineNumber": 293, "oldContent": "  Widget _buildLoginButton(Color primaryGreen) {"}, {"type": "DELETE", "lineNumber": 295, "oldContent": "    return SizedBox("}, {"type": "DELETE", "lineNumber": 298, "oldContent": "      height: 50,"}, {"type": "DELETE", "lineNumber": 300, "oldContent": "      child: Out<PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 303, "oldContent": "        onPressed: () {},"}, {"type": "DELETE", "lineNumber": 305, "oldContent": "        style: OutlinedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 308, "oldContent": "          foregroundColor: primaryGreen,"}, {"type": "INSERT", "lineNumber": 293, "content": "          elevation: 0,"}, {"type": "INSERT", "lineNumber": 294, "content": "        ),"}, {"type": "INSERT", "lineNumber": 295, "content": "        child: const Text("}, {"type": "INSERT", "lineNumber": 296, "content": "          'انشاء حساب',"}, {"type": "INSERT", "lineNumber": 297, "content": "          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),"}, {"type": "INSERT", "lineNumber": 298, "content": "        ),"}, {"type": "INSERT", "lineNumber": 299, "content": "      ),"}, {"type": "INSERT", "lineNumber": 300, "content": "    );"}, {"type": "INSERT", "lineNumber": 301, "content": "  }"}, {"type": "INSERT", "lineNumber": 302, "content": ""}, {"type": "INSERT", "lineNumber": 303, "content": "  Widget _buildLoginButton(Color primaryGreen) {"}, {"type": "INSERT", "lineNumber": 304, "content": "    return SizedBox("}, {"type": "INSERT", "lineNumber": 305, "content": "      height: 50,"}, {"type": "INSERT", "lineNumber": 306, "content": "      child: Out<PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 307, "content": "        onPressed: () {},"}, {"type": "INSERT", "lineNumber": 308, "content": "        style: OutlinedButton.styleFrom("}, {"type": "INSERT", "lineNumber": 309, "content": "          foregroundColor: primaryGreen,"}, {"type": "INSERT", "lineNumber": 312, "content": "            borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 323, "oldContent": "            borderRadius: BorderRadius.circular(12),"}, {"type": "INSERT", "lineNumber": 326, "content": "}"}, {"type": "INSERT", "lineNumber": 327, "content": ""}, {"type": "INSERT", "lineNumber": 328, "content": "// class HeaderClipper extends CustomClipper<Path> {"}, {"type": "INSERT", "lineNumber": 329, "content": "//   @override"}, {"type": "INSERT", "lineNumber": 330, "content": "//   Path getClip(Size size) {"}, {"type": "INSERT", "lineNumber": 331, "content": "//     final path = Path();"}, {"type": "INSERT", "lineNumber": 332, "content": "//     path.lineTo(0, size.height * 0.75);"}, {"type": "INSERT", "lineNumber": 333, "content": "//     path.quadraticBezierTo("}, {"type": "INSERT", "lineNumber": 334, "content": "//       size.width * 0.2,"}, {"type": "INSERT", "lineNumber": 335, "content": "//       size.height,"}, {"type": "INSERT", "lineNumber": 336, "content": "//       size.width * 0.5,"}, {"type": "INSERT", "lineNumber": 337, "content": "//       size.height * 0.85,"}, {"type": "INSERT", "lineNumber": 338, "content": "//     );"}, {"type": "INSERT", "lineNumber": 339, "content": "//     path.quadraticBezierTo("}, {"type": "INSERT", "lineNumber": 340, "content": "//       size.width * 0.85,"}, {"type": "INSERT", "lineNumber": 341, "content": "//       size.height * 0.65,"}, {"type": "INSERT", "lineNumber": 342, "content": "//       size.width,"}, {"type": "INSERT", "lineNumber": 343, "content": "//       size.height * 0.7,"}, {"type": "INSERT", "lineNumber": 344, "content": "//     );"}, {"type": "INSERT", "lineNumber": 345, "content": "//     path.lineTo(size.width, 0);"}, {"type": "INSERT", "lineNumber": 346, "content": "//     path.close();"}, {"type": "INSERT", "lineNumber": 347, "content": "//     return path;"}, {"type": "INSERT", "lineNumber": 348, "content": "//   }"}, {"type": "INSERT", "lineNumber": 349, "content": "//"}, {"type": "INSERT", "lineNumber": 350, "content": "//   @override"}, {"type": "INSERT", "lineNumber": 351, "content": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 352, "content": "//     return false;"}, {"type": "INSERT", "lineNumber": 353, "content": "//   }"}, {"type": "DELETE", "lineNumber": 327, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 328, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 329, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 355, "content": "// import 'dart:io';"}, {"type": "INSERT", "lineNumber": 357, "content": "// import 'package:flutter/material.dart';"}, {"type": "INSERT", "lineNumber": 358, "content": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "INSERT", "lineNumber": 359, "content": "// import 'package:dropx/src/app.dart';"}, {"type": "INSERT", "lineNumber": 360, "content": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "INSERT", "lineNumber": 362, "content": "// void main() async {"}, {"type": "DELETE", "lineNumber": 334, "oldContent": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "DELETE", "lineNumber": 335, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 336, "oldContent": "// import 'package:flutter/material.dart';"}, {"type": "DELETE", "lineNumber": 337, "oldContent": "// import 'dart:io';"}, {"type": "DELETE", "lineNumber": 338, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 339, "oldContent": "//     return false;"}, {"type": "DELETE", "lineNumber": 340, "oldContent": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 365, "content": "//   await GetStorageService.init();"}, {"type": "DELETE", "lineNumber": 342, "oldContent": "//   }"}, {"type": "INSERT", "lineNumber": 367, "content": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 368, "content": "//"}, {"type": "INSERT", "lineNumber": 369, "content": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 370, "content": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 371, "content": "// }"}, {"type": "INSERT", "lineNumber": 372, "content": ""}]}, {"timestamp": 1756969537490, "changes": [{"type": "DELETE", "lineNumber": 10, "oldContent": "class MyApp extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "INSERT", "lineNumber": 11, "content": "class MyApp extends StatelessWidget {"}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "INSERT", "lineNumber": 26, "content": "  }"}, {"type": "DELETE", "lineNumber": 28, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "        SafeArea("}, {"type": "DELETE", "lineNumber": 73, "oldContent": "            padding:"}, {"type": "INSERT", "lineNumber": 72, "content": "        SafeArea("}, {"type": "MODIFY", "lineNumber": 74, "content": "            padding: const EdgeInsets.symmetric(", "oldContent": "                const EdgeInsets.symmetric(horizontal: 24.0, vertical: 5.0),"}, {"type": "INSERT", "lineNumber": 75, "content": "              horizontal: 24.0,"}, {"type": "INSERT", "lineNumber": 76, "content": "            ),"}, {"type": "INSERT", "lineNumber": 80, "content": "              children: ["}, {"type": "DELETE", "lineNumber": 80, "oldContent": "                const Padding("}, {"type": "DELETE", "lineNumber": 82, "oldContent": "        Text("}, {"type": "DELETE", "lineNumber": 83, "oldContent": "      children: ["}, {"type": "DELETE", "lineNumber": 84, "oldContent": "      crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "DELETE", "lineNumber": 85, "oldContent": ""}, {"type": "INSERT", "lineNumber": 83, "content": "                  child: Text("}, {"type": "INSERT", "lineNumber": 84, "content": "                    'انشاء حساب',"}, {"type": "INSERT", "lineNumber": 85, "content": "                    style: TextStyle("}, {"type": "INSERT", "lineNumber": 86, "content": "                      color: Colors.white,"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "            fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 95, "oldContent": "          style: const TextStyle("}, {"type": "DELETE", "lineNumber": 96, "oldContent": "    const Color primaryGreen = Color(0xFF2DB45D);"}, {"type": "DELETE", "lineNumber": 97, "oldContent": "            fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 98, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 99, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 100, "oldContent": "//     return false;"}, {"type": "INSERT", "lineNumber": 95, "content": "                  width: 50,"}, {"type": "INSERT", "lineNumber": 96, "content": "                  height: 50,"}, {"type": "INSERT", "lineNumber": 97, "content": "                  errorBuilder: (context, error, stackTrace) {"}, {"type": "INSERT", "lineNumber": 98, "content": "                    return const Icon("}, {"type": "INSERT", "lineNumber": 99, "content": "                      Icons.account_balance, // A placeholder icon"}, {"type": "INSERT", "lineNumber": 100, "content": "                      color: Colors.white,"}, {"type": "INSERT", "lineNumber": 101, "content": "                      size: 50,"}, {"type": "INSERT", "lineNumber": 104, "content": "                ),"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "              ],"}, {"type": "INSERT", "lineNumber": 107, "content": "          ),"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 126, "oldContent": "          ),"}, {"type": "INSERT", "lineNumber": 128, "content": "          ),"}, {"type": "INSERT", "lineNumber": 137, "content": "                _passwordVisible = !_passwordVisible;"}, {"type": "DELETE", "lineNumber": 137, "oldContent": "              });"}, {"type": "DELETE", "lineNumber": 140, "oldContent": "            label: 'تأكيد كلمة المرور',"}, {"type": "INSERT", "lineNumber": 142, "content": "          _buildTextField("}, {"type": "INSERT", "lineNumber": 143, "content": "            label: 'تأكيد كلمة المرور',"}, {"type": "DELETE", "lineNumber": 144, "oldContent": "          _buildTextField("}, {"type": "INSERT", "lineNumber": 154, "content": "          _buildTextField("}, {"type": "DELETE", "lineNumber": 154, "oldContent": "              label: 'رقم الهوية',"}, {"type": "INSERT", "lineNumber": 174, "content": "  }"}, {"type": "DELETE", "lineNumber": 174, "oldContent": ""}, {"type": "DELETE", "lineNumber": 246, "oldContent": ""}, {"type": "DELETE", "lineNumber": 247, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 248, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 249, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 250, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 251, "oldContent": "// void main() async {"}, {"type": "DELETE", "lineNumber": 252, "oldContent": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "DELETE", "lineNumber": 253, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 254, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 255, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 256, "oldContent": "//     return false;"}, {"type": "INSERT", "lineNumber": 247, "content": "              });"}, {"type": "INSERT", "lineNumber": 248, "content": "            },"}, {"type": "INSERT", "lineNumber": 249, "content": "            activeColor: const Color(0xFF2DB45D),"}, {"type": "INSERT", "lineNumber": 250, "content": "            side: const BorderSide(color: Colors.black, width: 1.5),"}, {"type": "INSERT", "lineNumber": 251, "content": "            shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 252, "content": "              borderRadius: BorderRadius.circular(4),"}, {"type": "INSERT", "lineNumber": 253, "content": "            ),"}, {"type": "INSERT", "lineNumber": 254, "content": "          ),"}, {"type": "INSERT", "lineNumber": 255, "content": "        ),"}, {"type": "INSERT", "lineNumber": 256, "content": "        const SizedBox(width: 12),"}, {"type": "INSERT", "lineNumber": 257, "content": "        Expanded("}, {"type": "INSERT", "lineNumber": 284, "content": "    return SizedBox("}, {"type": "INSERT", "lineNumber": 285, "content": "      height: 50,"}, {"type": "INSERT", "lineNumber": 286, "content": "      child: El<PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 287, "content": "        onPressed: () {},"}, {"type": "INSERT", "lineNumber": 288, "content": "        style: ElevatedButton.styleFrom("}, {"type": "INSERT", "lineNumber": 289, "content": "          backgroundColor: const Color(0xFFC4C4C4),"}, {"type": "INSERT", "lineNumber": 290, "content": "          foregroundColor: Colors.black,"}, {"type": "INSERT", "lineNumber": 291, "content": "          shape: RoundedRectangleBorder("}, {"type": "INSERT", "lineNumber": 292, "content": "            borderRadius: BorderRadius.circular(12),"}, {"type": "INSERT", "lineNumber": 293, "content": "          ),"}, {"type": "DELETE", "lineNumber": 285, "oldContent": "    return SizedBox("}, {"type": "DELETE", "lineNumber": 288, "oldContent": "      height: 50,"}, {"type": "DELETE", "lineNumber": 290, "oldContent": "      child: El<PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 293, "oldContent": "        onPressed: () {},"}, {"type": "DELETE", "lineNumber": 296, "oldContent": "        style: ElevatedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 298, "oldContent": "          backgroundColor: const Color(0xFFC4C4C4),"}, {"type": "DELETE", "lineNumber": 301, "oldContent": "          foregroundColor: Colors.black,"}, {"type": "DELETE", "lineNumber": 304, "oldContent": "          shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 306, "oldContent": "            borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 309, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 315, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 316, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 317, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 318, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "DELETE", "lineNumber": 319, "oldContent": "//   await GetStorageService.init();"}, {"type": "DELETE", "lineNumber": 320, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 321, "oldContent": "// void main() async {"}, {"type": "DELETE", "lineNumber": 322, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 323, "oldContent": "// import 'package:dropx/src/app.dart';"}, {"type": "DELETE", "lineNumber": 324, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 325, "oldContent": "//"}, {"type": "INSERT", "lineNumber": 316, "content": "        child: Text("}, {"type": "INSERT", "lineNumber": 317, "content": "          'تسجيل دخول',"}, {"type": "INSERT", "lineNumber": 318, "content": "          style: TextStyle("}, {"type": "INSERT", "lineNumber": 319, "content": "            fontSize: 18,"}, {"type": "INSERT", "lineNumber": 320, "content": "            fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 321, "content": "            color: primary<PERSON>reen,"}, {"type": "INSERT", "lineNumber": 322, "content": "          ),"}, {"type": "INSERT", "lineNumber": 323, "content": "        ),"}, {"type": "INSERT", "lineNumber": 324, "content": "      ),"}, {"type": "INSERT", "lineNumber": 325, "content": "    );"}, {"type": "INSERT", "lineNumber": 326, "content": "  }"}, {"type": "DELETE", "lineNumber": 327, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 332, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 334, "oldContent": ""}, {"type": "DELETE", "lineNumber": 336, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 338, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 347, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 350, "oldContent": "// import 'dart:io';"}, {"type": "DELETE", "lineNumber": 352, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 354, "oldContent": "//     return false;"}, {"type": "DELETE", "lineNumber": 356, "oldContent": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 349, "content": "//   }"}, {"type": "INSERT", "lineNumber": 350, "content": "//"}, {"type": "INSERT", "lineNumber": 352, "content": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 353, "content": "//     return false;"}, {"type": "INSERT", "lineNumber": 355, "content": "// }"}, {"type": "INSERT", "lineNumber": 356, "content": "// import 'dart:io';"}, {"type": "INSERT", "lineNumber": 358, "content": "// import 'package:flutter/material.dart';"}, {"type": "INSERT", "lineNumber": 359, "content": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "INSERT", "lineNumber": 360, "content": "// import 'package:dropx/src/app.dart';"}, {"type": "INSERT", "lineNumber": 361, "content": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "INSERT", "lineNumber": 362, "content": "//"}, {"type": "INSERT", "lineNumber": 363, "content": "// void main() async {"}, {"type": "INSERT", "lineNumber": 364, "content": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "INSERT", "lineNumber": 365, "content": "//"}, {"type": "INSERT", "lineNumber": 366, "content": "//   await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 367, "content": "//"}, {"type": "INSERT", "lineNumber": 368, "content": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 369, "content": "//"}, {"type": "INSERT", "lineNumber": 370, "content": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 371, "content": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 372, "content": "// }"}, {"type": "INSERT", "lineNumber": 373, "content": ""}]}, {"timestamp": 1756969552390, "changes": [{"type": "DELETE", "lineNumber": 10, "oldContent": "class MyApp extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "INSERT", "lineNumber": 11, "content": "class MyApp extends StatelessWidget {"}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "MODIFY", "lineNumber": 72, "content": "        SafeArea(", "oldContent": "        SafeArea("}, {"type": "INSERT", "lineNumber": 73, "content": "          child: Padding("}, {"type": "DELETE", "lineNumber": 75, "oldContent": "                const EdgeInsets.symmetric(horizontal: 24.0, vertical: 5.0),"}, {"type": "INSERT", "lineNumber": 76, "content": "              vertical: 16.0,"}, {"type": "DELETE", "lineNumber": 80, "oldContent": "                const Padding("}, {"type": "INSERT", "lineNumber": 82, "content": "                const Padding("}, {"type": "DELETE", "lineNumber": 87, "oldContent": "//   @override"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 89, "oldContent": "//   }"}, {"type": "INSERT", "lineNumber": 88, "content": "                      fontSize: 32,"}, {"type": "INSERT", "lineNumber": 89, "content": "                      fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 90, "content": "                    ),"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "//     return path;"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "//     path.lineTo(size.width, 0);"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "//       size.height * 0.7,"}, {"type": "INSERT", "lineNumber": 92, "content": "                ),"}, {"type": "INSERT", "lineNumber": 93, "content": "                // Placeholder for the logo image"}, {"type": "INSERT", "lineNumber": 94, "content": "                Image.asset("}, {"type": "INSERT", "lineNumber": 95, "content": "                  'assets/images/logo_symbol.png',"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "              ],"}, {"type": "INSERT", "lineNumber": 106, "content": "              ],"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "        ),"}, {"type": "INSERT", "lineNumber": 109, "content": "        ),"}, {"type": "DELETE", "lineNumber": 137, "oldContent": "              });"}, {"type": "INSERT", "lineNumber": 139, "content": "              });"}, {"type": "DELETE", "lineNumber": 143, "oldContent": "            hint: 'تأكيد كلمة المرور',"}, {"type": "INSERT", "lineNumber": 145, "content": "            hint: 'تأكيد كلمة المرور',"}, {"type": "DELETE", "lineNumber": 154, "oldContent": "              label: 'رقم الهوية',"}, {"type": "INSERT", "lineNumber": 156, "content": "              label: 'رقم الهوية',"}, {"type": "DELETE", "lineNumber": 174, "oldContent": ""}, {"type": "INSERT", "lineNumber": 176, "content": ""}, {"type": "DELETE", "lineNumber": 239, "oldContent": ""}, {"type": "DELETE", "lineNumber": 240, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 241, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 242, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "DELETE", "lineNumber": 243, "oldContent": "//   await GetStorageService.init();"}, {"type": "DELETE", "lineNumber": 244, "oldContent": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "DELETE", "lineNumber": 245, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 246, "oldContent": "// import 'package:dropx/src/app.dart';"}, {"type": "INSERT", "lineNumber": 240, "content": "        SizedBox("}, {"type": "INSERT", "lineNumber": 241, "content": "          width: 24,"}, {"type": "INSERT", "lineNumber": 242, "content": "          height: 24,"}, {"type": "INSERT", "lineNumber": 243, "content": "          child: Checkbox("}, {"type": "INSERT", "lineNumber": 244, "content": "            value: _termsAccepted,"}, {"type": "INSERT", "lineNumber": 245, "content": "            onChanged: (bool? value) {"}, {"type": "INSERT", "lineNumber": 246, "content": "              setState(() {"}, {"type": "INSERT", "lineNumber": 247, "content": "                _termsAccepted = value ?? false;"}, {"type": "DELETE", "lineNumber": 284, "oldContent": "          elevation: 0,"}, {"type": "DELETE", "lineNumber": 286, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 289, "oldContent": "        child: const Text("}, {"type": "DELETE", "lineNumber": 291, "oldContent": "          'انشاء حساب',"}, {"type": "DELETE", "lineNumber": 294, "oldContent": "          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),"}, {"type": "DELETE", "lineNumber": 297, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 299, "oldContent": "      ),"}, {"type": "INSERT", "lineNumber": 295, "content": "          elevation: 0,"}, {"type": "INSERT", "lineNumber": 296, "content": "        ),"}, {"type": "INSERT", "lineNumber": 297, "content": "        child: const Text("}, {"type": "INSERT", "lineNumber": 298, "content": "          'انشاء حساب',"}, {"type": "INSERT", "lineNumber": 299, "content": "          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),"}, {"type": "INSERT", "lineNumber": 300, "content": "        ),"}, {"type": "INSERT", "lineNumber": 301, "content": "      ),"}, {"type": "DELETE", "lineNumber": 311, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "MODIFY", "lineNumber": 312, "content": "          side: BorderSide(color: primaryGreen, width: 1.5),", "oldContent": "//   await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 313, "content": "          shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 314, "oldContent": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "MODIFY", "lineNumber": 315, "content": "          ),", "oldContent": "// void main() async {"}, {"type": "INSERT", "lineNumber": 316, "content": "        ),"}, {"type": "DELETE", "lineNumber": 344, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 346, "oldContent": "//"}, {"type": "INSERT", "lineNumber": 347, "content": "//     path.lineTo(size.width, 0);"}, {"type": "INSERT", "lineNumber": 348, "content": "//     path.close();"}, {"type": "INSERT", "lineNumber": 349, "content": "//     return path;"}, {"type": "INSERT", "lineNumber": 350, "content": "//   }"}, {"type": "INSERT", "lineNumber": 351, "content": "//"}, {"type": "INSERT", "lineNumber": 352, "content": "//   @override"}, {"type": "DELETE", "lineNumber": 350, "oldContent": "//     path.lineTo(size.width, 0);"}, {"type": "INSERT", "lineNumber": 355, "content": "//   }"}, {"type": "DELETE", "lineNumber": 352, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 354, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 355, "oldContent": "// import 'package:flutter/material.dart';"}, {"type": "INSERT", "lineNumber": 359, "content": "// import 'package:flutter/material.dart';"}, {"type": "DELETE", "lineNumber": 358, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "DELETE", "lineNumber": 360, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 362, "oldContent": "//   await GetStorageService.init();"}, {"type": "DELETE", "lineNumber": 364, "oldContent": "//"}, {"type": "INSERT", "lineNumber": 366, "content": "//"}, {"type": "INSERT", "lineNumber": 367, "content": "//   await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 368, "content": "//"}, {"type": "INSERT", "lineNumber": 369, "content": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 370, "content": "//"}, {"type": "INSERT", "lineNumber": 371, "content": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 372, "content": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 373, "content": "// }"}, {"type": "INSERT", "lineNumber": 374, "content": ""}]}, {"timestamp": 1756969554638, "changes": [{"type": "DELETE", "lineNumber": 10, "oldContent": "class MyApp extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "INSERT", "lineNumber": 11, "content": "class MyApp extends StatelessWidget {"}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "INSERT", "lineNumber": 71, "content": "        ),"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "        SafeArea("}, {"type": "MODIFY", "lineNumber": 76, "content": "              vertical: 8.0,", "oldContent": "              vertical: 16.0,"}, {"type": "MODIFY", "lineNumber": 91, "content": "                  ),", "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 228, "content": "            ),"}, {"type": "INSERT", "lineNumber": 229, "content": "            contentPadding: const EdgeInsets.only(top: 15, bottom: 10),"}, {"type": "INSERT", "lineNumber": 230, "content": "          ),"}, {"type": "INSERT", "lineNumber": 231, "content": "        ),"}, {"type": "INSERT", "lineNumber": 232, "content": "      ],"}, {"type": "INSERT", "lineNumber": 233, "content": "    );"}, {"type": "INSERT", "lineNumber": 234, "content": "  }"}, {"type": "DELETE", "lineNumber": 229, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 230, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 231, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 232, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "DELETE", "lineNumber": 233, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 234, "oldContent": "//   await GetStorageService.init();"}, {"type": "DELETE", "lineNumber": 235, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 236, "oldContent": "// void main() async {"}, {"type": "DELETE", "lineNumber": 237, "oldContent": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "DELETE", "lineNumber": 238, "oldContent": "// import 'package:dropx/src/app.dart';"}, {"type": "DELETE", "lineNumber": 239, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "INSERT", "lineNumber": 236, "content": "  Widget _buildTermsAndConditions() {"}, {"type": "INSERT", "lineNumber": 237, "content": "    return Row("}, {"type": "INSERT", "lineNumber": 238, "content": "      crossAxisAlignment: CrossAxisAlignment.center,"}, {"type": "INSERT", "lineNumber": 239, "content": "      children: ["}, {"type": "DELETE", "lineNumber": 282, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 283, "oldContent": "//"}, {"type": "INSERT", "lineNumber": 282, "content": "  }"}, {"type": "INSERT", "lineNumber": 283, "content": ""}, {"type": "DELETE", "lineNumber": 291, "oldContent": "          elevation: 0,"}, {"type": "MODIFY", "lineNumber": 293, "content": "            borderRadius: BorderRadius.circular(12),", "oldContent": "        ),"}, {"type": "INSERT", "lineNumber": 294, "content": "          ),"}, {"type": "INSERT", "lineNumber": 295, "content": "          elevation: 0,"}, {"type": "INSERT", "lineNumber": 296, "content": "        ),"}, {"type": "DELETE", "lineNumber": 297, "oldContent": "            borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 300, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 344, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 345, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 344, "content": "//       size.width,"}, {"type": "INSERT", "lineNumber": 345, "content": "//       size.height * 0.7,"}, {"type": "INSERT", "lineNumber": 346, "content": "//     );"}, {"type": "DELETE", "lineNumber": 347, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 349, "oldContent": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "DELETE", "lineNumber": 351, "oldContent": "//     return false;"}, {"type": "DELETE", "lineNumber": 354, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 356, "oldContent": "// import 'dart:io';"}, {"type": "INSERT", "lineNumber": 353, "content": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 354, "content": "//     return false;"}, {"type": "INSERT", "lineNumber": 356, "content": "// }"}, {"type": "INSERT", "lineNumber": 357, "content": "// import 'dart:io';"}, {"type": "MODIFY", "lineNumber": 360, "content": "// import 'package:flutter_riverpod/flutter_riverpod.dart';", "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 362, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 363, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 364, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 362, "content": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "INSERT", "lineNumber": 364, "content": "// void main() async {"}, {"type": "INSERT", "lineNumber": 365, "content": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "INSERT", "lineNumber": 368, "content": "//"}, {"type": "INSERT", "lineNumber": 369, "content": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 370, "content": "//"}, {"type": "INSERT", "lineNumber": 371, "content": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 372, "content": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 373, "content": "// }"}, {"type": "INSERT", "lineNumber": 374, "content": ""}]}, {"timestamp": 1756969675248, "changes": [{"type": "DELETE", "lineNumber": 10, "oldContent": "class MyApp extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "INSERT", "lineNumber": 11, "content": "class MyApp extends StatelessWidget {"}, {"type": "INSERT", "lineNumber": 13, "content": ""}, {"type": "MODIFY", "lineNumber": 126, "content": "            label: 'رقم الهاتف',", "oldContent": "            label: 'رقم الهاتف:',"}, {"type": "DELETE", "lineNumber": 221, "oldContent": ""}, {"type": "DELETE", "lineNumber": 222, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 223, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "DELETE", "lineNumber": 224, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 225, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 226, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "DELETE", "lineNumber": 227, "oldContent": "//"}, {"type": "INSERT", "lineNumber": 221, "content": "                  )"}, {"type": "INSERT", "lineNumber": 222, "content": "                : null,"}, {"type": "INSERT", "lineNumber": 223, "content": "            enabledBorder: const UnderlineInputBorder("}, {"type": "INSERT", "lineNumber": 224, "content": "              borderSide: BorderSide(color: primaryGreen, width: 1.0),"}, {"type": "DELETE", "lineNumber": 229, "oldContent": "//   await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 226, "content": "            focusedBorder: const UnderlineInputBorder("}, {"type": "INSERT", "lineNumber": 227, "content": "              borderSide: BorderSide(color: primaryGreen, width: 2.0),"}, {"type": "INSERT", "lineNumber": 228, "content": "            ),"}, {"type": "INSERT", "lineNumber": 235, "content": ""}, {"type": "INSERT", "lineNumber": 292, "content": "          shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 294, "oldContent": "          shape: RoundedRectangleBorder("}, {"type": "MODIFY", "lineNumber": 297, "content": "        child: const Text(", "oldContent": "        child: const Text("}, {"type": "DELETE", "lineNumber": 342, "oldContent": ""}, {"type": "DELETE", "lineNumber": 343, "oldContent": "// }"}, {"type": "INSERT", "lineNumber": 342, "content": "//       size.width * 0.85,"}, {"type": "INSERT", "lineNumber": 343, "content": "//       size.height * 0.65,"}, {"type": "DELETE", "lineNumber": 351, "oldContent": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "MODIFY", "lineNumber": 353, "content": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {", "oldContent": "//     return false;"}, {"type": "INSERT", "lineNumber": 354, "content": "//     return false;"}, {"type": "INSERT", "lineNumber": 355, "content": "//   }"}, {"type": "DELETE", "lineNumber": 357, "oldContent": "//   }"}, {"type": "MODIFY", "lineNumber": 358, "content": "//", "oldContent": "// import 'package:dropx/src/app.dart';"}, {"type": "INSERT", "lineNumber": 359, "content": "// import 'package:flutter/material.dart';"}, {"type": "DELETE", "lineNumber": 360, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "MODIFY", "lineNumber": 361, "content": "// import 'package:dropx/src/app.dart';", "oldContent": "// }"}, {"type": "INSERT", "lineNumber": 363, "content": "//"}, {"type": "DELETE", "lineNumber": 366, "oldContent": ""}, {"type": "MODIFY", "lineNumber": 367, "content": "//   await GetStorageService.init();", "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 369, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "MODIFY", "lineNumber": 371, "content": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);", "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 372, "content": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 373, "content": "// }"}, {"type": "INSERT", "lineNumber": 374, "content": ""}]}, {"timestamp": 1756970914590, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "import 'package:flutter/gestures.dart';"}, {"type": "DELETE", "lineNumber": 1, "oldContent": "import 'package:flutter/material.dart';"}, {"type": "DELETE", "lineNumber": 2, "oldContent": "import 'package:flutter/services.dart';"}, {"type": "DELETE", "lineNumber": 3, "oldContent": "import 'package:google_fonts/google_fonts.dart';"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "import 'generated/assets.gen.dart';"}, {"type": "DELETE", "lineNumber": 7, "oldContent": "void main() {"}, {"type": "DELETE", "lineNumber": 8, "oldContent": "  runApp(const MyApp());"}, {"type": "DELETE", "lineNumber": 9, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 10, "oldContent": "class MyApp extends StatelessWidget {"}, {"type": "DELETE", "lineNumber": 11, "oldContent": ""}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "DELETE", "lineNumber": 13, "oldContent": "  const MyApp({super.key});"}, {"type": "DELETE", "lineNumber": 14, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "  Widget build(BuildContext context) {"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "    return MaterialApp("}, {"type": "DELETE", "lineNumber": 17, "oldContent": "      title: 'Flutter Sign Up UI',"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "      theme: Theme<PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 19, "oldContent": "        primarySwatch: Colors.green,"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "        textTheme: GoogleFonts.cairoTextTheme(Theme.of(context).textTheme),"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "        scaffoldBackgroundColor: Colors.white,"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "      debugShowCheckedModeBanner: false,"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "      home: const SignUpScreen(),"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 28, "oldContent": ""}, {"type": "DELETE", "lineNumber": 29, "oldContent": "class SignUpScreen extends StatefulWidget {"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "  const SignUpScreen({super.key});"}, {"type": "DELETE", "lineNumber": 31, "oldContent": ""}, {"type": "DELETE", "lineNumber": 32, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "  State<SignUpScreen> createState() => _SignUpScreenState();"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 35, "oldContent": ""}, {"type": "DELETE", "lineNumber": 36, "oldContent": "class _SignUpScreenState extends State<SignUpScreen> {"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "  bool _passwordVisible = false;"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "  bool _confirmPasswordVisible = false;"}, {"type": "DELETE", "lineNumber": 39, "oldContent": "  bool _termsAccepted = false;"}, {"type": "DELETE", "lineNumber": 40, "oldContent": ""}, {"type": "DELETE", "lineNumber": 41, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "  Widget build(BuildContext context) {"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "    return AnnotatedRegion<SystemUiOverlayStyle>("}, {"type": "DELETE", "lineNumber": 44, "oldContent": "      value: SystemUiOverlayStyle.light.copyWith("}, {"type": "DELETE", "lineNumber": 45, "oldContent": "        statusBarColor: const Color(0xFF2DB45D),"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "        systemNavigationBarColor: Colors.white,"}, {"type": "DELETE", "lineNumber": 47, "oldContent": "        systemNavigationBarIconBrightness: Brightness.dark,"}, {"type": "DELETE", "lineNumber": 48, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 49, "oldContent": "      child: Directionality("}, {"type": "DELETE", "lineNumber": 50, "oldContent": "        textDirection: TextDirection.rtl,"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "        child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 52, "oldContent": "          body: SingleChildScrollView("}, {"type": "DELETE", "lineNumber": 53, "oldContent": "            child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 54, "oldContent": "              children: ["}, {"type": "DELETE", "lineNumber": 55, "oldContent": "                _buildHeader(context),"}, {"type": "DELETE", "lineNumber": 56, "oldContent": "                _buildForm(context),"}, {"type": "DELETE", "lineNumber": 57, "oldContent": "              ],"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 62, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 63, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 64, "oldContent": ""}, {"type": "DELETE", "lineNumber": 65, "oldContent": "  Widget _buildHeader(BuildContext context) {"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "    return Stack("}, {"type": "DELETE", "lineNumber": 67, "oldContent": "      children: ["}, {"type": "DELETE", "lineNumber": 68, "oldContent": "        Assets.images.topClipper.image("}, {"type": "DELETE", "lineNumber": 69, "oldContent": "          width: double.infinity,"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "          fit: BoxFit.cover,"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "        SafeArea("}, {"type": "DELETE", "lineNumber": 73, "oldContent": "          child: Padding("}, {"type": "DELETE", "lineNumber": 74, "oldContent": "            padding: const EdgeInsets.symmetric("}, {"type": "DELETE", "lineNumber": 75, "oldContent": "              horizontal: 24.0,"}, {"type": "DELETE", "lineNumber": 76, "oldContent": "              vertical: 8.0,"}, {"type": "DELETE", "lineNumber": 77, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 78, "oldContent": "            child: Row("}, {"type": "DELETE", "lineNumber": 79, "oldContent": "              mainAxisAlignment: MainAxisAlignment.spaceBetween,"}, {"type": "DELETE", "lineNumber": 80, "oldContent": "              crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "DELETE", "lineNumber": 81, "oldContent": "              children: ["}, {"type": "DELETE", "lineNumber": 82, "oldContent": "                const Padding("}, {"type": "DELETE", "lineNumber": 83, "oldContent": "                  padding: EdgeInsets.only(top: 10.0),"}, {"type": "DELETE", "lineNumber": 84, "oldContent": "                  child: Text("}, {"type": "DELETE", "lineNumber": 85, "oldContent": "                    'انشاء حساب',"}, {"type": "DELETE", "lineNumber": 86, "oldContent": "                    style: TextStyle("}, {"type": "DELETE", "lineNumber": 87, "oldContent": "                      color: Colors.white,"}, {"type": "DELETE", "lineNumber": 88, "oldContent": "                      fontSize: 32,"}, {"type": "DELETE", "lineNumber": 89, "oldContent": "                      fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 90, "oldContent": "                    ),"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "                  ),"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 93, "oldContent": "                // Placeholder for the logo image"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "                Image.asset("}, {"type": "DELETE", "lineNumber": 95, "oldContent": "                  'assets/images/logo_symbol.png',"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "                  width: 50,"}, {"type": "DELETE", "lineNumber": 97, "oldContent": "                  height: 50,"}, {"type": "DELETE", "lineNumber": 98, "oldContent": "                  errorBuilder: (context, error, stackTrace) {"}, {"type": "DELETE", "lineNumber": 99, "oldContent": "                    return const Icon("}, {"type": "DELETE", "lineNumber": 100, "oldContent": "                      Icons.account_balance, // A placeholder icon"}, {"type": "DELETE", "lineNumber": 101, "oldContent": "                      color: Colors.white,"}, {"type": "DELETE", "lineNumber": 102, "oldContent": "                      size: 50,"}, {"type": "DELETE", "lineNumber": 103, "oldContent": "                    );"}, {"type": "DELETE", "lineNumber": 104, "oldContent": "                  },"}, {"type": "DELETE", "lineNumber": 105, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 106, "oldContent": "              ],"}, {"type": "DELETE", "lineNumber": 107, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 108, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 109, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 110, "oldContent": "      ],"}, {"type": "DELETE", "lineNumber": 111, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 112, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 113, "oldContent": ""}, {"type": "DELETE", "lineNumber": 114, "oldContent": "  Widget _buildForm(BuildContext context) {"}, {"type": "DELETE", "lineNumber": 115, "oldContent": "    const Color primaryGreen = Color(0xFF2DB45D);"}, {"type": "DELETE", "lineNumber": 116, "oldContent": ""}, {"type": "DELETE", "lineNumber": 117, "oldContent": "    return Padding("}, {"type": "DELETE", "lineNumber": 118, "oldContent": "      padding: const EdgeInsets.symmetric(horizontal: 32.0),"}, {"type": "DELETE", "lineNumber": 119, "oldContent": "      child: <PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 120, "oldContent": "        crossAxisAlignment: CrossAxisAlignment.stretch,"}, {"type": "DELETE", "lineNumber": 121, "oldContent": "        children: ["}, {"type": "DELETE", "lineNumber": 122, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 123, "oldContent": "          _buildTextField(label: 'الاسم', hint: 'الاسم'),"}, {"type": "DELETE", "lineNumber": 124, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 125, "oldContent": "          _buildTextField("}, {"type": "DELETE", "lineNumber": 126, "oldContent": "            label: 'رقم الهاتف',"}, {"type": "DELETE", "lineNumber": 127, "oldContent": "            hint: '05xxxxxxxx',"}, {"type": "DELETE", "lineNumber": 128, "oldContent": "            keyboardType: TextInputType.phone,"}, {"type": "DELETE", "lineNumber": 129, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 130, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 131, "oldContent": "          _buildTextField("}, {"type": "DELETE", "lineNumber": 132, "oldContent": "            label: 'كلمة المرور',"}, {"type": "DELETE", "lineNumber": 133, "oldContent": "            hint: 'كلمة المرور',"}, {"type": "DELETE", "lineNumber": 134, "oldContent": "            isPassword: true,"}, {"type": "DELETE", "lineNumber": 135, "oldContent": "            obscureText: !_passwordVisible,"}, {"type": "DELETE", "lineNumber": 136, "oldContent": "            onToggleVisibility: () {"}, {"type": "DELETE", "lineNumber": 137, "oldContent": "              setState(() {"}, {"type": "DELETE", "lineNumber": 138, "oldContent": "                _passwordVisible = !_passwordVisible;"}, {"type": "DELETE", "lineNumber": 139, "oldContent": "              });"}, {"type": "DELETE", "lineNumber": 140, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 141, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 142, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 143, "oldContent": "          _buildTextField("}, {"type": "DELETE", "lineNumber": 144, "oldContent": "            label: 'تأكيد كلمة المرور',"}, {"type": "DELETE", "lineNumber": 145, "oldContent": "            hint: 'تأكيد كلمة المرور',"}, {"type": "DELETE", "lineNumber": 146, "oldContent": "            isPassword: true,"}, {"type": "DELETE", "lineNumber": 147, "oldContent": "            obscureText: !_confirmPasswordVisible,"}, {"type": "DELETE", "lineNumber": 148, "oldContent": "            onToggleVisibility: () {"}, {"type": "DELETE", "lineNumber": 149, "oldContent": "              setState(() {"}, {"type": "DELETE", "lineNumber": 150, "oldContent": "                _confirmPasswordVisible = !_confirmPasswordVisible;"}, {"type": "DELETE", "lineNumber": 151, "oldContent": "              });"}, {"type": "DELETE", "lineNumber": 152, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 153, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 154, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 155, "oldContent": "          _buildTextField("}, {"type": "DELETE", "lineNumber": 156, "oldContent": "              label: 'رقم الهوية',"}, {"type": "DELETE", "lineNumber": 157, "oldContent": "              hint: 'رقم الهوية',"}, {"type": "DELETE", "lineNumber": 158, "oldContent": "              keyboardType: TextInputType.number),"}, {"type": "DELETE", "lineNumber": 159, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 160, "oldContent": "          _buildTermsAndConditions(),"}, {"type": "DELETE", "lineNumber": 161, "oldContent": "          const SizedBox(height: 30),"}, {"type": "DELETE", "lineNumber": 162, "oldContent": "          _buildSignUpButton(),"}, {"type": "DELETE", "lineNumber": 163, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 164, "oldContent": "          const Text("}, {"type": "DELETE", "lineNumber": 165, "oldContent": "            'تملك حساب؟',"}, {"type": "DELETE", "lineNumber": 166, "oldContent": "            textAlign: TextAlign.center,"}, {"type": "DELETE", "lineNumber": 167, "oldContent": "            style: TextStyle(color: Colors.black54, fontSize: 14),"}, {"type": "DELETE", "lineNumber": 168, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 169, "oldContent": "          const SizedBox(height: 10),"}, {"type": "DELETE", "lineNumber": 170, "oldContent": "          _build<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(primaryGreen),"}, {"type": "DELETE", "lineNumber": 171, "oldContent": "          const SizedBox(height: 20),"}, {"type": "DELETE", "lineNumber": 172, "oldContent": "        ],"}, {"type": "DELETE", "lineNumber": 173, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 174, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 175, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 176, "oldContent": ""}, {"type": "DELETE", "lineNumber": 177, "oldContent": "  Widget _buildTextField({"}, {"type": "DELETE", "lineNumber": 178, "oldContent": "    required String label,"}, {"type": "DELETE", "lineNumber": 179, "oldContent": "    required String hint,"}, {"type": "DELETE", "lineNumber": 180, "oldContent": "    Widget? prefix,"}, {"type": "DELETE", "lineNumber": 181, "oldContent": "    bool isPassword = false,"}, {"type": "DELETE", "lineNumber": 182, "oldContent": "    bool obscureText = false,"}, {"type": "DELETE", "lineNumber": 183, "oldContent": "    VoidCallback? onToggleVisibility,"}, {"type": "DELETE", "lineNumber": 184, "oldContent": "    TextInputType? keyboardType,"}, {"type": "DELETE", "lineNumber": 185, "oldContent": "  }) {"}, {"type": "DELETE", "lineNumber": 186, "oldContent": "    const Color primaryGreen = Color(0xFF2DB45D);"}, {"type": "DELETE", "lineNumber": 187, "oldContent": "    return Column("}, {"type": "DELETE", "lineNumber": 188, "oldContent": "      crossAxisAlignment: CrossAxisAlignment.start,"}, {"type": "DELETE", "lineNumber": 189, "oldContent": "      children: ["}, {"type": "DELETE", "lineNumber": 190, "oldContent": "        Text("}, {"type": "DELETE", "lineNumber": 191, "oldContent": "          label,"}, {"type": "DELETE", "lineNumber": 192, "oldContent": "          style: const TextStyle("}, {"type": "DELETE", "lineNumber": 193, "oldContent": "            fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 194, "oldContent": "            fontSize: 16,"}, {"type": "DELETE", "lineNumber": 195, "oldContent": "            color: Colors.black,"}, {"type": "DELETE", "lineNumber": 196, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 197, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 198, "oldContent": "        TextField("}, {"type": "DELETE", "lineNumber": 199, "oldContent": "          obscureText: obscureText,"}, {"type": "DELETE", "lineNumber": 200, "oldContent": "          keyboardType: keyboardType,"}, {"type": "DELETE", "lineNumber": 201, "oldContent": "          style: const TextStyle(fontSize: 16, color: Colors.black87),"}, {"type": "DELETE", "lineNumber": 202, "oldContent": "          decoration: InputDecoration("}, {"type": "DELETE", "lineNumber": 203, "oldContent": "            hintText: hint,"}, {"type": "DELETE", "lineNumber": 204, "oldContent": "            hintStyle: const TextStyle(color: Colors.grey, fontSize: 14),"}, {"type": "DELETE", "lineNumber": 205, "oldContent": "            prefixIcon: prefix != null"}, {"type": "DELETE", "lineNumber": 206, "oldContent": "                ? Padding("}, {"type": "DELETE", "lineNumber": 207, "oldContent": "                    padding: const EdgeInsets.only(top: 12.0, left: 8.0),"}, {"type": "DELETE", "lineNumber": 208, "oldContent": "                    child: prefix)"}, {"type": "DELETE", "lineNumber": 209, "oldContent": "                : null,"}, {"type": "DELETE", "lineNumber": 210, "oldContent": "            prefixIconConstraints:"}, {"type": "DELETE", "lineNumber": 211, "oldContent": "                const BoxConstraints(minWidth: 0, minHeight: 0),"}, {"type": "DELETE", "lineNumber": 212, "oldContent": "            suffixIcon: isPassword"}, {"type": "DELETE", "lineNumber": 213, "oldContent": "                ? IconButton("}, {"type": "DELETE", "lineNumber": 214, "oldContent": "                    icon: Icon("}, {"type": "DELETE", "lineNumber": 215, "oldContent": "                      obscureText"}, {"type": "DELETE", "lineNumber": 216, "oldContent": "                          ? Icons.visibility_off_outlined"}, {"type": "DELETE", "lineNumber": 217, "oldContent": "                          : Icons.visibility_outlined,"}, {"type": "DELETE", "lineNumber": 218, "oldContent": ""}, {"type": "DELETE", "lineNumber": 219, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 220, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 221, "oldContent": "                  )"}, {"type": "DELETE", "lineNumber": 222, "oldContent": "                : null,"}, {"type": "DELETE", "lineNumber": 223, "oldContent": "            enabledBorder: const UnderlineInputBorder("}, {"type": "DELETE", "lineNumber": 224, "oldContent": "              borderSide: BorderSide(color: primaryGreen, width: 1.0),"}, {"type": "DELETE", "lineNumber": 225, "oldContent": "            focusedBorder: const UnderlineInputBorder("}, {"type": "DELETE", "lineNumber": 226, "oldContent": "              borderSide: BorderSide(color: primaryGreen, width: 2.0),"}, {"type": "DELETE", "lineNumber": 227, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 228, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 229, "oldContent": "            contentPadding: const EdgeInsets.only(top: 15, bottom: 10),"}, {"type": "DELETE", "lineNumber": 230, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 231, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 232, "oldContent": "      ],"}, {"type": "DELETE", "lineNumber": 233, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 234, "oldContent": ""}, {"type": "DELETE", "lineNumber": 235, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 236, "oldContent": "  Widget _buildTermsAndConditions() {"}, {"type": "DELETE", "lineNumber": 237, "oldContent": "    return Row("}, {"type": "DELETE", "lineNumber": 238, "oldContent": "      crossAxisAlignment: CrossAxisAlignment.center,"}, {"type": "DELETE", "lineNumber": 239, "oldContent": "      children: ["}, {"type": "DELETE", "lineNumber": 240, "oldContent": "        SizedBox("}, {"type": "DELETE", "lineNumber": 241, "oldContent": "          width: 24,"}, {"type": "DELETE", "lineNumber": 242, "oldContent": "          height: 24,"}, {"type": "DELETE", "lineNumber": 243, "oldContent": "          child: Checkbox("}, {"type": "DELETE", "lineNumber": 244, "oldContent": "            value: _termsAccepted,"}, {"type": "DELETE", "lineNumber": 245, "oldContent": "            onChanged: (bool? value) {"}, {"type": "DELETE", "lineNumber": 246, "oldContent": "              setState(() {"}, {"type": "DELETE", "lineNumber": 247, "oldContent": "                _termsAccepted = value ?? false;"}, {"type": "DELETE", "lineNumber": 248, "oldContent": "              });"}, {"type": "DELETE", "lineNumber": 249, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 250, "oldContent": "            activeColor: const Color(0xFF2DB45D),"}, {"type": "DELETE", "lineNumber": 251, "oldContent": "            side: const BorderSide(color: Colors.black, width: 1.5),"}, {"type": "DELETE", "lineNumber": 252, "oldContent": "            shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 253, "oldContent": "              borderRadius: BorderRadius.circular(4),"}, {"type": "DELETE", "lineNumber": 254, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 255, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 256, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 257, "oldContent": "        const SizedBox(width: 12),"}, {"type": "DELETE", "lineNumber": 258, "oldContent": "        Expanded("}, {"type": "DELETE", "lineNumber": 259, "oldContent": "          child: <PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 260, "oldContent": "            text: TextSpan("}, {"type": "DELETE", "lineNumber": 261, "oldContent": "              style: GoogleFonts.cairo(fontSize: 13, color: Colors.black87),"}, {"type": "DELETE", "lineNumber": 262, "oldContent": "              children: ["}, {"type": "DELETE", "lineNumber": 263, "oldContent": "                const TextSpan(text: 'أقر بأنني قد قرأت وفهمت وأوافق على '),"}, {"type": "DELETE", "lineNumber": 264, "oldContent": "                TextSpan("}, {"type": "DELETE", "lineNumber": 265, "oldContent": "                  text: 'الشروط والأحكام',"}, {"type": "DELETE", "lineNumber": 266, "oldContent": "                  style: const TextStyle("}, {"type": "DELETE", "lineNumber": 267, "oldContent": "                    color: Color(0xFF3366CC),"}, {"type": "DELETE", "lineNumber": 268, "oldContent": "                    decoration: TextDecoration.underline,"}, {"type": "DELETE", "lineNumber": 269, "oldContent": "                  ),"}, {"type": "DELETE", "lineNumber": 270, "oldContent": "                  recognizer: TapGestureRecognizer()"}, {"type": "DELETE", "lineNumber": 271, "oldContent": "                    ..onTap = () {"}, {"type": "DELETE", "lineNumber": 272, "oldContent": "                      // Handle terms and conditions tap"}, {"type": "DELETE", "lineNumber": 273, "oldContent": "                    },"}, {"type": "DELETE", "lineNumber": 274, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 275, "oldContent": "                const TextSpan(text: ' الخاصة باستخدام هذا التطبيق'),"}, {"type": "DELETE", "lineNumber": 276, "oldContent": "              ],"}, {"type": "DELETE", "lineNumber": 277, "oldContent": "            ),"}, {"type": "DELETE", "lineNumber": 278, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 279, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 280, "oldContent": "      ],"}, {"type": "DELETE", "lineNumber": 281, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 282, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 283, "oldContent": ""}, {"type": "DELETE", "lineNumber": 284, "oldContent": "  Widget _buildSignUpButton() {"}, {"type": "DELETE", "lineNumber": 285, "oldContent": "    return SizedBox("}, {"type": "DELETE", "lineNumber": 286, "oldContent": "      height: 50,"}, {"type": "DELETE", "lineNumber": 287, "oldContent": "      child: El<PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 288, "oldContent": "        onPressed: () {},"}, {"type": "DELETE", "lineNumber": 289, "oldContent": "        style: ElevatedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 290, "oldContent": "          backgroundColor: const Color(0xFFC4C4C4),"}, {"type": "DELETE", "lineNumber": 291, "oldContent": "          foregroundColor: Colors.black,"}, {"type": "DELETE", "lineNumber": 292, "oldContent": "          shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 293, "oldContent": "            borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 294, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 295, "oldContent": "          elevation: 0,"}, {"type": "DELETE", "lineNumber": 296, "oldContent": "        child: const Text("}, {"type": "DELETE", "lineNumber": 297, "oldContent": "        child: const Text("}, {"type": "DELETE", "lineNumber": 298, "oldContent": "          'انشاء حساب',"}, {"type": "DELETE", "lineNumber": 299, "oldContent": "          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),"}, {"type": "DELETE", "lineNumber": 300, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 301, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 302, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 303, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 304, "oldContent": ""}, {"type": "DELETE", "lineNumber": 305, "oldContent": "  Widget _buildLoginButton(Color primaryGreen) {"}, {"type": "DELETE", "lineNumber": 306, "oldContent": "    return SizedBox("}, {"type": "DELETE", "lineNumber": 307, "oldContent": "      height: 50,"}, {"type": "DELETE", "lineNumber": 308, "oldContent": "      child: Out<PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 309, "oldContent": "        onPressed: () {},"}, {"type": "DELETE", "lineNumber": 310, "oldContent": "        style: OutlinedButton.styleFrom("}, {"type": "DELETE", "lineNumber": 311, "oldContent": "          foregroundColor: primaryGreen,"}, {"type": "DELETE", "lineNumber": 312, "oldContent": "          side: BorderSide(color: primaryGreen, width: 1.5),"}, {"type": "DELETE", "lineNumber": 313, "oldContent": "          shape: RoundedRectangleBorder("}, {"type": "DELETE", "lineNumber": 314, "oldContent": "            borderRadius: BorderRadius.circular(12),"}, {"type": "DELETE", "lineNumber": 315, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 316, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 317, "oldContent": "        child: Text("}, {"type": "DELETE", "lineNumber": 318, "oldContent": "          'تسجيل دخول',"}, {"type": "DELETE", "lineNumber": 319, "oldContent": "          style: TextStyle("}, {"type": "DELETE", "lineNumber": 320, "oldContent": "            fontSize: 18,"}, {"type": "DELETE", "lineNumber": 321, "oldContent": "            fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 322, "oldContent": "            color: primary<PERSON>reen,"}, {"type": "DELETE", "lineNumber": 323, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 324, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 325, "oldContent": "      ),"}, {"type": "DELETE", "lineNumber": 326, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 327, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 328, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 329, "oldContent": ""}, {"type": "DELETE", "lineNumber": 341, "oldContent": ""}, {"type": "INSERT", "lineNumber": 13, "content": "//     path.quadraticBezierTo("}, {"type": "INSERT", "lineNumber": 24, "content": "//   @override"}, {"type": "DELETE", "lineNumber": 354, "oldContent": "//   @override"}, {"type": "DELETE", "lineNumber": 360, "oldContent": ""}, {"type": "INSERT", "lineNumber": 32, "content": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 366, "oldContent": ""}, {"type": "INSERT", "lineNumber": 38, "content": "//"}, {"type": "INSERT", "lineNumber": 42, "content": "//"}, {"type": "DELETE", "lineNumber": 372, "oldContent": ""}, {"type": "INSERT", "lineNumber": 46, "content": ""}]}, {"timestamp": 1756971252863, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": ""}, {"type": "DELETE", "lineNumber": 1, "oldContent": ""}, {"type": "DELETE", "lineNumber": 2, "oldContent": "//     path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 3, "oldContent": "//   @override"}, {"type": "DELETE", "lineNumber": 4, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 6, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 7, "oldContent": ""}, {"type": "INSERT", "lineNumber": 11, "content": "//     path.quadraticBezierTo("}, {"type": "INSERT", "lineNumber": 22, "content": "//   @override"}, {"type": "INSERT", "lineNumber": 30, "content": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "INSERT", "lineNumber": 36, "content": "//"}, {"type": "INSERT", "lineNumber": 40, "content": "//"}, {"type": "INSERT", "lineNumber": 44, "content": ""}]}, {"timestamp": 1756975710176, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "// class HeaderClipper extends CustomClipper<Path> {"}, {"type": "DELETE", "lineNumber": 1, "oldContent": "//   @override"}, {"type": "DELETE", "lineNumber": 2, "oldContent": "//   Path getClip(Size size) {"}, {"type": "DELETE", "lineNumber": 3, "oldContent": "//     path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 4, "oldContent": "//     final path = Path();"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "//     path.lineTo(0, size.height * 0.75);"}, {"type": "DELETE", "lineNumber": 6, "oldContent": "//     path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 7, "oldContent": "//       size.width * 0.2,"}, {"type": "DELETE", "lineNumber": 8, "oldContent": "//       size.height,"}, {"type": "DELETE", "lineNumber": 9, "oldContent": "//       size.width * 0.5,"}, {"type": "DELETE", "lineNumber": 11, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 12, "oldContent": "//       size.width * 0.85,"}, {"type": "DELETE", "lineNumber": 13, "oldContent": "//       size.height * 0.65,"}, {"type": "DELETE", "lineNumber": 14, "oldContent": "//       size.width,"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "//   @override"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "//       size.height * 0.7,"}, {"type": "DELETE", "lineNumber": 17, "oldContent": "//     );"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "//     path.lineTo(size.width, 0);"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "//     path.close();"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "//     return path;"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 23, "oldContent": "//   bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "// import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "//     return false;"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "//   }"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "// }"}, {"type": "DELETE", "lineNumber": 28, "oldContent": "// import 'dart:io';"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "// import 'package:flutter/material.dart';"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "// import 'package:dropx/src/app.dart';"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "// import 'package:xr_helper/xr_helper.dart';"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "// void main() async {"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "//   WidgetsFlutterBinding.ensureInitialized();"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "//   await GetStorageService.init();"}, {"type": "DELETE", "lineNumber": 39, "oldContent": "//"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "//   HttpOverrides.global = MyHttpOverrides();"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "//   // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "//   runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 2, "content": "class HeaderClipper extends CustomClipper<Path> {"}, {"type": "INSERT", "lineNumber": 3, "content": "  @override"}, {"type": "INSERT", "lineNumber": 4, "content": "  Path getClip(Size size) {"}, {"type": "INSERT", "lineNumber": 5, "content": "    final path = Path();"}, {"type": "INSERT", "lineNumber": 6, "content": "    path.lineTo(0, size.height * 0.75);"}, {"type": "INSERT", "lineNumber": 7, "content": "    path.quadraticBezierTo("}, {"type": "INSERT", "lineNumber": 8, "content": "      size.width * 0.2,"}, {"type": "INSERT", "lineNumber": 9, "content": "      size.height,"}, {"type": "INSERT", "lineNumber": 10, "content": "      size.width * 0.5,"}, {"type": "INSERT", "lineNumber": 11, "content": "      size.height * 0.85,"}, {"type": "INSERT", "lineNumber": 12, "content": "    );"}, {"type": "INSERT", "lineNumber": 13, "content": "    path.quadraticBezierTo("}, {"type": "INSERT", "lineNumber": 14, "content": "      size.width * 0.85,"}, {"type": "INSERT", "lineNumber": 15, "content": "      size.height * 0.65,"}, {"type": "INSERT", "lineNumber": 16, "content": "      size.width,"}, {"type": "INSERT", "lineNumber": 17, "content": "      size.height * 0.7,"}, {"type": "INSERT", "lineNumber": 18, "content": "    );"}, {"type": "INSERT", "lineNumber": 19, "content": "    path.lineTo(size.width, 0);"}, {"type": "INSERT", "lineNumber": 20, "content": "    path.close();"}, {"type": "INSERT", "lineNumber": 21, "content": "    return path;"}, {"type": "INSERT", "lineNumber": 22, "content": "  }"}, {"type": "INSERT", "lineNumber": 23, "content": ""}, {"type": "INSERT", "lineNumber": 24, "content": "  @override"}, {"type": "INSERT", "lineNumber": 25, "content": "  bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "INSERT", "lineNumber": 26, "content": "    return false;"}, {"type": "INSERT", "lineNumber": 27, "content": "  }"}, {"type": "INSERT", "lineNumber": 28, "content": "}"}, {"type": "INSERT", "lineNumber": 29, "content": "import 'dart:io';"}, {"type": "INSERT", "lineNumber": 30, "content": ""}, {"type": "INSERT", "lineNumber": 31, "content": "import 'package:flutter/material.dart';"}, {"type": "INSERT", "lineNumber": 32, "content": "import 'package:flutter_riverpod/flutter_riverpod.dart';"}, {"type": "INSERT", "lineNumber": 33, "content": "import 'package:dropx/src/app.dart';"}, {"type": "INSERT", "lineNumber": 34, "content": "import 'package:xr_helper/xr_helper.dart';"}, {"type": "INSERT", "lineNumber": 35, "content": ""}, {"type": "INSERT", "lineNumber": 36, "content": "void main() async {"}, {"type": "INSERT", "lineNumber": 37, "content": "  WidgetsFlutterBinding.ensureInitialized();"}, {"type": "INSERT", "lineNumber": 38, "content": ""}, {"type": "INSERT", "lineNumber": 39, "content": "  await GetStorageService.init();"}, {"type": "INSERT", "lineNumber": 40, "content": ""}, {"type": "INSERT", "lineNumber": 41, "content": "  HttpOverrides.global = MyHttpOverrides();"}, {"type": "INSERT", "lineNumber": 42, "content": ""}, {"type": "INSERT", "lineNumber": 43, "content": "  // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);"}, {"type": "INSERT", "lineNumber": 44, "content": "  runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 45, "content": "}"}, {"type": "INSERT", "lineNumber": 46, "content": ""}]}, {"timestamp": 1756975716396, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "class HeaderClipper extends CustomClipper<Path> {"}, {"type": "DELETE", "lineNumber": 1, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 2, "oldContent": "  Path getClip(Size size) {"}, {"type": "DELETE", "lineNumber": 3, "oldContent": "    final path = Path();"}, {"type": "DELETE", "lineNumber": 4, "oldContent": "    path.lineTo(0, size.height * 0.75);"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "    path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 6, "oldContent": "      size.width * 0.2,"}, {"type": "DELETE", "lineNumber": 7, "oldContent": "      size.height,"}, {"type": "DELETE", "lineNumber": 8, "oldContent": "      size.width * 0.5,"}, {"type": "DELETE", "lineNumber": 10, "oldContent": "      size.height * 0.85,"}, {"type": "DELETE", "lineNumber": 11, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 12, "oldContent": "    path.quadraticBezierTo("}, {"type": "DELETE", "lineNumber": 13, "oldContent": "      size.width * 0.85,"}, {"type": "DELETE", "lineNumber": 14, "oldContent": "      size.height * 0.65,"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "      size.width,"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "      size.height * 0.7,"}, {"type": "DELETE", "lineNumber": 17, "oldContent": "    );"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "    path.lineTo(size.width, 0);"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "    path.close();"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "    return path;"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 22, "oldContent": ""}, {"type": "DELETE", "lineNumber": 23, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 24, "oldContent": "  bool shouldReclip(CustomClipper<Path> oldClipper) {"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "    return false;"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "}"}, {"type": "DELETE", "lineNumber": 42, "oldContent": ""}, {"type": "DELETE", "lineNumber": 45, "oldContent": ""}, {"type": "INSERT", "lineNumber": 18, "content": ""}]}, {"timestamp": 1756975815193, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "// void main() async {"}, {"type": "DELETE", "lineNumber": 1, "oldContent": ""}, {"type": "INSERT", "lineNumber": 15, "content": "  runApp(const ProviderScope(child: BaseApp()));"}, {"type": "INSERT", "lineNumber": 16, "content": "}"}, {"type": "INSERT", "lineNumber": 17, "content": ""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/register/register.screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/auth/view/register/register.screen.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_form_builder/flutter_form_builder.dart';\nimport 'package:flutter_hooks/flutter_hooks.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:hooks_riverpod/hooks_riverpod.dart';\nimport 'package:dropx/generated/assets.gen.dart';\nimport 'package:dropx/src/core/shared/extensions/context_extensions.dart';\nimport 'package:dropx/src/core/theme/color_manager.dart';\nimport 'package:dropx/src/screens/main_screen/view/main.screen.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport 'widgets/register_fields_container.widget.dart';\n\nclass RegisterScreen extends HookConsumerWidget {\n  const RegisterScreen({\n    super.key,\n  });\n\n  @override\n  Widget build(BuildContext context, WidgetRef ref) {\n    final formKey = useMemoized(() => GlobalKey<FormBuilderState>());\n\n    return Scaffold(\n      appBar: AppBar(\n        backgroundColor: Colors.transparent,\n        surfaceTintColor: Colors.transparent,\n        actions: [\n          TextButton(\n            onPressed: () {\n              const MainScreen().navigate;\n            },\n            child: Row(\n              children: [\n                Text(\n                  context.tr.skip,\n                  style: AppTextStyles.title,\n                ),\n                AppGaps.gap8,\n                const CircleAvatar(\n                  radius: 18,\n                  backgroundColor: ColorManager.primaryColor,\n                  child: Icon(\n                    Icons.arrow_forward_ios,\n                    color: ColorManager.white,\n                  ),\n                ),\n              ],\n            ),\n          ),\n        ],\n      ),\n      body: FormBuilder(\n        key: formKey,\n        child: Padding(\n          padding: const EdgeInsets.all(AppSpaces.padding12),\n          child: ListView(\n            children: [\n              ClipRRect(\n                borderRadius: BorderRadius.circular(AppRadius.radius28),\n                child:\n                    Assets.images.logoSymbol.image(fit: BoxFit.cover, width: 220.w),\n              ).center(),\n              Text(\n                context.tr.registerWithYourAccountNow,\n                style: AppTextStyles.title.copyWith(\n                  fontWeight: FontWeight.bold,\n                ),\n              ).center(),\n              AppGaps.gap24,\n\n              // * Fields Container\n              RegisterFieldsContainer(\n                formKey: formKey,\n              )\n            ],\n          ),\n        ),\n      ),\n    );\n  }\n}\n", "baseTimestamp": *************, "deltas": [{"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 59, "oldContent": "                child:"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "                    Assets.images.logoSymbol.image(fit: BoxFit.cover, width: 220.w),"}, {"type": "INSERT", "lineNumber": 59, "content": "                child: Assets.images.logoSymbol"}, {"type": "INSERT", "lineNumber": 60, "content": "                    .image(fit: BoxFit.cover, width: 220.w),"}]}, {"timestamp": *************, "changes": [{"type": "DELETE", "lineNumber": 23, "oldContent": "      appBar: AppBar("}, {"type": "DELETE", "lineNumber": 24, "oldContent": "        backgroundColor: Colors.transparent,"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "        surfaceTintColor: Colors.transparent,"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "        actions: ["}, {"type": "DELETE", "lineNumber": 27, "oldContent": "          TextButton("}, {"type": "DELETE", "lineNumber": 28, "oldContent": "            onPressed: () {"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "              const MainScreen().navigate;"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "            },"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "            child: Row("}, {"type": "DELETE", "lineNumber": 32, "oldContent": "              children: ["}, {"type": "DELETE", "lineNumber": 33, "oldContent": "                Text("}, {"type": "DELETE", "lineNumber": 34, "oldContent": "                  context.tr.skip,"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "                  style: AppTextStyles.title,"}, {"type": "INSERT", "lineNumber": 23, "content": "      body: Column("}, {"type": "INSERT", "lineNumber": 24, "content": "        children: ["}, {"type": "INSERT", "lineNumber": 25, "content": "          // <PERSON>"}, {"type": "INSERT", "lineNumber": 26, "content": "          Container("}, {"type": "INSERT", "lineNumber": 27, "content": "            width: double.infinity,"}, {"type": "INSERT", "lineNumber": 28, "content": "            decoration: const BoxDecoration("}, {"type": "INSERT", "lineNumber": 29, "content": "              color: ColorManager.primaryColor,"}, {"type": "INSERT", "lineNumber": 30, "content": "              borderRadius: BorderRadius.only("}, {"type": "INSERT", "lineNumber": 31, "content": "                bottomLeft: Ra<PERSON>.circular(30),"}, {"type": "INSERT", "lineNumber": 32, "content": "                bottomRight: Radius.circular(30),"}, {"type": "INSERT", "lineNumber": 33, "content": "              ),"}, {"type": "INSERT", "lineNumber": 34, "content": "            ),"}, {"type": "INSERT", "lineNumber": 35, "content": "            child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 36, "content": "              child: Padding("}, {"type": "INSERT", "lineNumber": 37, "content": "                padding: const EdgeInsets.all(AppSpaces.padding24),"}, {"type": "INSERT", "lineNumber": 38, "content": "                child: <PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 39, "content": "                  children: ["}, {"type": "INSERT", "lineNumber": 40, "content": "                    // Logo"}, {"type": "INSERT", "lineNumber": 41, "content": "                    Assets.images.logoSymbol.image("}, {"type": "INSERT", "lineNumber": 42, "content": "                      width: 60.w,"}, {"type": "INSERT", "lineNumber": 43, "content": "                      height: 60.h,"}, {"type": "INSERT", "lineNumber": 44, "content": "                      color: ColorManager.white,"}, {"type": "INSERT", "lineNumber": 45, "content": "                    ),"}, {"type": "INSERT", "lineNumber": 46, "content": "                    AppGaps.gap16,"}, {"type": "INSERT", "lineNumber": 47, "content": "                    // Title"}, {"type": "INSERT", "lineNumber": 48, "content": "                    Text("}, {"type": "INSERT", "lineNumber": 49, "content": "                      context.tr.createAccount,"}, {"type": "INSERT", "lineNumber": 50, "content": "                      style: AppTextStyles.title.copyWith("}, {"type": "INSERT", "lineNumber": 51, "content": "                        color: ColorManager.white,"}, {"type": "INSERT", "lineNumber": 52, "content": "                        fontWeight: FontWeight.bold,"}, {"type": "INSERT", "lineNumber": 53, "content": "                        fontSize: 24.sp,"}, {"type": "INSERT", "lineNumber": 54, "content": "                      ),"}, {"type": "INSERT", "lineNumber": 55, "content": "                    ),"}, {"type": "INSERT", "lineNumber": 56, "content": "                  ],"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "                AppGaps.gap8,"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "                const CircleAvatar("}, {"type": "DELETE", "lineNumber": 39, "oldContent": "                  radius: 18,"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "                  backgroundColor: ColorManager.primaryColor,"}, {"type": "DELETE", "lineNumber": 41, "oldContent": "                  child: <PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 42, "oldContent": "                    Icons.arrow_forward_ios,"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "                    color: ColorManager.white,"}, {"type": "DELETE", "lineNumber": 44, "oldContent": "                  ),"}, {"type": "INSERT", "lineNumber": 58, "content": "              ),"}, {"type": "INSERT", "lineNumber": 59, "content": "            ),"}, {"type": "INSERT", "lineNumber": 60, "content": "          ),"}, {"type": "INSERT", "lineNumber": 61, "content": ""}, {"type": "INSERT", "lineNumber": 62, "content": "          // Form Content"}, {"type": "INSERT", "lineNumber": 63, "content": "          Expanded("}, {"type": "INSERT", "lineNumber": 64, "content": "            child: <PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 65, "content": "              key: <PERSON><PERSON><PERSON>,"}, {"type": "INSERT", "lineNumber": 66, "content": "              child: Padding("}, {"type": "INSERT", "lineNumber": 67, "content": "                padding: const EdgeInsets.all(AppSpaces.padding16),"}, {"type": "INSERT", "lineNumber": 68, "content": "                child: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("}, {"type": "INSERT", "lineNumber": 69, "content": "                  formKey: formKey,"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "              ],"}, {"type": "INSERT", "lineNumber": 71, "content": "              ),"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "      body: FormBuilder("}, {"type": "DELETE", "lineNumber": 52, "oldContent": "        key: <PERSON><PERSON><PERSON>,"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "        child: Padding("}, {"type": "DELETE", "lineNumber": 54, "oldContent": "          padding: const EdgeInsets.all(AppSpaces.padding12),"}, {"type": "DELETE", "lineNumber": 55, "oldContent": "          child: <PERSON><PERSON><PERSON><PERSON>("}, {"type": "DELETE", "lineNumber": 56, "oldContent": "            children: ["}, {"type": "DELETE", "lineNumber": 57, "oldContent": "              ClipRRect("}, {"type": "DELETE", "lineNumber": 58, "oldContent": "                borderRadius: BorderRadius.circular(AppRadius.radius28),"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "                child: Assets.images.logoSymbol"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "                    .image(fit: BoxFit.cover, width: 220.w),"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "              ).center(),"}, {"type": "DELETE", "lineNumber": 62, "oldContent": "              Text("}, {"type": "DELETE", "lineNumber": 63, "oldContent": "                context.tr.registerWithYourAccountNow,"}, {"type": "DELETE", "lineNumber": 64, "oldContent": "                style: AppTextStyles.title.copyWith("}, {"type": "DELETE", "lineNumber": 65, "oldContent": "                  fontWeight: FontWeight.bold,"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "                ),"}, {"type": "DELETE", "lineNumber": 67, "oldContent": "              ).center(),"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "              AppGaps.gap24,"}, {"type": "DELETE", "lineNumber": 69, "oldContent": ""}, {"type": "DELETE", "lineNumber": 70, "oldContent": "              // * Fields Container"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "              RegisterFieldsContainer("}, {"type": "DELETE", "lineNumber": 72, "oldContent": "                formKey: formKey,"}, {"type": "DELETE", "lineNumber": 73, "oldContent": "              )"}, {"type": "DELETE", "lineNumber": 74, "oldContent": "            ],"}, {"type": "DELETE", "lineNumber": 75, "oldContent": "          ),"}, {"type": "DELETE", "lineNumber": 76, "oldContent": "        ),"}, {"type": "DELETE", "lineNumber": 77, "oldContent": "      ),"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/gradle/wrapper/gradle-wrapper.properties": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/gradle/wrapper/gradle-wrapper.properties", "baseContent": "distributionBase=GRADLE_USER_HOME\ndistributionPath=wrapper/dists\nzipStoreBase=GRADLE_USER_HOME\nzipStorePath=wrapper/dists\ndistributionUrl=https\\://services.gradle.org/distributions/gradle-8.10.2-all.zip\n", "baseTimestamp": 1756967820315}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/app/build.gradle": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/android/app/build.gradle", "baseContent": "plugins {\n    id \"com.android.application\"\n    id \"kotlin-android\"\n    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.\n    id \"dev.flutter.flutter-gradle-plugin\"\n}\n\nandroid {\n    namespace = \"com.ajory.dropx\"\n    compileSdk = flutter.compileSdkVersion\n    ndkVersion = flutter.ndkVersion\n\n    compileOptions {\n        sourceCompatibility = JavaVersion.VERSION_1_8\n        targetCompatibility = JavaVersion.VERSION_1_8\n    }\n\n    kotlinOptions {\n        jvmTarget = JavaVersion.VERSION_1_8\n    }\n\n    defaultConfig {\n        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).\n        applicationId = \"com.ajory.dropx\"\n        // You can update the following values to match your application needs.\n        // For more information, see: https://flutter.dev/to/review-gradle-config.\n        minSdk = 23\n        targetSdk = flutter.targetSdkVersion\n        versionCode = flutter.versionCode\n        versionName = flutter.versionName\n    }\n\n    buildTypes {\n        release {\n            // TODO: Add your own signing config for the release build.\n            // Signing with the debug keys for now, so `flutter run --release` works.\n            signingConfig = signingConfigs.debug\n        }\n    }\n}\n\nflutter {\n    source = \"../..\"\n}\n", "baseTimestamp": 1756967830121, "deltas": [{"timestamp": 1756967833204, "changes": [{"type": "MODIFY", "lineNumber": 27, "content": "        targetSdk = 36", "oldContent": "        targetSdk = flutter.targetSdkVersion"}]}, {"timestamp": 1756967835944, "changes": [{"type": "MODIFY", "lineNumber": 9, "content": "    compileSdk = 36", "oldContent": "    compileSdk = flutter.compileSdkVersion"}]}, {"timestamp": 1756967936981, "changes": [{"type": "MODIFY", "lineNumber": 10, "content": "    ndkVersion \"28.0.13004108\"", "oldContent": "    ndkVersion = flutter.ndkVersion"}, {"type": "INSERT", "lineNumber": 12, "content": ""}]}, {"timestamp": 1756967940665, "changes": [{"type": "MODIFY", "lineNumber": 10, "content": "    ndkVersion = \"28.0.13004108\"", "oldContent": "    ndkVersion \"28.0.13004108\""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/ios/Podfile": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/ios/Podfile", "baseContent": "# Uncomment this line to define a global platform for your project\nplatform :ios, '13.0'x\n\n# CocoaPods analytics sends network stats synchronously affecting flutter build latency.\nENV['COCOAPODS_DISABLE_STATS'] = 'true'\n\nproject 'Runner', {\n  'Debug' => :debug,\n  'Profile' => :release,\n  'Release' => :release,\n}\n\ndef flutter_root\n  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)\n  unless File.exist?(generated_xcode_build_settings_path)\n    raise \"#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first\"\n  end\n\n  File.foreach(generated_xcode_build_settings_path) do |line|\n    matches = line.match(/FLUTTER_ROOT\\=(.*)/)\n    return matches[1].strip if matches\n  end\n  raise \"FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get\"\nend\n\nrequire File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)\n\nflutter_ios_podfile_setup\n\ntarget 'Runner' do\n  use_frameworks!\n  use_modular_headers!\n\n  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))\n  target 'RunnerTests' do\n    inherit! :search_paths\n  end\nend\n\npost_install do |installer|\n  installer.pods_project.targets.each do |target|\n    flutter_additional_ios_build_settings(target)\n  end\nend\n", "baseTimestamp": 1756968053055, "deltas": [{"timestamp": 1756968055555, "changes": [{"type": "MODIFY", "lineNumber": 1, "content": "platform :ios, '13.0'", "oldContent": "platform :ios, '13.0'x"}]}, {"timestamp": 1756968058001, "changes": [{"type": "MODIFY", "lineNumber": 1, "content": "platform :ios, '15.0'", "oldContent": "platform :ios, '13.0'"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/shared/services/app_settings/controller/settings_controller.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/shared/services/app_settings/controller/settings_controller.dart", "baseContent": "import 'dart:async';\n\nimport 'package:flutter/material.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport '../repository/local_settings_repo.dart';\n\nfinal settingsControllerProvider =\n    ChangeNotifierProvider<AppSettingsController>((ref) {\n  final settingsLocalRepo = ref.watch(settingsRepoProvider);\n  return AppSettingsController(settingsLocalRepo: settingsLocalRepo)\n    ..loadSettings();\n});\n\nclass AppSettingsController extends BaseVM {\n  final SettingsLocalRepo settingsLocalRepo;\n\n  AppSettingsController({required this.settingsLocalRepo});\n\n  //! Load Settings ===================================\n  Future<void> loadSettings() async {\n    _locale = await settingsLocalRepo.locale();\n\n    notifyListeners();\n  }\n\n  Locale _locale = const Locale('ar', 'EG');\n\n  Locale get locale => _locale;\n\n  bool get isEnglish => _locale.languageCode == 'en';\n\n  //! Update Language  ===================================\n  Future<void> updateLanguage(Locale newLocale) async {\n    if (_locale == newLocale) return;\n    _locale = newLocale;\n    await settingsLocalRepo.updateLanguage(newLocale);\n    notifyListeners();\n  }\n}\n", "baseTimestamp": 1756968365516}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/shared/services/app_settings/repository/local_settings_repo.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/shared/services/app_settings/repository/local_settings_repo.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:hooks_riverpod/hooks_riverpod.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nfinal settingsRepoProvider = Provider<SettingsLocalRepo>((ref) {\n  return SettingsLocalRepo();\n});\n\nclass SettingsLocalRepo {\n  Future<void> updateLanguage(Locale locale) async {\n    GetStorageService.setData(\n        key: LocalKeys.language, value: locale.languageCode);\n  }\n\n  Future<Locale> locale() async {\n    final langCode = await GetStorageService.getData(key: LocalKeys.language);\n\n    if (langCode != null) {\n      return Locale(langCode);\n    } else {\n      return const Locale('ar', 'EG');\n    }\n  }\n}\n", "baseTimestamp": 1756968372623}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/app.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/app.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:google_fonts/google_fonts.dart';\nimport 'package:hooks_riverpod/hooks_riverpod.dart';\nimport 'package:dropx/src/screens/main_screen/view/main.screen.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nimport 'core/consts/app_constants.dart';\nimport 'core/shared/services/app_settings/controller/settings_controller.dart';\nimport 'core/shared/widgets/loading/loading_widget.dart';\nimport 'core/theme/theme_manager.dart';\n\nclass BaseApp extends HookConsumerWidget {\n  const BaseApp({super.key});\n\n  @override\n  Widget build(BuildContext context, WidgetRef ref) {\n    final settingsController = ref.watch(settingsControllerProvider);\n\n    ScreenUtil.init(context);\n\n    final theme = AppTheme(\n        appTextTheme: settingsController.isEnglish\n            ? GoogleFonts.workSansTextTheme()\n            : GoogleFonts.cairoTextTheme());\n\n    return BaseMaterialApp(\n      title: AppConsts.appName,\n      //? Localization\n      locale: settingsController.locale,\n      supportedLocales: AppConsts.supportedLocales,\n      localizationsDelegates: AppConsts.localizationsDelegates,\n      //? Theme\n      theme: theme.appTheme(),\n      loadingWidget: const LoadingWidget(),\n      home: \n      // const MainScreen(),\n      const SplashScreen(),\n      // const SplashScreen()\n      // const MainScreen(),\n    );\n  }\n}\n", "baseTimestamp": 1756968401774, "deltas": [{"timestamp": 1756968405309, "changes": [{"type": "INSERT", "lineNumber": 0, "content": "import 'package:dropx/src/screens/splash/view/splash_screen.dart';"}]}, {"timestamp": 1756968427944, "changes": [{"type": "DELETE", "lineNumber": 36, "oldContent": "      home: "}, {"type": "DELETE", "lineNumber": 37, "oldContent": "      // const MainScreen(),"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "      const S<PERSON>lashScreen(),"}, {"type": "INSERT", "lineNumber": 36, "content": "      home:"}, {"type": "INSERT", "lineNumber": 37, "content": "          // const MainScreen(),"}, {"type": "INSERT", "lineNumber": 38, "content": "          const S<PERSON>lashScreen(),"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/splash/view/splash_screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/screens/splash/view/splash_screen.dart", "baseContent": "import 'package:flutter/material.dart';\nimport 'package:flutter_hooks/flutter_hooks.dart';\nimport 'package:dropx/generated/assets.gen.dart';\nimport 'package:dropx/src/screens/on_boarding/view/on_boarding.screen.dart';\nimport 'package:xr_helper/xr_helper.dart';\n\nclass SplashScreen extends HookWidget {\n  const SplashScreen({super.key});\n\n  @override\n  Widget build(BuildContext context) {\n    final loggedIn = GetStorageService.hasData(key: LocalKeys.user);\n\n    useEffect(() {\n      const navigateWidget =\n      // OnBoardingScreen();\n      // const LoginScreen();\n      // loggedIn ? const MainScreen() : const LoginScreen();\n\n      Future.delayed(const Duration(seconds: 6), () {\n        navigateWidget.navigate;\n      });\n\n      return () {};\n    }, []);\n\n    return Scaffold(\n      body: Center(\n        child: Assets.images.logo.image(fit: BoxFit.cover),\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1756968413710, "deltas": [{"timestamp": 1756968421665, "changes": [{"type": "MODIFY", "lineNumber": 14, "content": "      const navigateWidget =RegisterScreen();", "oldContent": "      const navigateWidget ="}]}, {"timestamp": 1756968427949, "changes": [{"type": "INSERT", "lineNumber": 6, "content": "import '../../auth/view/register/register.screen.dart';"}, {"type": "INSERT", "lineNumber": 7, "content": ""}, {"type": "DELETE", "lineNumber": 14, "oldContent": "      const navigateWidget =RegisterScreen();"}, {"type": "INSERT", "lineNumber": 16, "content": "      const navigateWidget = RegisterScreen();"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/theme/color_manager.dart": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/dropx/lib/src/core/theme/color_manager.dart", "baseContent": "import 'package:flutter/material.dart';\n\nclass ColorManager {\n  static const primaryColor = Color(0xFF29b65d);\n  static final lightPrimaryColor = const Color(0xFFf5a926).withOpacity(0.6);\n\n  static const secondaryColor = Color(0xFFECF0FF);\n\n  static const buttonColor = secondaryColor;\n  static final containerColor = Colors.grey.withOpacity(0.1);\n  static const selectedContainerColor = Color(0xFFD3E1E2);\n  static const fieldColor = Color(0xFFCBD5E1);\n  static const white = Color(0xFFFFFFFF);\n  static const black = Color(0xFF000000);\n  static const grey = Color(0xFFf5f5f5);\n  static const greyIcon = Color(0xFF9E9E9E);\n  static const highlightColor = Color(0xFFFFFFFF);\n  static const lightGrey = Color(0xFFEEF1F6);\n\n  static const shimmerBaseColor = Color(0xFFCECECE);\n  static const cardColor = Color(0xFFEDEDED);\n  static const darkGrey = Color(0xFFA4A4A4);\n  static const darkBlue = Color(0xFF23292F);\n  static const iconColor = Color(0xFF727272);\n  static const errorColor = Color(0xFFE74C3C);\n  static const successColor = Color(0xFF2ECC71);\n}\n", "baseTimestamp": 1756975831329, "deltas": [{"timestamp": 1756975833291, "changes": [{"type": "MODIFY", "lineNumber": 4, "content": "  static final lightPrimaryColor = const Color(0xFF29b65d).withOpacity(0.6);", "oldContent": "  static final lightPrimaryColor = const Color(0xFFf5a926).withOpacity(0.6);"}]}]}}}