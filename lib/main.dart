import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dropx/src/app.dart';
import 'package:xr_helper/xr_helper.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await GetStorageService.init();

  HttpOverrides.global = MyHttpOverrides();

  // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  runApp(const ProviderScope(child: BaseApp()));
}
