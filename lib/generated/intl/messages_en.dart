// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  static String m0(name) => "مرحباً، ${name}";

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "about": MessageLookupByLibrary.simpleMessage("حول"),
    "address": MessageLookupByLibrary.simpleMessage("العنوان"),
    "animals": MessageLookupByLibrary.simpleMessage("الحيوانات"),
    "changeLocation": MessageLookupByLibrary.simpleMessage("تغيير الموقع"),
    "changeSocial": MessageLookupByLibrary.simpleMessage("تغيير وسائل التواصل"),
    "close": MessageLookupByLibrary.simpleMessage("إغلاق"),
    "confirmPassword": MessageLookupByLibrary.simpleMessage(
      "تأكيد كلمة المرور",
    ),
    "createAccount": MessageLookupByLibrary.simpleMessage("إنشاء حساب"),
    "description": MessageLookupByLibrary.simpleMessage("الوصف"),
    "doctorBackground": MessageLookupByLibrary.simpleMessage("خلفية الطبيب"),
    "doctorLogo": MessageLookupByLibrary.simpleMessage("شعار الطبيب"),
    "doctorName": MessageLookupByLibrary.simpleMessage("اسم الطبيب"),
    "doctors": MessageLookupByLibrary.simpleMessage("الأطباء"),
    "dontHaveAccount": MessageLookupByLibrary.simpleMessage("لا تملك حساب؟"),
    "dontHaveAnAccount": MessageLookupByLibrary.simpleMessage("ليس لديك حساب؟"),
    "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
    "emailOptional": MessageLookupByLibrary.simpleMessage(
      "البريد الإلكتروني (اختياري)",
    ),
    "enter": MessageLookupByLibrary.simpleMessage("أدخل"),
    "enterVerificationCode": MessageLookupByLibrary.simpleMessage(
      "أدخل رمز التحقق",
    ),
    "error": MessageLookupByLibrary.simpleMessage("خطأ"),
    "forgotPassword": MessageLookupByLibrary.simpleMessage("نسيت كلمة المرور؟"),
    "forgotPasswordLink": MessageLookupByLibrary.simpleMessage(
      "نسيت كلمة المرور؟",
    ),
    "fullName": MessageLookupByLibrary.simpleMessage("الاسم الكامل"),
    "haveAnAccount": MessageLookupByLibrary.simpleMessage("لديك حساب؟"),
    "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
    "idNumber": MessageLookupByLibrary.simpleMessage("رقم الهوية"),
    "invalidIdNumber": MessageLookupByLibrary.simpleMessage(
      "رقم الهوية غير صحيح",
    ),
    "invalidPhoneNumber": MessageLookupByLibrary.simpleMessage(
      "رقم الهاتف غير صحيح",
    ),
    "invalidVerificationCode": MessageLookupByLibrary.simpleMessage(
      "رمز التحقق غير صحيح",
    ),
    "locationPickedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم اختيار الموقع بنجاح",
    ),
    "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "loginSuccessful": MessageLookupByLibrary.simpleMessage(
      "تم تسجيل الدخول بنجاح",
    ),
    "loginTitle": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "loginWithYourAccountNow": MessageLookupByLibrary.simpleMessage(
      "سجل دخولك بحسابك الآن!",
    ),
    "mobileNumber": MessageLookupByLibrary.simpleMessage("رقم الهاتف المحمول"),
    "next": MessageLookupByLibrary.simpleMessage("التالي"),
    "noDataFound": MessageLookupByLibrary.simpleMessage(
      "لم يتم العثور على بيانات",
    ),
    "onBoardingDescription1": MessageLookupByLibrary.simpleMessage(
      "اكتشف مجموعة واسعة من الحيوانات الأليفة للتبني واصطحب الرفيق المثالي إلى المنزل.",
    ),
    "onBoardingDescription2": MessageLookupByLibrary.simpleMessage(
      "تصفح متاجر الحيوانات الأليفة الموثوقة واعثر على أفضل المنتجات لأصدقائك ذوي الفراء.",
    ),
    "onBoardingDescription3": MessageLookupByLibrary.simpleMessage(
      "تواصل مع الأطباء البيطريين المحترفين وتأكد من حصول حيواناتك الأليفة على أفضل رعاية.",
    ),
    "onBoardingTitle1": MessageLookupByLibrary.simpleMessage(
      "اعثر على حيوانك الأليف المثالي",
    ),
    "onBoardingTitle2": MessageLookupByLibrary.simpleMessage(
      "استكشف أفضل متاجر الحيوانات الأليفة",
    ),
    "onBoardingTitle3": MessageLookupByLibrary.simpleMessage(
      "اعتن بصحة حيوانك الأليف",
    ),
    "password": MessageLookupByLibrary.simpleMessage("كلمة المرور"),
    "passwordsDoNotMatch": MessageLookupByLibrary.simpleMessage(
      "كلمات المرور غير متطابقة",
    ),
    "phoneHint": MessageLookupByLibrary.simpleMessage("05xxxxxxxx"),
    "pickImage": MessageLookupByLibrary.simpleMessage("اختيار صورة"),
    "pickLocation": MessageLookupByLibrary.simpleMessage("اختيار الموقع"),
    "pleaseAcceptTerms": MessageLookupByLibrary.simpleMessage(
      "يرجى الموافقة على الشروط والأحكام",
    ),
    "pleaseAddValidLink": MessageLookupByLibrary.simpleMessage(
      "يرجى إضافة رابط صحيح",
    ),
    "pleaseAddYourLocation": MessageLookupByLibrary.simpleMessage(
      "يرجى إضافة موقعك",
    ),
    "pleaseAddYourSocialMedia": MessageLookupByLibrary.simpleMessage(
      "يرجى إضافة وسائل التواصل الاجتماعي الخاصة بك",
    ),
    "products": MessageLookupByLibrary.simpleMessage("المنتجات"),
    "reels": MessageLookupByLibrary.simpleMessage("الريلز"),
    "register": MessageLookupByLibrary.simpleMessage("التسجيل"),
    "registerAsDoctor": MessageLookupByLibrary.simpleMessage("التسجيل كطبيب؟"),
    "registerAsStore": MessageLookupByLibrary.simpleMessage("التسجيل كمتجر؟"),
    "registerWithYourAccountNow": MessageLookupByLibrary.simpleMessage(
      "سجل حسابك الآن!",
    ),
    "registrationSuccessful": MessageLookupByLibrary.simpleMessage(
      "تم التسجيل بنجاح",
    ),
    "rememberMe": MessageLookupByLibrary.simpleMessage("تذكرني"),
    "resendCode": MessageLookupByLibrary.simpleMessage("إعادة إرسال الرمز"),
    "resetPassword": MessageLookupByLibrary.simpleMessage(
      "إعادة تعيين كلمة المرور",
    ),
    "save": MessageLookupByLibrary.simpleMessage("حفظ"),
    "search": MessageLookupByLibrary.simpleMessage("البحث"),
    "searchForDoctors": MessageLookupByLibrary.simpleMessage(
      "البحث عن الأطباء",
    ),
    "searchForProducts": MessageLookupByLibrary.simpleMessage(
      "البحث عن المنتجات",
    ),
    "searchForStores": MessageLookupByLibrary.simpleMessage("البحث عن المتاجر"),
    "seeAll": MessageLookupByLibrary.simpleMessage("عرض الكل"),
    "shops": MessageLookupByLibrary.simpleMessage("المتاجر"),
    "skip": MessageLookupByLibrary.simpleMessage("تخطي"),
    "socialMedia": MessageLookupByLibrary.simpleMessage(
      "وسائل التواصل الاجتماعي",
    ),
    "startNow": MessageLookupByLibrary.simpleMessage("ابدأ الآن"),
    "storeBackground": MessageLookupByLibrary.simpleMessage("خلفية المتجر"),
    "storeLogo": MessageLookupByLibrary.simpleMessage("شعار المتجر"),
    "storeName": MessageLookupByLibrary.simpleMessage("اسم المتجر"),
    "stores": MessageLookupByLibrary.simpleMessage("المتاجر"),
    "submit": MessageLookupByLibrary.simpleMessage("إرسال"),
    "tapToSelectLocation": MessageLookupByLibrary.simpleMessage(
      "اضغط لاختيار الموقع",
    ),
    "termsAndConditions": MessageLookupByLibrary.simpleMessage(
      "أقر بأنني قد قرأت ووافقت على الشروط والأحكام الخاصة باستخدام هذا التطبيق",
    ),
    "termsAndConditionsEnd": MessageLookupByLibrary.simpleMessage(
      "الخاصة باستخدام هذا التطبيق",
    ),
    "termsAndConditionsLink": MessageLookupByLibrary.simpleMessage(
      "الشروط والأحكام",
    ),
    "termsAndConditionsText": MessageLookupByLibrary.simpleMessage(
      "أقر بأنني قد قرأت وفهمت وأوافق على",
    ),
    "termsDialogContent": MessageLookupByLibrary.simpleMessage(
      "هذه هي الشروط والأحكام الخاصة بالتطبيق. يرجى قراءتها بعناية قبل الموافقة عليها. سيتم إضافة المحتوى الفعلي للشروط والأحكام هنا لاحقاً.",
    ),
    "termsDialogTitle": MessageLookupByLibrary.simpleMessage("الشروط والأحكام"),
    "verificationCodeSent": MessageLookupByLibrary.simpleMessage(
      "تم إرسال رمز التحقق بنجاح",
    ),
    "verificationMessage": MessageLookupByLibrary.simpleMessage(
      "تم إرسال رمز التحقق إلى رقم هاتفك",
    ),
    "verificationTitle": MessageLookupByLibrary.simpleMessage(
      "التحقق من الهاتف",
    ),
    "verify": MessageLookupByLibrary.simpleMessage("تحقق"),
    "welcomeWithName": m0,
    "youCanAlsoRegisterAsDoctor": MessageLookupByLibrary.simpleMessage(
      "يمكنك أيضاً التسجيل كطبيب من ملفك الشخصي.",
    ),
    "youCanAlsoRegisterAsStore": MessageLookupByLibrary.simpleMessage(
      "يمكنك أيضاً التسجيل كمتجر من ملفك الشخصي.",
    ),
  };
}
