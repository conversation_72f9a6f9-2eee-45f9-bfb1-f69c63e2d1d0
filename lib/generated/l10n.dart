// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `تسجيل الدخول`
  String get login {
    return Intl.message('تسجيل الدخول', name: 'login', desc: '', args: []);
  }

  /// `التسجيل`
  String get register {
    return Intl.message('التسجيل', name: 'register', desc: '', args: []);
  }

  /// `البريد الإلكتروني`
  String get email {
    return Intl.message('البريد الإلكتروني', name: 'email', desc: '', args: []);
  }

  /// `ليس لديك حساب؟`
  String get dontHaveAnAccount {
    return Intl.message(
      'ليس لديك حساب؟',
      name: 'dontHaveAnAccount',
      desc: '',
      args: [],
    );
  }

  /// `لديك حساب؟`
  String get haveAnAccount {
    return Intl.message(
      'لديك حساب؟',
      name: 'haveAnAccount',
      desc: '',
      args: [],
    );
  }

  /// `كلمة المرور`
  String get password {
    return Intl.message('كلمة المرور', name: 'password', desc: '', args: []);
  }

  /// `تأكيد كلمة المرور`
  String get confirmPassword {
    return Intl.message(
      'تأكيد كلمة المرور',
      name: 'confirmPassword',
      desc: '',
      args: [],
    );
  }

  /// `نسيت كلمة المرور؟`
  String get forgotPassword {
    return Intl.message(
      'نسيت كلمة المرور؟',
      name: 'forgotPassword',
      desc: '',
      args: [],
    );
  }

  /// `إعادة تعيين كلمة المرور`
  String get resetPassword {
    return Intl.message(
      'إعادة تعيين كلمة المرور',
      name: 'resetPassword',
      desc: '',
      args: [],
    );
  }

  /// `رقم الهاتف المحمول`
  String get mobileNumber {
    return Intl.message(
      'رقم الهاتف المحمول',
      name: 'mobileNumber',
      desc: '',
      args: [],
    );
  }

  /// `سجل دخولك بحسابك الآن!`
  String get loginWithYourAccountNow {
    return Intl.message(
      'سجل دخولك بحسابك الآن!',
      name: 'loginWithYourAccountNow',
      desc: '',
      args: [],
    );
  }

  /// `سجل حسابك الآن!`
  String get registerWithYourAccountNow {
    return Intl.message(
      'سجل حسابك الآن!',
      name: 'registerWithYourAccountNow',
      desc: '',
      args: [],
    );
  }

  /// `تخطي`
  String get skip {
    return Intl.message('تخطي', name: 'skip', desc: '', args: []);
  }

  /// `المتاجر`
  String get stores {
    return Intl.message('المتاجر', name: 'stores', desc: '', args: []);
  }

  /// `الاسم الكامل`
  String get fullName {
    return Intl.message('الاسم الكامل', name: 'fullName', desc: '', args: []);
  }

  /// `التسجيل كمتجر؟`
  String get registerAsStore {
    return Intl.message(
      'التسجيل كمتجر؟',
      name: 'registerAsStore',
      desc: '',
      args: [],
    );
  }

  /// `التسجيل كطبيب؟`
  String get registerAsDoctor {
    return Intl.message(
      'التسجيل كطبيب؟',
      name: 'registerAsDoctor',
      desc: '',
      args: [],
    );
  }

  /// `الرئيسية`
  String get home {
    return Intl.message('الرئيسية', name: 'home', desc: '', args: []);
  }

  /// `الريلز`
  String get reels {
    return Intl.message('الريلز', name: 'reels', desc: '', args: []);
  }

  /// `المتاجر`
  String get shops {
    return Intl.message('المتاجر', name: 'shops', desc: '', args: []);
  }

  /// `الأطباء`
  String get doctors {
    return Intl.message('الأطباء', name: 'doctors', desc: '', args: []);
  }

  /// `عرض الكل`
  String get seeAll {
    return Intl.message('عرض الكل', name: 'seeAll', desc: '', args: []);
  }

  /// `مرحباً، {name}`
  String welcomeWithName(Object name) {
    return Intl.message(
      'مرحباً، $name',
      name: 'welcomeWithName',
      desc: '',
      args: [name],
    );
  }

  /// `الحيوانات`
  String get animals {
    return Intl.message('الحيوانات', name: 'animals', desc: '', args: []);
  }

  /// `المنتجات`
  String get products {
    return Intl.message('المنتجات', name: 'products', desc: '', args: []);
  }

  /// `حول`
  String get about {
    return Intl.message('حول', name: 'about', desc: '', args: []);
  }

  /// `اعثر على حيوانك الأليف المثالي`
  String get onBoardingTitle1 {
    return Intl.message(
      'اعثر على حيوانك الأليف المثالي',
      name: 'onBoardingTitle1',
      desc: '',
      args: [],
    );
  }

  /// `استكشف أفضل متاجر الحيوانات الأليفة`
  String get onBoardingTitle2 {
    return Intl.message(
      'استكشف أفضل متاجر الحيوانات الأليفة',
      name: 'onBoardingTitle2',
      desc: '',
      args: [],
    );
  }

  /// `اعتن بصحة حيوانك الأليف`
  String get onBoardingTitle3 {
    return Intl.message(
      'اعتن بصحة حيوانك الأليف',
      name: 'onBoardingTitle3',
      desc: '',
      args: [],
    );
  }

  /// `اكتشف مجموعة واسعة من الحيوانات الأليفة للتبني واصطحب الرفيق المثالي إلى المنزل.`
  String get onBoardingDescription1 {
    return Intl.message(
      'اكتشف مجموعة واسعة من الحيوانات الأليفة للتبني واصطحب الرفيق المثالي إلى المنزل.',
      name: 'onBoardingDescription1',
      desc: '',
      args: [],
    );
  }

  /// `تصفح متاجر الحيوانات الأليفة الموثوقة واعثر على أفضل المنتجات لأصدقائك ذوي الفراء.`
  String get onBoardingDescription2 {
    return Intl.message(
      'تصفح متاجر الحيوانات الأليفة الموثوقة واعثر على أفضل المنتجات لأصدقائك ذوي الفراء.',
      name: 'onBoardingDescription2',
      desc: '',
      args: [],
    );
  }

  /// `تواصل مع الأطباء البيطريين المحترفين وتأكد من حصول حيواناتك الأليفة على أفضل رعاية.`
  String get onBoardingDescription3 {
    return Intl.message(
      'تواصل مع الأطباء البيطريين المحترفين وتأكد من حصول حيواناتك الأليفة على أفضل رعاية.',
      name: 'onBoardingDescription3',
      desc: '',
      args: [],
    );
  }

  /// `التالي`
  String get next {
    return Intl.message('التالي', name: 'next', desc: '', args: []);
  }

  /// `ابدأ الآن`
  String get startNow {
    return Intl.message('ابدأ الآن', name: 'startNow', desc: '', args: []);
  }

  /// `الوصف`
  String get description {
    return Intl.message('الوصف', name: 'description', desc: '', args: []);
  }

  /// `العنوان`
  String get address {
    return Intl.message('العنوان', name: 'address', desc: '', args: []);
  }

  /// `أدخل`
  String get enter {
    return Intl.message('أدخل', name: 'enter', desc: '', args: []);
  }

  /// `اسم الطبيب`
  String get doctorName {
    return Intl.message('اسم الطبيب', name: 'doctorName', desc: '', args: []);
  }

  /// `البريد الإلكتروني (اختياري)`
  String get emailOptional {
    return Intl.message(
      'البريد الإلكتروني (اختياري)',
      name: 'emailOptional',
      desc: '',
      args: [],
    );
  }

  /// `شعار الطبيب`
  String get doctorLogo {
    return Intl.message('شعار الطبيب', name: 'doctorLogo', desc: '', args: []);
  }

  /// `حفظ`
  String get save {
    return Intl.message('حفظ', name: 'save', desc: '', args: []);
  }

  /// `إرسال`
  String get submit {
    return Intl.message('إرسال', name: 'submit', desc: '', args: []);
  }

  /// `اختيار صورة`
  String get pickImage {
    return Intl.message('اختيار صورة', name: 'pickImage', desc: '', args: []);
  }

  /// `تم اختيار الموقع بنجاح`
  String get locationPickedSuccessfully {
    return Intl.message(
      'تم اختيار الموقع بنجاح',
      name: 'locationPickedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `اختيار الموقع`
  String get pickLocation {
    return Intl.message(
      'اختيار الموقع',
      name: 'pickLocation',
      desc: '',
      args: [],
    );
  }

  /// `اضغط لاختيار الموقع`
  String get tapToSelectLocation {
    return Intl.message(
      'اضغط لاختيار الموقع',
      name: 'tapToSelectLocation',
      desc: '',
      args: [],
    );
  }

  /// `تغيير الموقع`
  String get changeLocation {
    return Intl.message(
      'تغيير الموقع',
      name: 'changeLocation',
      desc: '',
      args: [],
    );
  }

  /// `لم يتم العثور على بيانات`
  String get noDataFound {
    return Intl.message(
      'لم يتم العثور على بيانات',
      name: 'noDataFound',
      desc: '',
      args: [],
    );
  }

  /// `تغيير وسائل التواصل`
  String get changeSocial {
    return Intl.message(
      'تغيير وسائل التواصل',
      name: 'changeSocial',
      desc: '',
      args: [],
    );
  }

  /// `يرجى إضافة رابط صحيح`
  String get pleaseAddValidLink {
    return Intl.message(
      'يرجى إضافة رابط صحيح',
      name: 'pleaseAddValidLink',
      desc: '',
      args: [],
    );
  }

  /// `وسائل التواصل الاجتماعي`
  String get socialMedia {
    return Intl.message(
      'وسائل التواصل الاجتماعي',
      name: 'socialMedia',
      desc: '',
      args: [],
    );
  }

  /// `خلفية الطبيب`
  String get doctorBackground {
    return Intl.message(
      'خلفية الطبيب',
      name: 'doctorBackground',
      desc: '',
      args: [],
    );
  }

  /// `يرجى إضافة وسائل التواصل الاجتماعي الخاصة بك`
  String get pleaseAddYourSocialMedia {
    return Intl.message(
      'يرجى إضافة وسائل التواصل الاجتماعي الخاصة بك',
      name: 'pleaseAddYourSocialMedia',
      desc: '',
      args: [],
    );
  }

  /// `يرجى إضافة موقعك`
  String get pleaseAddYourLocation {
    return Intl.message(
      'يرجى إضافة موقعك',
      name: 'pleaseAddYourLocation',
      desc: '',
      args: [],
    );
  }

  /// `يمكنك أيضاً التسجيل كطبيب من ملفك الشخصي.`
  String get youCanAlsoRegisterAsDoctor {
    return Intl.message(
      'يمكنك أيضاً التسجيل كطبيب من ملفك الشخصي.',
      name: 'youCanAlsoRegisterAsDoctor',
      desc: '',
      args: [],
    );
  }

  /// `يمكنك أيضاً التسجيل كمتجر من ملفك الشخصي.`
  String get youCanAlsoRegisterAsStore {
    return Intl.message(
      'يمكنك أيضاً التسجيل كمتجر من ملفك الشخصي.',
      name: 'youCanAlsoRegisterAsStore',
      desc: '',
      args: [],
    );
  }

  /// `شعار المتجر`
  String get storeLogo {
    return Intl.message('شعار المتجر', name: 'storeLogo', desc: '', args: []);
  }

  /// `خلفية المتجر`
  String get storeBackground {
    return Intl.message(
      'خلفية المتجر',
      name: 'storeBackground',
      desc: '',
      args: [],
    );
  }

  /// `اسم المتجر`
  String get storeName {
    return Intl.message('اسم المتجر', name: 'storeName', desc: '', args: []);
  }

  /// `البحث`
  String get search {
    return Intl.message('البحث', name: 'search', desc: '', args: []);
  }

  /// `البحث عن المنتجات`
  String get searchForProducts {
    return Intl.message(
      'البحث عن المنتجات',
      name: 'searchForProducts',
      desc: '',
      args: [],
    );
  }

  /// `البحث عن المتاجر`
  String get searchForStores {
    return Intl.message(
      'البحث عن المتاجر',
      name: 'searchForStores',
      desc: '',
      args: [],
    );
  }

  /// `البحث عن الأطباء`
  String get searchForDoctors {
    return Intl.message(
      'البحث عن الأطباء',
      name: 'searchForDoctors',
      desc: '',
      args: [],
    );
  }

  /// `إنشاء حساب`
  String get createAccount {
    return Intl.message(
      'إنشاء حساب',
      name: 'createAccount',
      desc: '',
      args: [],
    );
  }

  /// `رقم الهوية`
  String get idNumber {
    return Intl.message('رقم الهوية', name: 'idNumber', desc: '', args: []);
  }

  /// `أقر بأنني قد قرأت ووافقت على الشروط والأحكام الخاصة باستخدام هذا التطبيق`
  String get termsAndConditions {
    return Intl.message(
      'أقر بأنني قد قرأت ووافقت على الشروط والأحكام الخاصة باستخدام هذا التطبيق',
      name: 'termsAndConditions',
      desc: '',
      args: [],
    );
  }

  /// `رقم الهوية غير صحيح`
  String get invalidIdNumber {
    return Intl.message(
      'رقم الهوية غير صحيح',
      name: 'invalidIdNumber',
      desc: '',
      args: [],
    );
  }

  /// `رقم الهاتف غير صحيح`
  String get invalidPhoneNumber {
    return Intl.message(
      'رقم الهاتف غير صحيح',
      name: 'invalidPhoneNumber',
      desc: '',
      args: [],
    );
  }

  /// `كلمات المرور غير متطابقة`
  String get passwordsDoNotMatch {
    return Intl.message(
      'كلمات المرور غير متطابقة',
      name: 'passwordsDoNotMatch',
      desc: '',
      args: [],
    );
  }

  /// `يرجى الموافقة على الشروط والأحكام`
  String get pleaseAcceptTerms {
    return Intl.message(
      'يرجى الموافقة على الشروط والأحكام',
      name: 'pleaseAcceptTerms',
      desc: '',
      args: [],
    );
  }

  /// `خطأ`
  String get error {
    return Intl.message('خطأ', name: 'error', desc: '', args: []);
  }

  /// `أقر بأنني قد قرأت وفهمت وأوافق على`
  String get termsAndConditionsText {
    return Intl.message(
      'أقر بأنني قد قرأت وفهمت وأوافق على',
      name: 'termsAndConditionsText',
      desc: '',
      args: [],
    );
  }

  /// `الشروط والأحكام`
  String get termsAndConditionsLink {
    return Intl.message(
      'الشروط والأحكام',
      name: 'termsAndConditionsLink',
      desc: '',
      args: [],
    );
  }

  /// `الخاصة باستخدام هذا التطبيق`
  String get termsAndConditionsEnd {
    return Intl.message(
      'الخاصة باستخدام هذا التطبيق',
      name: 'termsAndConditionsEnd',
      desc: '',
      args: [],
    );
  }

  /// `الشروط والأحكام`
  String get termsDialogTitle {
    return Intl.message(
      'الشروط والأحكام',
      name: 'termsDialogTitle',
      desc: '',
      args: [],
    );
  }

  /// `هذه هي الشروط والأحكام الخاصة بالتطبيق. يرجى قراءتها بعناية قبل الموافقة عليها. سيتم إضافة المحتوى الفعلي للشروط والأحكام هنا لاحقاً.`
  String get termsDialogContent {
    return Intl.message(
      'هذه هي الشروط والأحكام الخاصة بالتطبيق. يرجى قراءتها بعناية قبل الموافقة عليها. سيتم إضافة المحتوى الفعلي للشروط والأحكام هنا لاحقاً.',
      name: 'termsDialogContent',
      desc: '',
      args: [],
    );
  }

  /// `إغلاق`
  String get close {
    return Intl.message('إغلاق', name: 'close', desc: '', args: []);
  }

  /// `05xxxxxxxx`
  String get phoneHint {
    return Intl.message('05xxxxxxxx', name: 'phoneHint', desc: '', args: []);
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'ar'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
