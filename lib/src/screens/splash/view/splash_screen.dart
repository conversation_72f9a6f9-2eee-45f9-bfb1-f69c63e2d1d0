import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:dropx/generated/assets.gen.dart';
import 'package:dropx/src/screens/on_boarding/view/on_boarding.screen.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../auth/view/register/register.screen.dart';

class SplashScreen extends HookWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final loggedIn = GetStorageService.hasData(key: LocalKeys.user);

    useEffect(() {
      const navigateWidget = RegisterScreen();
      // OnBoardingScreen();
      // const LoginScreen();
      // loggedIn ? const MainScreen() : const LoginScreen();

      Future.delayed(const Duration(seconds: 6), () {
        navigateWidget.navigate;
      });

      return () {};
    }, []);

    return Scaffold(
      body: Center(
        child: Assets.images.logo.image(fit: BoxFit.cover),
      ),
    );
  }
}
