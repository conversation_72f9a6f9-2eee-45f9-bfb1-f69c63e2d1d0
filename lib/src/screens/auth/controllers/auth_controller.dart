import 'package:dropx/src/core/consts/network/api_strings.dart';
import 'package:dropx/src/screens/auth/models/user_model.dart';
import 'package:dropx/src/screens/auth/repositories/auth_repository.dart';
import 'package:dropx/src/screens/auth/view/login/login.screen.dart';
import 'package:dropx/src/screens/main_screen/view/main.screen.dart';
import 'package:xr_helper/xr_helper.dart';

class AuthController extends BaseVM {
  final AuthRepository authRepo;

  AuthController({
    required this.authRepo,
  });

  // * Login
  Future<bool> login({
    required Map<String, dynamic> data,
  }) async {
    return await baseFunction(
      () async {
        final user = await _setUser(data);

        final userData = await authRepo.login(user: user);

        return userData;
      },
      additionalFunction: () {
        const MainScreen().navigate;
      },
    );
  }

  // * Register
  Future<bool> register({
    required Map<String, dynamic> data,
  }) async {
    return await baseFunction(
      () async {
        final user = await _setUser(data);

        final userData = await authRepo.register(user: user);

        return userData;
      },
      additionalFunction: () {
        const MainScreen().navigate;
      },
    );
  }

  // * Set User
  Future<UserModel> _setUser(
    Map<String, dynamic> data,
  ) async {
    // final fcmToken = await NotificationService.getToken();

    final user = UserModel(
      name: data[FieldsConsts.name],
      mobile: data[FieldsConsts.mobile],
      password: data[FieldsConsts.password],
      deviceToken: 'test',
    );

    return user;
  }

  // * Logout
  Future<void> logout() async {
    return await baseFunction(
      () async {
        await authRepo.logout();

        const LoginScreen().navigateReplacement;
      },
    );
  }

// * Get Countries
// Future<List<CountryModel>> getCountries() async {
//   return await baseFunction(
//     () async {
//       return await authRepo.getCountries();
//     },
//   );
// }
}
