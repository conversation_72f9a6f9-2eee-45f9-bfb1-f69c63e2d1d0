import 'package:dropx/src/core/consts/network/api_endpoints.dart';
import 'package:dropx/src/screens/auth/models/user_model.dart';
import 'package:xr_helper/xr_helper.dart';

class AuthRepository with BaseRepository {
  final BaseApiServices networkApiService;

  AuthRepository({
    required this.networkApiService,
  });

  // * Login
  Future<bool> login({
    required UserModel user,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.login;

        final response =
            await networkApiService.postResponse(url, body: user.toLoginJson());

        saveUserData(response);

        return true;
      },
    );
  }

  // * Register
  Future<Map<String, dynamic>> register({
    required UserModel user,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.register;

        final response = await networkApiService.postResponse(url,
            body: user.toRegisterJson());

        return response['data'];
      },
    );
  }

  // * Verify Code
  Future<bool> verifyCode({
    required UserModel user,
    required String code,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.verifyCode;

        final response = await networkApiService.postResponse(url,
            body: user.toVerifyJson(code));

        saveUserData(response);

        return true;
      },
    );
  }

  // * Save to local
  void saveUserData(Map<String, dynamic> data) {
    final userData = UserModel.fromJson(data['data']['user']);

    GetStorageService.setData(
      key: LocalKeys.user,
      value: userData.toRegisterJson(),
    );

    GetStorageService.setData(
      key: LocalKeys.token,
      value: data['data']['token'],
    );
  }

  // * Get Countries
  // Future<List<CountryModel>> getCountries() async {
  //   return baseFunction(
  //     () async {
  //       const url = ApiEndpoints.countries;
  //
  //       final response = await networkApiService.getResponse(url);
  //
  //       final countries = response['countries'] as List;
  //
  //       final countriesList =
  //           countries.map((country) => CountryModel.fromJson(country)).toList();
  //
  //       return countriesList;
  //     },
  //   );
  // }

  // * Logout
  Future<void> logout() async {
    await baseFunction(
      () async {
        //! Clear Local Data
        GetStorageService.clearLocalData();
      },
    );
  }
}
