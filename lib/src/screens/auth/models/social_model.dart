import 'package:equatable/equatable.dart';

import 'user_model.dart';

class SocialModel extends Equatable {
  final int? id;
  final String link;
  final UserType type;

  const SocialModel({
    this.id,
    this.link = '',
    this.type = UserType.user,
  });

  // * From Json
  factory SocialModel.fromJson(Map<String, dynamic> json) {
    return SocialModel(
      id: json['id'],
      link: json['link'] ?? '',
    );
  }

  // * To Json
  Map<String, dynamic> toJson() {
    return {
      'url': link,
      'type': type.name,
    };
  }

  factory SocialModel.empty() => const SocialModel();

  @override
  List<Object?> get props => [
        id,
        link,
        type,
      ];
}
