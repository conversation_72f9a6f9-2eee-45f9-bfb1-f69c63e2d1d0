import 'package:equatable/equatable.dart';
import 'package:xr_helper/xr_helper.dart';

class UserModel extends Equatable {
  final int? id;
  final String? name;
  final String? email;
  final String? mobile;
  final String? password;
  final String? deviceToken;

  const UserModel({
    this.id,
    this.name,
    this.email,
    this.mobile,
    this.password,
    this.deviceToken,
  });

  // * For Login ================================
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      mobile: json['phone'] ?? '',
    );
  }

  //? Copy With

  // * To Json ================================
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'email_phone': mobile,
      'fcm_token': deviceToken,
      'password': password,
      'password_confirmation': password,
    };
  }

  // * To Login Json ================================
  Map<String, dynamic> toLoginJson() {
    return {
      'name': name,
      'email_phone': mobile,
      'fcm_token': deviceToken,
      'password': password,
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        mobile,
        password,
        deviceToken,
      ];

  //? Get saved user
  static UserModel currentUser() {
    final userData = GetStorageService.getData(key: LocalKeys.user);

    if (userData == null) {
      return const UserModel();
    } else {
      return UserModel.fromJson(userData);
    }
  }
}
