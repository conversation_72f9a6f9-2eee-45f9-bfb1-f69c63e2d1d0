import 'package:equatable/equatable.dart';
import 'package:xr_helper/xr_helper.dart';

class UserModel extends Equatable {
  final int? id;
  final String? name;
  final String? email;
  final String? phone;
  final String? password;
  final String? deviceToken;
  final String? nationalId;

  const UserModel({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.password,
    this.deviceToken,
    this.nationalId,
  });

  // * For Login ================================
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      name: json['name'] ?? '',
      email: json['email'],
      phone: json['phone'] ?? '',
    );
  }

  //? Copy With

  // * To Register Json ================================
  Map<String, dynamic> toRegisterJson() {
    return {
      'name': name,
      'phone': phone,
      'password': password,
      'national_id': nationalId,
    };
  }

  // * To Login Json ================================
  Map<String, dynamic> toLoginJson() {
    return {
      'phone': phone,
      'password': password,
    };
  }

  // * To Verify Json ================================
  Map<String, dynamic> toVerifyJson(String code) {
    return {
      'phone': phone,
      'code': code,
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        phone,
        password,
        deviceToken,
        nationalId,
      ];

  //? Get saved user
  static UserModel currentUser() {
    final userData = GetStorageService.getData(key: LocalKeys.user);

    if (userData == null) {
      return const UserModel();
    } else {
      return UserModel.fromJson(userData);
    }
  }
}
