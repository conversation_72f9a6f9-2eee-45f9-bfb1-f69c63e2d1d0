import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'widgets/register_fields_container.widget.dart';
import 'widgets/register_header.widget.dart';

class RegisterScreen extends HookConsumerWidget {
  const RegisterScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormBuilderState>());

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: SingleChildScrollView(
          child: FormBuilder(
            key: form<PERSON><PERSON>,
            child: Column(
              children: [
                // Header
                const RegisterHeader(),

                // Form Content
                RegisterFieldsContainer(
                  formKey: formKey,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
