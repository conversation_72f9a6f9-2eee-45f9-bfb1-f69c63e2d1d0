import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/generated/assets.gen.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/main_screen/view/main.screen.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/register_fields_container.widget.dart';

class RegisterScreen extends HookConsumerWidget {
  const RegisterScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormBuilderState>());

    return Scaffold(
      body: Column(
        children: [
          // Green Header
          Container(
            width: double.infinity,
            decoration: const BoxDecoration(
              color: ColorManager.primaryColor,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(30),
                bottomRight: Radius.circular(30),
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(AppSpaces.padding24),
                child: Column(
                  children: [
                    // Logo
                    Assets.images.logoSymbol.image(
                      width: 60.w,
                      height: 60.h,
                      color: ColorManager.white,
                    ),
                    AppGaps.gap16,
                    // Title
                    Text(
                      context.tr.createAccount,
                      style: AppTextStyles.title.copyWith(
                        color: ColorManager.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 24.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Form Content
          Expanded(
            child: FormBuilder(
              key: formKey,
              child: Padding(
                padding: const EdgeInsets.all(AppSpaces.padding16),
                child: RegisterFieldsContainer(
                  formKey: formKey,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
