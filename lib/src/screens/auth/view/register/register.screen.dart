import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/generated/assets.gen.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/main_screen/view/main.screen.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/register_fields_container.widget.dart';

class RegisterScreen extends HookConsumerWidget {
  const RegisterScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormBuilderState>());

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        surfaceTintColor: Colors.transparent,
        actions: [
          TextButton(
            onPressed: () {
              const MainScreen().navigate;
            },
            child: Row(
              children: [
                Text(
                  context.tr.skip,
                  style: AppTextStyles.title,
                ),
                AppGaps.gap8,
                const CircleAvatar(
                  radius: 18,
                  backgroundColor: ColorManager.primaryColor,
                  child: Icon(
                    Icons.arrow_forward_ios,
                    color: ColorManager.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: FormBuilder(
        key: formKey,
        child: Padding(
          padding: const EdgeInsets.all(AppSpaces.padding12),
          child: ListView(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(AppRadius.radius28),
                child:
                    Assets.images.logo.image(fit: BoxFit.cover, width: 220.w),
              ).center(),
              Text(
                context.tr.registerWithYourAccountNow,
                style: AppTextStyles.title.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ).center(),
              AppGaps.gap24,

              // * Fields Container
              RegisterFieldsContainer(
                formKey: formKey,
              )
            ],
          ),
        ),
      ),
    );
  }
}
