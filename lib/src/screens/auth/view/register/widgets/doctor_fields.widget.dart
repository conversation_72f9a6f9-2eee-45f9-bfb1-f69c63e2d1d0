import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:dropx/src/core/consts/network/api_strings.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/fields/base_image_picker.dart';
import 'package:dropx/src/core/shared/widgets/fields/text_field.dart';
import 'package:dropx/src/core/shared/widgets/map/map_picker.dart';
import 'package:dropx/src/core/shared/widgets/social_fields/social_fields.widget.dart';
import 'package:dropx/src/screens/auth/models/social_model.dart';
import 'package:xr_helper/xr_helper.dart';

class DoctorFieldsWidget extends ConsumerWidget {
  final Map<String, ValueNotifier> valueNotifiers;

  const DoctorFieldsWidget({
    super.key,
    required this.valueNotifiers,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            Expanded(
              child: BaseImagePicker(
                name: FieldsConsts.doctorLogo,
                label: context.tr.doctorLogo,
              ),
            ),
            AppGaps.gap12,
            Expanded(
              child: BaseImagePicker(
                name: FieldsConsts.doctorBackground,
                label: context.tr.doctorBackground,
              ),
            ),
          ],
        ),

        AppGaps.gap12,

        //! Doctor Name ------------------------------
        BaseTextField(
          name: FieldsConsts.doctorName,
          title: context.tr.doctorName,
        ),

        AppGaps.gap12,

        //! Description ------------------------------
        BaseTextField(
          name: FieldsConsts.doctorDescription,
          title: context.tr.description,
          maxLines: 3,
        ),

        AppGaps.gap12,

        //! Address ------------------------------
        BaseTextField(
          name: FieldsConsts.doctorAddress,
          title: context.tr.address,
          maxLines: 3,
        ),

        AppGaps.gap16,

        SocialWidgetFields(
            socialFields: valueNotifiers[FieldsConsts.social]!
                as ValueNotifier<List<SocialModel>>),

        AppGaps.gap16,

        MapLocationPickerField(
          selectedLocation:
              valueNotifiers[FieldsConsts.doctorLoc]! as ValueNotifier<LatLng?>,
        ),
      ],
    );
  }
}
