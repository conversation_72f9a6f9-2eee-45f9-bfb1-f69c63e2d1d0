import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/consts/network/api_strings.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/fields/text_field.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/auth/view/login/login.screen.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../providers/auth_providers.dart';

class RegisterFieldsContainer extends HookConsumerWidget {
  final GlobalKey<FormBuilderState> formKey;

  const RegisterFieldsContainer({super.key, required this.formKey});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerNotifierProvider);
    final termsAccepted = useState(false);

    void register() {
      if (!formKey.currentState!.saveAndValidate()) return;

      if (!termsAccepted.value) {
        showToast(context.tr.pleaseAcceptTerms, isError: true);
        return;
      }

      final data = formKey.currentState?.instantValue ?? {};

      authController.register(data: data);
    }

    return ListView(
      children: [
        // Name Field
        BaseTextField(
          name: FieldsConsts.name,
          title: context.tr.fullName,
          validator: (value) => Validations.mustBeNotEmpty(
            value,
            emptyMessage: context.tr.fullName,
          ),
        ),

        AppGaps.gap16,

        // Phone Number Field
        BaseTextField(
          name: FieldsConsts.mobile,
          title: context.tr.mobileNumber,
          textInputType: TextInputType.phone,
          validator: (value) => Validations.palestinianPhoneNumber(
            value,
            emptyMessage: context.tr.mobileNumber,
            invalidMessage: context.tr.invalidPhoneNumber,
          ),
        ),

        AppGaps.gap16,

        // Password Field
        BaseTextField(
          name: FieldsConsts.password,
          title: context.tr.password,
          textInputType: TextInputType.visiblePassword,
          isObscure: true,
          validator: (value) => Validations.password(
            value,
            emptyPasswordMessage: context.tr.password,
          ),
        ),

        AppGaps.gap16,

        // Confirm Password Field
        BaseTextField(
          name: FieldsConsts.confirmPassword,
          title: context.tr.confirmPassword,
          textInputType: TextInputType.visiblePassword,
          isObscure: true,
          validator: (value) {
            final password =
                formKey.currentState?.fields[FieldsConsts.password]?.value;
            return Validations.confirmPassword(
              value,
              password,
              emptyMessage: context.tr.confirmPassword,
              mismatchMessage: context.tr.passwordsDoNotMatch,
            );
          },
        ),

        AppGaps.gap16,

        // ID Number Field
        BaseTextField(
          name: FieldsConsts.idNumber,
          title: context.tr.idNumber,
          textInputType: TextInputType.number,
          validator: (value) => Validations.palestinianId(
            value,
            emptyMessage: context.tr.idNumber,
            invalidMessage: context.tr.invalidIdNumber,
          ),
        ),

        AppGaps.gap24,

        // Terms and Conditions Checkbox
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Checkbox(
              value: termsAccepted.value,
              onChanged: (value) {
                termsAccepted.value = value ?? false;
              },
              activeColor: ColorManager.primaryColor,
            ),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  termsAccepted.value = !termsAccepted.value;
                },
                child: Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Text(
                    context.tr.termsAndConditions,
                    style: AppTextStyles.labelMedium.copyWith(
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),

        AppGaps.gap24,

        // Register Button
        Button(
          isLoading: authController.isLoading,
          label: context.tr.createAccount,
          onPressed: register,
        ),

        AppGaps.gap16,

        // Have Account? Login
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              context.tr.haveAnAccount,
              style: AppTextStyles.labelMedium,
            ),
            TextButton(
              onPressed: () {
                const LoginScreen().navigate;
              },
              child: Text(
                context.tr.login,
                style: AppTextStyles.labelMedium.copyWith(
                  color: ColorManager.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
