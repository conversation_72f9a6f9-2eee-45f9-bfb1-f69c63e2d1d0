import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/consts/network/api_strings.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/fields/text_field.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/auth/models/social_model.dart';
import 'package:dropx/src/screens/auth/view/register/widgets/doctor_fields.widget.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../providers/auth_providers.dart';
import 'store_fields.widget.dart';

class RegisterFieldsContainer extends HookConsumerWidget {
  final GlobalKey<FormBuilderState> formKey;

  const RegisterFieldsContainer({super.key, required this.formKey});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerNotifierProvider);

    final valueNotifiers = {
      FieldsConsts.isDoctor: useState(false),
      FieldsConsts.isStore: useState(false),
      FieldsConsts.doctorLoc: useState<LatLng?>(null),
      FieldsConsts.storeLoc: useState<LatLng?>(null),
      FieldsConsts.social: useState<List<SocialModel>>([]),
    };

    final isStore =
        valueNotifiers[FieldsConsts.isStore]! as ValueNotifier<bool>;
    final isDoctor =
        valueNotifiers[FieldsConsts.isDoctor]! as ValueNotifier<bool>;

    final socialMedia = valueNotifiers[FieldsConsts.social]!
        as ValueNotifier<List<SocialModel>>;

    void register() {
      if (!formKey.currentState!.saveAndValidate()) return;

      final data = formKey.currentState?.instantValue ?? {};

      if (socialMedia.value.isEmpty) {
        showToast(context.tr.pleaseAddYourSocialMedia, isError: true);
        return;
      }

      final location = isDoctor.value
          ? valueNotifiers[FieldsConsts.doctorLoc]!.value
          : valueNotifiers[FieldsConsts.storeLoc]!.value;

      if (location == null) {
        showToast(context.tr.pleaseAddYourLocation, isError: true);
        return;
      }

      authController.register(
        data: data,
      );
    }

    return Container(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      decoration: BoxDecoration(
        color: ColorManager.containerColor,
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          //! Mobile Number ------------------------------
          BaseTextField(
            name: FieldsConsts.name,
            hint: 'John Doe',
            title: context.tr.fullName,
          ),

          AppGaps.gap12,

          //! Email ------------------------------
          BaseTextField(
            name: FieldsConsts.email,
            title: context.tr.emailOptional,
            isRequired: false,
          ),

          AppGaps.gap12,

          //! Mobile Number ------------------------------
          BaseTextField(
            name: FieldsConsts.mobile,
            title: context.tr.mobileNumber,
          ),

          AppGaps.gap12,

          //! Password ------------------------------
          BaseTextField(
            name: FieldsConsts.password,
            title: context.tr.password,
            hint: '********',
            textInputType: TextInputType.visiblePassword,
            withoutEnter: true,
            isObscure: true,
          ),

          AppGaps.gap12,

          //? Register as store
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    isStore.value = !isStore.value;

                    if (isStore.value) {
                      isDoctor.value = false;
                    }
                  },
                  child: Row(
                    children: [
                      Checkbox(
                        value: isStore.value,
                        onChanged: (value) {
                          isStore.value = value!;

                          if (isStore.value) {
                            isDoctor.value = false;
                          }
                        },
                      ),
                      Expanded(
                          child: Text(
                        context.tr.registerAsStore,
                        style: AppTextStyles.labelMedium.copyWith(fontSize: 13),
                      )),
                    ],
                  ),
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    isDoctor.value = !isDoctor.value;

                    if (isDoctor.value) {
                      isStore.value = false;
                    }
                  },
                  child: Row(
                    children: [
                      Checkbox(
                        value: isDoctor.value,
                        onChanged: (value) {
                          isDoctor.value = value!;

                          if (isDoctor.value) {
                            isStore.value = false;
                          }
                        },
                      ),
                      Expanded(
                          child: FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                context.tr.registerAsDoctor,
                                style: AppTextStyles.labelMedium
                                    .copyWith(fontSize: 13),
                              ))),
                    ],
                  ),
                ),
              ),
            ],
          ),

          if (isDoctor.value || isStore.value) ...[
            Text(
              isDoctor.value
                  ? context.tr.youCanAlsoRegisterAsStore
                  : context.tr.youCanAlsoRegisterAsDoctor,
              style: AppTextStyles.labelMedium.copyWith(
                fontSize: 12,
                color: ColorManager.primaryColor,
              ),
            ),
            AppGaps.gap4,
          ],

          // * Doctor Fields
          if (isDoctor.value) ...[
            AppGaps.gap12,
            DoctorFieldsWidget(
              valueNotifiers: valueNotifiers,
            ),
          ] else if (isStore.value) ...[
            AppGaps.gap12,
            StoreFieldsWidget(
              valueNotifiers: valueNotifiers,
            ),
          ],

          AppGaps.gap12,

          // don't have an account, register
          Row(
            children: [
              Text(context.tr.dontHaveAnAccount),
              TextButton(
                onPressed: () {
                  // const RegisterWithMobileScreen().navigate;
                },
                child: Text(
                  context.tr.login,
                  style: AppTextStyles.subTitle.copyWith(
                    color: ColorManager.primaryColor,
                    fontWeight: FontWeight.bold,
                    decoration: TextDecoration.underline,
                    decorationColor: ColorManager.primaryColor,
                  ),
                ),
              ),
            ],
          ),

          Button(
            isLoading: authController.isLoading,
            label: context.tr.register,
            onPressed: register,
          )
        ],
      ),
    );
  }
}
