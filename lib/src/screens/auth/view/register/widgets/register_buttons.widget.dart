import 'dart:developer';

import 'package:dropx/src/core/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/auth/view/login/login.screen.dart';
import 'package:xr_helper/xr_helper.dart';

class RegisterButtons extends StatelessWidget {
  final bool isLoading;
  final bool isFormValid;
  final VoidCallback onRegister;

  const RegisterButtons({
    super.key,
    required this.isLoading,
    required this.isFormValid,
    required this.onRegister,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Register Button
        SizedBox(
          height: 50,
          width: double.infinity,
          child: Button(
            onPressed: isFormValid && !isLoading ? onRegister : null,
            isLoading: isLoading,
            loadingWidget: LoadingWidget(),
            color: isFormValid
                ? ColorManager.primaryColor
                : ColorManager.disabledButtonColor,
            label: context.tr.createAccount,
          ),
        ),

        AppGaps.gap16,

        // Have Account Text
        Text(
          context.tr.haveAnAccount,
          textAlign: TextAlign.center,
          style: AppTextStyles.labelMedium.copyWith(
            color: Colors.black54,
            fontSize: 14,
          ),
        ),

        AppGaps.gap8,

        // Login Button
        SizedBox(
          height: 50,
          width: double.infinity,
          child: OutlinedButton(
            onPressed: () {
              const LoginScreen().navigate;
            },
            style: OutlinedButton.styleFrom(
              foregroundColor: ColorManager.primaryColor,
              side: const BorderSide(
                color: ColorManager.primaryColor,
                width: 1.5,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              context.tr.login,
              style: AppTextStyles.labelLarge.copyWith(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: ColorManager.primaryColor,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
