import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/consts/network/api_strings.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/fields/text_field.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/auth/providers/auth_providers.dart';
import 'package:dropx/src/screens/auth/view/login/widgets/login_buttons.widget.dart';
import 'package:xr_helper/xr_helper.dart';

class LoginFieldsContainer extends HookConsumerWidget {
  final GlobalKey<FormBuilderState> formKey;

  const LoginFieldsContainer({super.key, required this.formKey});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerNotifierProvider);
    final rememberMe = useState(false);
    final isFormValid = useState(false);

    // Form validation state
    final phoneValid = useState(false);
    final passwordValid = useState(false);

    // Update form validity
    useEffect(() {
      isFormValid.value = phoneValid.value && passwordValid.value;
      return null;
    }, [phoneValid.value, passwordValid.value]);

    void login() {
      if (!formKey.currentState!.saveAndValidate()) return;

      final data = formKey.currentState?.instantValue ?? {};

      authController.login(data: data);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          AppGaps.gap16,

          // Phone Number Field
          BaseTextField(
            name: FieldsConsts.mobile,
            title: context.tr.mobileNumber,
            hint: context.tr.phoneHint,
            textInputType: TextInputType.phone,
            useUnderlineBorder: true,
            validator: (value) => Validations.palestinianPhoneNumber(
              value,
              emptyMessage: context.tr.mobileNumber,
              invalidMessage: context.tr.invalidPhoneNumber,
            ),
            realTimeValidator: (value) {
              final error = Validations.palestinianPhoneNumber(
                value,
                emptyMessage: context.tr.mobileNumber,
                invalidMessage: context.tr.invalidPhoneNumber,
              );
              phoneValid.value = error == null;
              return error;
            },
          ),

          AppGaps.gap16,

          // Password Field
          BaseTextField(
            name: FieldsConsts.password,
            title: context.tr.password,
            hint: context.tr.password,
            textInputType: TextInputType.visiblePassword,
            isObscure: true,
            useUnderlineBorder: true,
            validator: (value) => Validations.password(
              value,
              emptyPasswordMessage: context.tr.password,
            ),
            realTimeValidator: (value) {
              final error = Validations.password(
                value,
                emptyPasswordMessage: context.tr.password,
              );
              passwordValid.value = error == null;
              return error;
            },
          ),

          AppGaps.gap16,

          // Remember Me & Forgot Password
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  SizedBox(
                    width: 24,
                    height: 24,
                    child: Checkbox(
                      value: rememberMe.value,
                      onChanged: (value) {
                        rememberMe.value = value ?? false;
                      },
                      activeColor: ColorManager.primaryColor,
                      side: const BorderSide(color: Colors.black, width: 1.5),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                  AppGaps.gap8,
                  Text(
                    context.tr.rememberMe,
                    style: AppTextStyles.labelMedium.copyWith(fontSize: 13),
                  ),
                ],
              ),
              TextButton(
                onPressed: () {
                  // Handle forgot password
                },
                child: Text(
                  context.tr.forgotPasswordLink,
                  style: AppTextStyles.labelMedium.copyWith(
                    color: ColorManager.termsLinkColor,
                    fontSize: 13,
                  ),
                ),
              ),
            ],
          ),

          AppGaps.gap24,

          // Buttons
          LoginButtons(
            isLoading: authController.isLoading,
            isFormValid: isFormValid.value,
            onLogin: login,
          ),

          AppGaps.gap16,
        ],
      ),
    );
  }
}
