import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dropx/src/core/consts/network/api_strings.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/fields/text_field.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/auth/providers/auth_providers.dart';
import 'package:dropx/src/screens/auth/view/register/register.screen.dart';
import 'package:xr_helper/xr_helper.dart';

class LoginFieldsContainer extends ConsumerWidget {
  final GlobalKey<FormBuilderState> formKey;

  const LoginFieldsContainer({super.key, required this.formKey});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerNotifierProvider);
    void login() {
      if (!formKey.currentState!.saveAndValidate()) return;

      final data = formKey.currentState?.instantValue ?? {};

      authController.login(
        data: data,
      );
    }

    return Container(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      decoration: BoxDecoration(
        color: ColorManager.containerColor,
        borderRadius: BorderRadius.circular(AppRadius.radius12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          AppGaps.gap24,

          //! Mobile Number ------------------------------
          BaseTextField(
            name: FieldsConsts.mobile,
            hint: '**********',
            title: context.tr.mobileNumber,
          ),

          AppGaps.gap12,

          //! Password ------------------------------
          BaseTextField(
            name: FieldsConsts.password,
            title: context.tr.password,
            hint: '********',
            textInputType: TextInputType.visiblePassword,
            isObscure: true,
            withoutEnter: true,
          ),

          AppGaps.gap12,

          // don't have an account, register
          Row(
            children: [
              Text(context.tr.dontHaveAnAccount),
              TextButton(
                onPressed: () {
                  const RegisterScreen().navigate;
                },
                child: Text(
                  context.tr.register,
                  style: AppTextStyles.subTitle.copyWith(
                    color: ColorManager.primaryColor,
                    fontWeight: FontWeight.bold,
                    decoration: TextDecoration.underline,
                    decorationColor: ColorManager.primaryColor,
                  ),
                ),
              ),
            ],
          ),

          Button(
            isLoading: authController.isLoading,
            label: context.tr.login,
            onPressed: login,
          )
        ],
      ),
    );
  }
}
