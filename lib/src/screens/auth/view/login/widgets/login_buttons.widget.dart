import 'package:flutter/material.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/auth/view/register/register.screen.dart';
import 'package:xr_helper/xr_helper.dart';

class LoginButtons extends StatelessWidget {
  final bool isLoading;
  final bool isFormValid;
  final VoidCallback onLogin;

  const LoginButtons({
    super.key,
    required this.isLoading,
    required this.isFormValid,
    required this.onLogin,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Login Button
        SizedBox(
          height: 50,
          width: double.infinity,
          child: ElevatedButton(
            onPressed: isFormValid && !isLoading ? onLogin : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: isFormValid
                  ? ColorManager.primaryColor
                  : ColorManager.disabledButtonColor,
              foregroundColor: isFormValid ? ColorManager.white : Colors.black,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(
                    context.tr.login,
                    style: AppTextStyles.labelLarge.copyWith(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),

        AppGaps.gap16,

        // Don't Have Account Text
        Text(
          context.tr.dontHaveAccount,
          textAlign: TextAlign.center,
          style: AppTextStyles.labelMedium.copyWith(
            color: Colors.black54,
            fontSize: 14,
          ),
        ),

        AppGaps.gap8,

        // Register Button
        SizedBox(
          height: 50,
          width: double.infinity,
          child: OutlinedButton(
            onPressed: () {
              const RegisterScreen().navigate;
            },
            style: OutlinedButton.styleFrom(
              foregroundColor: ColorManager.primaryColor,
              side: const BorderSide(
                color: ColorManager.primaryColor,
                width: 1.5,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              context.tr.createAccount,
              style: AppTextStyles.labelLarge.copyWith(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: ColorManager.primaryColor,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
