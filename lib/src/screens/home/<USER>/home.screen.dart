import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/home/<USER>/widgets/home_slider.widget.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/animals_list.widget.dart';
import 'widgets/doctors_list.widget.dart';
import 'widgets/products_list.widget.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: ColorManager.white,
        surfaceTintColor: ColorManager.white,
        centerTitle: false,
        title: Text(
          context.tr.welcomeWithName('Amr'),
          style: AppTextStyles.title,
        ),
        leading: Padding(
          padding: const EdgeInsets.all(AppSpaces.padding8),
          child: CircleAvatar(
            backgroundColor: ColorManager.lightPrimaryColor,
            radius: 40.r,
            child: ClipOval(
              child: BaseCachedImage(
                height: 80.h,
                width: 80.w,
                'https://cdn4.iconfinder.com/data/icons/gamer-player-3d-avatars-3d-illustration-pack/512/19_Man_T-Shirt_Suit.png',
                fit: BoxFit.cover, // Ensures the image covers the circular area
              ),
            ),
          ),
        ),
        actions: [
          IconButton(
              onPressed: () {},
              icon: const CircleAvatar(
                  backgroundColor: ColorManager.primaryColor,
                  child: Icon(Icons.notifications))),
        ],
      ),
      body: ListView(
        padding:
            const EdgeInsets.symmetric(horizontal: AppSpaces.screenPadding),
        children: const [
          AppGaps.gap16,

          // * Home Slider
          HomeSliderWidget(),

          AppGaps.gap16,

          // * Animals
          AnimalsListWidget(),

          AppGaps.gap24,

          // * Products
          ProductsListWidget(),

          AppGaps.gap24,

          // * Doctors
          DoctorsListWidget(),

          AppGaps.gap24,
        ],
      ),
    );
  }
}
