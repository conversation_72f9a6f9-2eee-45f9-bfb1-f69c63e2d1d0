import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/container/base_container.widget.dart';
import 'package:xr_helper/xr_helper.dart';

class AnimalsListWidget extends StatelessWidget {
  const AnimalsListWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(context.tr.animals, style: AppTextStyles.title),
        AppGaps.gap8,
        SizedBox(
          height: 45.h,
          child: ListView(
            scrollDirection: Axis.horizontal,
            shrinkWrap: true,
            children: List.generate(5, (index) {
              final text = index == 0
                  ? 'Dogs'
                  : index == 1
                      ? 'Cats'
                      : index == 2
                          ? 'Birds'
                          : index == 3
                              ? 'Fish'
                              : 'Rabbits';

              final image = index == 0
                  ? 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTwyXeKDN29AmZgZPLS7n0Bepe8QmVappBwZCeA3XWEbWNdiDFB'
                  : 'https://media.4-paws.org/9/c/9/7/9c97c38666efa11b79d94619cc1db56e8c43d430/Molly_006-2829x1886-2726x1886-1920x1328.jpg';
              return BaseContainer(
                  margin: const EdgeInsets.only(right: AppSpaces.padding8),
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.padding12,
                    vertical: AppSpaces.padding8,
                  ),
                  child: Row(
                    children: [
                      image.networkImage(
                        height: 40.h,
                        width: 40.w,
                        fit: BoxFit.cover,
                      ),
                      AppGaps.gap8,
                      Text(text, style: AppTextStyles.subTitle),
                    ],
                  ));
            }),
          ),
        ),
      ],
    );
  }
}
