import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

class BaseTextField extends StatefulWidget {
  final FocusNode? focusNode;
  final TextEditingController? controller;
  final TextInputType textInputType;
  final Function(dynamic)? onChanged;
  final TextAlign textAlign;
  final Function()? onTap;
  final EdgeInsetsGeometry? contentPadding;
  final Widget? icon;
  final Widget? suffixIcon;
  final String? label;
  final String name;
  final String? hint;
  final int maxLines;
  final String? ignoringMessage;
  final String? Function(String?)? validator;
  final bool isObscure;
  final bool isRequired;
  final bool withoutEnter;
  final String? initialValue;
  final String? title;
  final bool? enabled;

  const BaseTextField({
    super.key,
    this.ignoringMessage,
    required this.name,
    this.enabled = true,
    this.focusNode,
    this.controller,
    this.isObscure = false,
    this.withoutEnter = false,
    this.onTap,
    this.hint,
    this.icon,
    this.suffixIcon,
    this.label,
    this.onChanged,
    this.initialValue,
    this.textAlign = TextAlign.start,
    this.contentPadding,
    this.textInputType = TextInputType.text,
    this.maxLines = 1,
    this.isRequired = true,
    this.validator,
    this.title,
  });

  @override
  State<BaseTextField> createState() => _BaseTextFieldState();
}

class _BaseTextFieldState extends State<BaseTextField> {
  late bool _isObscure;

  @override
  void initState() {
    super.initState();
    _isObscure = widget.isObscure;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.title != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(widget.title!, style: AppTextStyles.labelLarge),
          AppGaps.gap8,
          _buildTextField(context),
        ],
      );
    }
    return _buildTextField(context);
  }

  Widget _buildTextField(BuildContext context) {
    return FormBuilderTextField(
      decoration: InputDecoration(
        labelText: widget.label,
        hintText: widget.withoutEnter
            ? widget.hint
            : '${context.tr.enter} ${widget.hint ?? widget.title ?? widget.label}',
        hintStyle: AppTextStyles.labelMedium.copyWith(
          color: Colors.grey,
        ),
        labelStyle: AppTextStyles.labelMedium,
        prefixIcon: widget.icon != null
            ? Padding(
                padding: const EdgeInsets.all(8.0),
                child: widget.icon,
              )
            : null,
        suffixIcon: widget.textInputType == TextInputType.visiblePassword
            ? InkWell(
                onTap: () {
                  setState(() {
                    _isObscure = !_isObscure;
                  });
                },
                child: Icon(
                  _isObscure ? Icons.visibility_off : Icons.visibility,
                  color: Colors.grey,
                ),
              )
            : Padding(
                padding: const EdgeInsets.all(8.0),
                child: widget.suffixIcon,
              ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppSpaces.padding8,
          vertical: AppSpaces.padding12 + 5,
        ),
      ),
      name: widget.name,
      enableSuggestions: true,
      onTapOutside: (_) {
        FocusScope.of(context).unfocus();
      },
      focusNode: widget.focusNode,
      obscureText: widget.isObscure ? _isObscure : false,
      controller: widget.controller,
      keyboardType: widget.textInputType,
      inputFormatters: [
        if (widget.textInputType == TextInputType.number)
          FilteringTextInputFormatter.allow(RegExp(r'[0-9.-]')),
      ],
      textAlign: widget.textAlign,
      onChanged: widget.onChanged,
      enabled: widget.enabled ?? true,
      onTap: widget.onTap,
      initialValue: widget.initialValue,
      maxLines: widget.maxLines,
      validator: widget.validator,
      style: AppTextStyles.labelMedium,
    );
  }
}
