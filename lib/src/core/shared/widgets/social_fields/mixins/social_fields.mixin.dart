import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:dropx/src/core/theme/color_manager.dart';

mixin SocialMediaMixin {
  Widget getSocialIconByURL(String url) {
    if (url.contains('facebook')) {
      return const Icon(
        FontAwesomeIcons.facebook,
        color: Colors.blue,
      );
    } else if (url.contains('whatsapp') ||
        url.contains('wa.me') ||
        url.contains('api.whatsapp')) {
      return const Icon(
        FontAwesomeIcons.whatsapp,
        color: Colors.green,
      );
    } else if (url.contains('instagram')) {
      return const Icon(
        FontAwesomeIcons.instagram,
        color: Colors.pinkAccent,
      );
    } else if (url.contains('linkedin')) {
      return const Icon(
        FontAwesomeIcons.linkedin,
        color: Colors.blueAccent,
      );
    } else if (url.contains('twitter') || url.contains('x.com')) {
      return const Icon(
        FontAwesomeIcons.twitter,
        color: Colors.blue,
      );
    } else {
      return const Icon(
        FontAwesomeIcons.link,
        color: ColorManager.primaryColor,
      );
    }
  }

  void openURL(String url) {
    if (url.isNotEmpty) {}
  }
}
