import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/consts/network/api_strings.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/widgets/fields/text_field.dart';
import 'package:dropx/src/core/shared/widgets/lists/base_list.dart';
import 'package:dropx/src/core/shared/widgets/social_fields/mixins/social_fields.mixin.dart';
import 'package:dropx/src/screens/auth/models/social_model.dart';
import 'package:xr_helper/xr_helper.dart';

class SocialWidgetFields extends HookConsumerWidget with SocialMediaMixin {
  final ValueNotifier<List<SocialModel>> socialFields;
  final bool viewOnly;
  final bool localOnly;
  final bool withoutAddButton;

  const SocialWidgetFields({
    super.key,
    required this.socialFields,
    this.viewOnly = false,
    this.localOnly = false,
    this.withoutAddButton = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final socialController = useTextEditingController();

    return StatefulBuilder(builder: (context, setState) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!viewOnly) ...[
            Row(
              children: [
                Expanded(
                  child: BaseTextField(
                    name: FieldsConsts.social,
                    isRequired: false,
                    controller: socialController,
                    title: context.tr.socialMedia,
                    hint: 'https://wa.me/123456789',
                    withoutEnter: true,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: () {
                    // validate social is link
                    final isValidLink =
                        socialController.text.startsWith('http') ||
                            socialController.text.startsWith('https');

                    if (!isValidLink) {
                      showToast(context.tr.pleaseAddValidLink, isError: true);
                      return;
                    }

                    socialFields.value.add(
                      SocialModel(
                        link: socialController.text,
                      ),
                    );

                    if (!localOnly) {
                      // authController.addSocial(
                      //   socialLinks: [socialController.text],
                      // );
                    }

                    socialController.clear();

                    setState(() {});
                  },
                ),
              ],
            ),
            AppGaps.gap12,
          ],
          BaseList(
            data: socialFields.value,
            showEmptyWidget: false,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (value, index) {
              return ListTile(
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.padding8,
                  ),
                  title: Text(value.link),
                  leading: getSocialIconByURL(value.link),
                  trailing: viewOnly
                      ? null
                      : FloatingActionButton.small(
                          onPressed: () {
                            socialFields.value.removeAt(index);

                            if (value.id != null) {
                              // authController.removeSocial(socialId: value.id!);
                            }

                            setState(() {});
                          },
                          child: const Icon(
                            Icons.remove,
                            color: Colors.white,
                          )).sized(
                          height: 30.r,
                          width: 30.r,
                        ));
            },
          ),
          if (viewOnly && !withoutAddButton) ...[
            AppGaps.gap4,
            _ChangeSocialButton(
              socialController: socialController,
              socialFields: socialFields,
              setState: setState,
            ),
          ]
        ],
      );
    });
  }
}

class _ChangeSocialButton extends StatelessWidget {
  final TextEditingController socialController;
  final ValueNotifier<List<SocialModel>> socialFields;
  final Function setState;

  const _ChangeSocialButton({
    super.key,
    required this.socialController,
    required this.socialFields,
    required this.setState,
  });

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: () {
        showModalBottomSheet(
            context: context,
            builder: (context) {
              return ListView(
                children: [
                  SocialWidgetFields(
                    socialFields: socialFields,
                  ).paddingAll(AppSpaces.screenPadding),
                ],
              );
            }).then((value) => setState(() {}));
      },
      child: Text(context.tr.changeSocial),
    ).align(Alignment.centerRight);
  }
}
