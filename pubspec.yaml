name: dropx
description: "DropX Application."

publish_to: 'none'

scripts:
  build_runner: dart run build_runner build --delete-conflicting-outputs
  launch_icons: flutter pub run flutter_launcher_icons:main
  launch_splash: flutter pub run flutter_native_splash:create

version: 1.0.0+0

environment:
  sdk: '>=3.0.6 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  #? Localization
  flutter_localizations:
    sdk: flutter

  # * Helper Package *
  xr_helper:
    path: packages/xr_helper

  cupertino_icons: ^1.0.6

  #? State Management
  flutter_riverpod: ^2.4.9
  riverpod: ^2.4.9
  hooks_riverpod: ^2.4.9
  flutter_hooks: ^0.21.3+1
  equatable:

  #? responsive
  flutter_screenutil: ^5.9.3


  #? Google Fonts
  google_fonts:

  #? Assets
  lottie: ^3.0.0
  flutter_svg:
  smooth_page_indicator: ^1.2.0+3

  #? form Builder
  flutter_form_builder: ^10.2.0
  form_builder_image_picker: ^4.1.0
  form_builder_validators: ^11.0.0


  #? Utils
  no_context_navigation: ^3.0.0
  fluttertoast: ^8.2.8
  permission_handler: ^12.0.1

  #? Location
  google_maps_flutter: ^2.9.0
  geolocator: ^14.0.2

  #? Firebase
  firebase_core: ^4.1.0

  #? UI
  loading_animation_widget: ^1.3.0
  carousel_slider: ^5.0.0
  multi_video_player: ^0.0.5
  font_awesome_flutter: ^10.7.0
  flutter_staggered_grid_view: ^0.7.0
  chatview: ^2.3.0

dependency_overrides:
  archive: ^3.6.1
  win32: ^5.5.4


dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^6.0.0
  flutter_launcher_icons: ^0.14.4
  flutter_native_splash: ^2.3.9
  build_runner:
  flutter_gen_runner:

#? dart run flutter_launcher_icons:main
flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/app_icon.png"
  adaptive_icon_background: "assets/images/app_icon.png"
  adaptive_icon_foreground: "assets/images/app_icon.png"
  adaptive_icon_foreground_inset: 16

# ? dart run flutter_native_splash:create
flutter_native_splash:
  android: true
  ios: true
  web: false
  fullscreen: false
  color: '#ffffff'
  image: 'assets/images/app_icon.png'
  android_12:
    color: '#ffffff'
    image: 'assets/images/app_icon.png'

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
#    - assets/animated/
#    - assets/icons/

flutter_intl:
  enabled: true


flutter_gen:
  output: lib/generated